# ===========================================
# ASTRO WORKS INDONESIA - PRODUCTION CONFIG
# ===========================================

# ============
# CRITICAL - MUST CHANGE FOR PRODUCTION
# ============
POSTGRES_PASSWORD=your-super-secure-postgres-password-here
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long
ADMIN_PASSWORD=your-secure-admin-password-here
BANK_ACCOUNT_NUMBER=your-real-bank-account-number

# ============
# DOMAIN CONFIG
# ============
DOMAIN=astrokabinet.id
API_DOMAIN=furapi.astrokabinet.id
ACME_EMAIL=<EMAIL>

# ============
# DATABASE
# ============
DATABASE_URL=postgres://astro_user:${POSTGRES_PASSWORD}@postgres:5432/astro_ecommerce

# ============
# BACKEND CONFIG
# ============
HOST=0.0.0.0
PORT=7998
RUST_LOG=info
API_BASE_URL=https://furapi.astrokabinet.id

# Database Components (for fallback)
DB_HOST=localhost
DB_PORT=5432
DB_USER=astro_user
DB_PASSWORD=${POSTGRES_PASSWORD}
DB_NAME=astro_ecommerce

# Redis Components (for fallback)
REDIS_HOST=localhost
REDIS_PORT=6379

# Frontend Configuration
API_URL=https://${DOMAIN}/api/v1
FRONTEND_URL=https://${DOMAIN}
VITE_API_URL=${API_URL}

# Traefik Configuration
# Generate with: ./scripts/generate-traefik-auth.sh admin yourpassword
TRAEFIK_AUTH=admin:$2y$10$X2jnWkNVxHuAEzn7Jt8XxOzjqZrqQxQxQxQxQxQxQxQxQxQxQxQxQx

# ============
# ADMIN CONFIG
# ============
ADMIN_EMAIL=<EMAIL>

# ============
# COMPANY INFO
# ============
COMPANY_NAME=Astro Works Indonesia
COMPANY_EMAIL=<EMAIL>

# ============
# WHATSAPP
# ============
WHATSAPP_PHONE_NUMBER=***********
WHATSAPP_BASE_URL=https://wa.me/

# ============
# BANK INFO
# ============
BANK_NAME=BCA
BANK_ACCOUNT_NAME=Astro Works Indonesia PT

# ============
# FRONTEND CONFIG
# ============
PUBLIC_API_URL=https://furapi.astrokabinet.id/api/v1
PUBLIC_API_BASE_URL=https://furapi.astrokabinet.id
PUBLIC_FE_URL=https://astrokabinet.id
PUBLIC_COMPANY_NAME=Astro Works Indonesia
PUBLIC_COMPANY_EMAIL=<EMAIL>
PUBLIC_WHATSAPP_PHONE_NUMBER=***********
PUBLIC_ENABLE_ANALYTICS=false
PUBLIC_ENABLE_CHAT=true
PUBLIC_DEBUG_MODE=false

# ============
# TRAEFIK AUTH
# ============
# Generate with: htpasswd -nb admin yourpassword
TRAEFIK_AUTH=admin:$2y$10$X2jnWkNVxHuAEzn7Jt8XxOzjqZrqQxQxQxQxQxQxQxQxQxQxQxQxQx

# ============
# NOTES
# ============
# 1. Ganti semua password dengan nilai yang aman
# 2. JWT_SECRET harus minimal 32 karakter
# 3. Untuk production, gunakan domain yang sebenarnya
# 4. Backup akan disimpan di ./backups/
# 5. Logs akan disimpan dengan rotasi otomatis
# 6. Database menggunakan SSL dan logging enhanced
# 7. Semua service memiliki resource limits untuk VPS 1GB
BANK_NAME=BCA
BANK_ACCOUNT_NAME=Astro Works Indonesia

# CORS Configuration
CORS_ORIGIN=${FRONTEND_URL}

# File Upload Configuration
MAX_FILE_SIZE=********  # 10MB
UPLOAD_PATH=/app/uploads

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Cache Configuration
CACHE_TTL_SECONDS=300  # 5 minutes
CACHE_MAX_SIZE=100MB

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Security Configuration
ENABLE_HTTPS_REDIRECT=true
ENABLE_SECURITY_HEADERS=true
ENABLE_RATE_LIMITING=true

# Development Configuration (for .env.dev)
# DEV_MODE=true
# DEBUG=true
# HOT_RELOAD=true
