# =============================================================================
# VPS PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
FE_URL=https://astrokabinet.id
API_URL=https://furapi.astrokabinet.id/api/v1
API_DOMAIN=furapi.astrokabinet.id
API_BASE_URL=https://furapi.astrokabinet.id

SSL_ENABLED=true
ACME_EMAIL=<EMAIL>

# CORS Configuration
CORS_ORIGIN=https://astrokabinet.id,https://www.astrokabinet.id,https://furapi.astrokabinet.id

# =============================================================================
# BACKEND CONFIGURATION
# =============================================================================
BACKEND_HOST=0.0.0.0
BACKEND_PORT=7998
HOST=0.0.0.0
PORT=7998
RUST_LOG=info
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-minimum-32-characters-long

# =============================================================================
# TRAEFIK CONFIGURATION
# =============================================================================
TRAEFIK_DASHBOARD_PORT=8080
TRAEFIK_API_INSECURE=false
TRAEFIK_AUTH=admin:$2y$10$X2jnWkNVxHuAEzn7Jt8XxOzjqZrqQxQxQxQxQxQxQxQxQxQxQx

# Database Configuration
POSTGRES_USER=astro_user
POSTGRES_PASSWORD=astro_secure_password_2024
POSTGRES_DB=astro_ecommerce
DATABASE_URL=**************************************************************/astro_ecommerce

# Database Components (for fallback)
DB_HOST=postgres
DB_PORT=5432
DB_USER=astro_user
DB_PASSWORD=astro_secure_password_2024
DB_NAME=astro_ecommerce

# Redis Configuration
REDIS_URL=redis://redis:6379

# Redis Components (for fallback)
REDIS_HOST=redis
REDIS_PORT=6379

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-for-production-min-32-chars
ADMIN_DEFAULT_EMAIL=<EMAIL>
ADMIN_DEFAULT_PASSWORD=Astrokabinet25!

# Application Configuration
RUST_LOG=info
ENVIRONMENT=production

# Business Configuration
WHATSAPP_PHONE_NUMBER=***********
WHATSAPP_BASE_URL=https://wa.me/
BANK_NAME=BCA
BANK_ACCOUNT_NUMBER=**********
BANK_ACCOUNT_NAME=Astro Works Indonesia PT
COMPANY_NAME=Astro Works Indonesia
COMPANY_EMAIL=<EMAIL>
COMPANY_ADDRESS=Jakarta, Indonesia

# SSL Configuration
ACME_EMAIL=<EMAIL>

# =============================================================================
# FILE UPLOAD & STORAGE
# =============================================================================
MAX_FILE_SIZE=********
UPLOAD_PATH=/app/uploads
STATIC_PATH=/app/static

# =============================================================================
# MONITORING & PERFORMANCE
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=100MB

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=/app/backups
