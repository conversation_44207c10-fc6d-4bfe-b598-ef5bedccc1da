# Rust
/target/
**/*.rs.bk
Cargo.lock

# Environment files
.env.local
.env.production
.env.prod
.env.vps.secure
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Node.js (for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
.svelte-kit/

# Docker
.dockerignore

# Backup files
*.bak
*.backup
*.old

# Temporary files
tmp/
temp/
.tmp/

# SQLx
sqlx-data.json

# Static uploads
/static/uploads/
/be_astro/static/uploads/

# Data directories
/data/
/be_astro/data/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Deployment
.env.vps.secure
docker-context-*
