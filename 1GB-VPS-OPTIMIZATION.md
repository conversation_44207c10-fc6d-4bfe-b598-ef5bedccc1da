# 🔧 Optimasi untuk VPS 1GB RAM + 1 CPU

Konfigurasi khusus untuk VPS dengan resource sangat terbatas.

## 📊 Resource Allocation Summary

### Total VPS Resources
- **RAM**: 1GB (1024MB)
- **CPU**: 1 Core (100%)

### Optimized Resource Distribution

| Service | CPU Limit | CPU Reserve | RAM Limit | RAM Reserve | % of Total RAM |
|---------|-----------|-------------|-----------|-------------|----------------|
| **Backend** | 50% | 10% | 512MB | 256MB | 50% |
| **PostgreSQL** | 30% | 5% | 256MB | 128MB | 25% |
| **Redis** | 15% | 5% | 128MB | 64MB | 12.5% |
| **System/OS** | 5% | - | 128MB | - | 12.5% |
| **TOTAL** | **100%** | **20%** | **1024MB** | **448MB** | **100%** |

## 🔧 Optimizations Applied

### 1. PostgreSQL Optimizations
```sql
max_connections = 50          # Reduced from 200
shared_buffers = 64MB         # Reduced from 256MB
effective_cache_size = 256MB  # Reduced from 1GB
maintenance_work_mem = 16MB   # Reduced from 64MB
work_mem = 2MB               # Added limit
temp_buffers = 8MB           # Added limit
```

### 2. Redis Optimizations
```redis
maxmemory 96mb               # Reduced from 256mb
maxmemory-policy allkeys-lru # Memory eviction policy
appendonly no                # Disabled for memory saving
save ""                      # Disabled background saves
```

### 3. Rust Build Optimizations
```dockerfile
CARGO_BUILD_JOBS=1           # Single job for 1 CPU
RUSTFLAGS="-C opt-level=s"   # Size optimization over speed
cargo build --jobs 1         # Single threaded compilation
```

### 4. Docker Resource Limits
```yaml
# Strict limits to prevent OOM
backend:
  cpus: '0.5'
  memory: 512M
postgres:
  cpus: '0.3' 
  memory: 256M
redis:
  cpus: '0.15'
  memory: 128M
```

## 🚀 Deployment Strategies

### 1. Recommended: 1GB VPS Optimized Deployment
```bash
make deploy-1gb
```
**Features:**
- Sequential service deployment
- Resource monitoring
- Automatic cleanup
- Health verification

### 2. Alternative: Binary Deployment
```bash
make deploy-binary
```
**Features:**
- Build locally, deploy binary only
- No VPS compilation
- Minimal resource usage

### 3. Emergency: Simple Deployment
```bash
make deploy-prod-simple
```
**Use only if other methods fail**

## 📈 Monitoring & Alerts

### Memory Thresholds (Stricter for 1GB VPS)
- **Warning**: >70% memory usage (instead of 85%)
- **Critical**: >80% memory usage (instead of 90%)
- **Emergency**: >90% memory usage (OOM risk)

### CPU Thresholds
- **Normal**: <60% CPU usage
- **Warning**: >70% CPU usage
- **Critical**: >85% CPU usage

## 🛠️ Troubleshooting 1GB VPS Issues

### Common Problems

#### 1. Out of Memory (OOM)
**Symptoms:**
- Containers getting killed
- SSH disconnections
- Slow response times

**Solutions:**
```bash
# Check memory usage
docker --context astro-vps stats

# Restart services one by one
docker --context astro-vps restart astro_postgres_prod
docker --context astro-vps restart astro_redis_prod
docker --context astro-vps restart astro_backend_prod

# Emergency cleanup
docker --context astro-vps system prune -f
```

#### 2. High CPU Usage
**Symptoms:**
- Slow compilation
- Timeouts during deployment
- High load average

**Solutions:**
```bash
# Use binary deployment instead
make deploy-binary

# Check CPU usage
docker --context astro-vps exec astro_backend_prod top

# Reduce concurrent connections
# (Already optimized in PostgreSQL config)
```

#### 3. Disk Space Issues
**Symptoms:**
- Build failures
- Container start failures

**Solutions:**
```bash
# Clean Docker resources
docker --context astro-vps system prune -a -f

# Remove old images
docker --context astro-vps image prune -a -f

# Check disk usage
docker --context astro-vps exec astro_backend_prod df -h
```

## 🔍 Performance Expectations

### Expected Performance on 1GB VPS
- **Startup Time**: 2-3 minutes
- **Response Time**: 200-500ms (API)
- **Concurrent Users**: 10-20 users
- **Database Connections**: Max 50
- **Memory Usage**: 70-85% normal

### Limitations
- **No heavy background tasks**
- **Limited file uploads** (max 10MB)
- **Basic caching only**
- **Single instance deployment**

## 📋 Maintenance Tasks

### Daily
```bash
# Check resource usage
make logs | grep -E "(memory|cpu|error)"

# Verify health
curl https://api.astrokabinet.id/health
```

### Weekly
```bash
# Clean up Docker resources
docker --context astro-vps system prune -f

# Check logs for errors
make logs | tail -100
```

### Monthly
```bash
# Full system cleanup
docker --context astro-vps system prune -a -f

# Update containers
make deploy-1gb
```

## 🚨 Emergency Procedures

### If VPS Becomes Unresponsive
1. **Restart VPS** from control panel
2. **Wait 5 minutes** for full boot
3. **Redeploy** with optimized settings:
   ```bash
   make deploy-1gb
   ```

### If Memory Issues Persist
1. **Enable swap** (if not already):
   ```bash
   # On VPS (if accessible)
   sudo fallocate -l 1G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

2. **Use binary deployment only**:
   ```bash
   make deploy-binary
   ```

## 🎯 Success Metrics for 1GB VPS

### Healthy State Indicators
- ✅ Memory usage: 70-80%
- ✅ CPU usage: <60% average
- ✅ All containers running
- ✅ API response time: <500ms
- ✅ Database connections: <30

### Warning State Indicators
- ⚠️ Memory usage: 80-85%
- ⚠️ CPU usage: 60-80%
- ⚠️ API response time: 500-1000ms
- ⚠️ Database connections: 30-40

### Critical State Indicators
- 🚨 Memory usage: >85%
- 🚨 CPU usage: >80%
- 🚨 API response time: >1000ms
- 🚨 Database connections: >40
- 🚨 Container restarts

---

**Note**: Konfigurasi ini dirancang khusus untuk VPS 1GB RAM + 1 CPU. Untuk VPS dengan spek lebih tinggi, gunakan konfigurasi standard di `RESOURCE-MANAGEMENT.md`.
