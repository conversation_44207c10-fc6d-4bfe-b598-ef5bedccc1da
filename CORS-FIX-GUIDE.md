# CORS Fix Guide - Frontend ke Backend API

## 🔍 **Ma<PERSON>ah yang <PERSON>**

Frontend masih mengarah ke `localhost:8000` padahal backend sudah di-deploy ke `api.astrokabinet.id`, menyebabkan CORS error:

```
Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at http://localhost:8000/api/v1/auth/login
```

## ✅ **Solusi yang Telah Diterapkan**

### 1. **Fixed Hardcoded API URL di Frontend**
- ✅ Updated `fe_astro/src/lib/api/products.ts` untuk menggunakan config
- ✅ Mengganti `const API_BASE = 'http://localhost:8000/api/v1'` dengan `const API_BASE = config.api.url`

### 2. **Created Production Environment File**
- ✅ Dibuat `fe_astro/.env.production` dengan konfigurasi production:
```bash
VITE_API_BASE_URL=https://api.astrokabinet.id
VITE_API_URL=https://api.astrokabinet.id/api/v1
```

### 3. **Updated CORS Configuration**
- ✅ Backend CORS di `.env.vps` sudah include localhost untuk development
- ✅ Traefik CORS middleware sudah dikonfigurasi dengan benar

### 4. **Created Deployment Scripts**
- ✅ `scripts/deploy-frontend.sh` untuk deploy frontend dengan environment yang benar
- ✅ Makefile targets: `make deploy-frontend` dan `make deploy-full`

## 🚀 **Cara Deploy yang Benar**

### **Untuk Development (localhost)**
```bash
# Frontend tetap menggunakan .env (localhost)
cd fe_astro
npm run dev

# Backend bisa local atau VPS
```

### **Untuk Production**
```bash
# 1. Deploy backend ke VPS
make deploy-vps

# 2. Deploy frontend ke Cloudflare Workers
make deploy-frontend

# Atau deploy keduanya sekaligus
make deploy-full
```

## 🔧 **Environment Configuration**

### **Development (.env)**
```bash
VITE_API_URL=http://localhost:8000/api/v1
```

### **Production (.env.production)**
```bash
VITE_API_URL=https://api.astrokabinet.id/api/v1
```

### **Wrangler (Cloudflare Workers)**
```toml
[env.production.vars]
VITE_API_URL = "https://api.astrokabinet.id/api/v1"
```

## 🌐 **CORS Configuration**

### **Backend (.env.vps)**
```bash
CORS_ORIGIN=https://astrokabinet.id,https://www.astrokabinet.id,https://api.astrokabinet.id,http://localhost:5173,http://localhost:5174
```

### **Traefik (docker-compose.vps-traefik.yml)**
```yaml
- "traefik.http.middlewares.api-cors.headers.accesscontrolalloworiginlist=${CORS_ORIGIN}"
```

## 🔍 **Testing CORS Fix**

### **1. Test API Endpoint Langsung**
```bash
curl -X POST https://api.astrokabinet.id/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "Origin: https://astrokabinet.id" \
  -d '{"email": "<EMAIL>", "password": "SecureAdmin123!"}'
```

### **2. Test dari Browser Console**
```javascript
fetch('https://api.astrokabinet.id/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'SecureAdmin123!'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

### **3. Check CORS Headers**
```bash
curl -I -X OPTIONS https://api.astrokabinet.id/api/v1/auth/login \
  -H "Origin: https://astrokabinet.id" \
  -H "Access-Control-Request-Method: POST"
```

## 🐛 **Troubleshooting**

### **Jika Masih Ada CORS Error:**

1. **Check Environment Variables**
```bash
# Di browser console
console.log(window.location.origin);
console.log('API URL:', config.api.url);
```

2. **Check Backend CORS**
```bash
make vps-logs | grep -i cors
```

3. **Check Traefik Configuration**
```bash
make vps-traefik
```

4. **Verify DNS Resolution**
```bash
nslookup api.astrokabinet.id
```

### **Jika Frontend Masih ke Localhost:**

1. **Clear Browser Cache**
2. **Check .env.local file**
3. **Restart development server**
4. **Verify Wrangler environment**

## 📝 **Checklist Deployment**

- [ ] DNS record `api.astrokabinet.id` sudah pointing ke VPS
- [ ] Backend deployed dengan `make deploy-vps`
- [ ] SSL certificate aktif di `https://api.astrokabinet.id`
- [ ] CORS headers configured dengan benar
- [ ] Frontend environment menggunakan production config
- [ ] Frontend deployed dengan `make deploy-frontend`
- [ ] Test login dari `https://astrokabinet.id`

## 🎯 **Expected Result**

Setelah fix ini:
- ✅ Frontend di `https://astrokabinet.id` bisa akses API di `https://api.astrokabinet.id`
- ✅ No more CORS errors
- ✅ Login functionality works
- ✅ All API calls use HTTPS dengan domain yang benar

## 📞 **Support**

Jika masih ada masalah:
1. Check browser console untuk error messages
2. Verify API endpoint dengan curl
3. Check backend logs: `make vps-logs`
4. Verify CORS headers dengan browser dev tools
