# 🚀 Astro Works Indonesia - Production Deployment Guide

## Overview

Panduan lengkap untuk deployment production Astro Works Indonesia dengan Docker Compose yang telah dioptimasi untuk VPS 1GB RAM.

## 🏗️ Arsitektur Deployment

```
┌─────────────────────────────────────────────────────────────┐
│                    Production Environment                    │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Port 3000)     │  Backend API (Port 7998)       │
│  - <PERSON>velte + <PERSON>inx         │  - Rust + Axum                 │
│  - Static files           │  - Image optimization          │
│  - Responsive design      │  - JWT authentication          │
├─────────────────────────────────────────────────────────────┤
│  Database (Port 5432)     │  Redis Cache (Port 6379)       │
│  - PostgreSQL 15          │  - Redis 7 Alpine              │
│  - SSL enabled            │  - Memory optimized             │
│  - Auto backup            │  - LRU eviction                 │
├─────────────────────────────────────────────────────────────┤
│  Monitoring (Port 9090)   │  Backup Service                 │
│  - Prometheus metrics     │  - Daily auto backup            │
│  - Resource monitoring    │  - 7-day retention              │
│  - Health checks          │  - Manual backup support        │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Minimum 1GB (recommended 2GB)
- **Storage**: Minimum 20GB SSD
- **CPU**: 1 vCPU minimum
- **Network**: Public IP dengan port 80, 443, 7998, 3000 terbuka

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- curl
- Basic Linux administration knowledge

## 🚀 Quick Start

### 1. Clone Repository
```bash
git clone https://github.com/your-repo/astro-works.git
cd astro-works
```

### 2. Initialize Environment
```bash
# Make scripts executable
chmod +x scripts/*.sh

# Run initialization
./scripts/init.sh
```

### 3. Configure Environment
```bash
# Edit environment variables
nano .env

# CRITICAL: Change these values!
# - POSTGRES_PASSWORD
# - JWT_SECRET (minimum 32 characters)
# - ADMIN_PASSWORD
# - BANK_ACCOUNT_NUMBER
```

### 4. Deploy
```bash
# Deploy all services
./scripts/deploy.sh
```

### 5. Verify Deployment
```bash
# Check system status
./scripts/monitor.sh

# Access services
# Frontend: http://your-server:3000
# Backend API: http://your-server:7998
# Prometheus: http://your-server:9090
```

## 📋 Environment Configuration

### Critical Variables (MUST CHANGE)
```bash
# Database security
POSTGRES_PASSWORD=your-super-secure-postgres-password-here

# Application security
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long
ADMIN_PASSWORD=your-secure-admin-password-here

# Business configuration
BANK_ACCOUNT_NUMBER=your-real-bank-account-number
```

### Optional Variables
```bash
# Company information
COMPANY_NAME=Astro Works Indonesia
COMPANY_EMAIL=<EMAIL>
WHATSAPP_PHONE_NUMBER=***********

# Domain configuration (for production)
API_BASE_URL=https://furapi.astrokabinet.id
PUBLIC_FE_URL=https://astrokabinet.id
```

## 🛠️ Management Scripts

### Daily Operations
```bash
# System monitoring
./scripts/monitor.sh

# Manual backup
./scripts/backup.sh

# View logs
./scripts/maintenance.sh logs

# Restart service
./scripts/maintenance.sh restart
```

### Maintenance Operations
```bash
# System cleanup
./scripts/maintenance.sh cleanup

# Update services
./scripts/maintenance.sh update

# Restore from backup
./scripts/maintenance.sh restore

# Detailed status
./scripts/maintenance.sh status
```

## 📊 Monitoring & Health Checks

### Service Health Endpoints
- **Backend**: `http://localhost:7998/health`
- **Frontend**: `http://localhost:3000/`
- **Database**: `pg_isready` check
- **Redis**: `PING` command
- **Prometheus**: `http://localhost:9090`

### Resource Limits (Optimized for 1GB VPS)
```yaml
Backend:    512MB RAM, 1.0 CPU
Frontend:   256MB RAM, 0.5 CPU
Database:   256MB RAM, 0.5 CPU
Redis:      64MB RAM,  0.25 CPU
Backup:     128MB RAM, 0.25 CPU
Prometheus: 128MB RAM, 0.25 CPU
```

### Log Management
- **Rotation**: 10MB max size, 3 files retained
- **Location**: `./logs/` directory
- **Access**: `docker-compose logs -f`

## 💾 Backup Strategy

### Automatic Backup
- **Frequency**: Daily at midnight
- **Retention**: 7 days
- **Components**: Database, static files, configuration
- **Location**: `./backups/` directory

### Manual Backup
```bash
# Create immediate backup
./scripts/backup.sh --type manual

# Custom retention
./scripts/backup.sh --retention 14
```

### Restore Process
```bash
# List available backups
ls -la backups/

# Restore from specific backup
./scripts/maintenance.sh restore
```

## 🔒 Security Features

### Database Security
- SSL connections enabled
- Non-root user execution
- Connection logging
- Password-protected access

### Application Security
- JWT token authentication
- CORS protection
- Rate limiting ready
- No-new-privileges containers

### Network Security
- Isolated internal networks
- Minimal port exposure
- Security-optimized containers

## 🚨 Troubleshooting

### Common Issues

#### Services Won't Start
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs

# Check resource usage
docker stats

# Restart specific service
./scripts/maintenance.sh restart <service-name>
```

#### Database Connection Issues
```bash
# Check database status
docker-compose -f docker-compose.prod.yml exec postgres pg_isready -U astro_user

# Reset database connection
docker-compose -f docker-compose.prod.yml restart postgres
```

#### High Memory Usage
```bash
# Check container stats
docker stats --no-stream

# Clean up resources
./scripts/maintenance.sh cleanup

# Restart services
./scripts/maintenance.sh update
```

#### Backup Failures
```bash
# Check backup service logs
docker-compose -f docker-compose.prod.yml logs backup

# Manual backup test
./scripts/backup.sh --type manual

# Check disk space
df -h
```

### Emergency Procedures

#### Complete System Reset
```bash
# Stop all services
docker-compose -f docker-compose.prod.yml down

# Clean everything (DANGER: Data loss!)
docker system prune -a --volumes

# Redeploy
./scripts/deploy.sh
```

#### Database Recovery
```bash
# Stop backend
docker-compose -f docker-compose.prod.yml stop astro-backend

# Restore from backup
./scripts/maintenance.sh restore

# Restart backend
docker-compose -f docker-compose.prod.yml start astro-backend
```

## 📈 Performance Optimization

### For 1GB VPS
- Resource limits configured for optimal performance
- Aggressive memory management
- Log rotation to prevent disk filling
- Efficient caching strategies

### Scaling Recommendations
- **2GB RAM**: Increase backend to 1GB, database to 512MB
- **4GB RAM**: Add load balancer, increase all limits by 2x
- **8GB RAM**: Consider multi-instance deployment

## 🔄 Updates & Maintenance

### Regular Maintenance Schedule
- **Daily**: Monitor system health
- **Weekly**: Review logs and performance
- **Monthly**: Update base images and security patches
- **Quarterly**: Full system backup and disaster recovery test

### Update Process
```bash
# Pull latest changes
git pull origin main

# Update and restart
./scripts/maintenance.sh update

# Verify deployment
./scripts/monitor.sh
```

## 📞 Support

### Log Collection for Support
```bash
# Export all logs
./scripts/maintenance.sh logs

# System status report
./scripts/monitor.sh > system-status.txt

# Backup current state
./scripts/backup.sh --type support
```

### Contact Information
- **Technical Support**: <EMAIL>
- **Emergency**: WhatsApp +***********
- **Documentation**: [GitHub Repository]

---

**⚠️ Important**: Always test deployment in staging environment before production deployment!
