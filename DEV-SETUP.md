# Astro Works - Development Setup Guide

## Quick Start

### Prerequisites
- Docker & Docker Compose
- Rust & Cargo
- Bun (recommended) or Node.js & npm

### Option 1: Automated Setup (Recommended)

```bash
# Start databases and get ready
./start-dev.sh

# Run both servers simultaneously
./run-servers.sh
```

### Option 2: Manual Setup

```bash
# 1. Start databases
docker-compose -f docker-compose.dev.yml up -d postgres redis

# 2. Copy environment file
cp .env.dev .env

# 3. Install frontend dependencies (if needed)
cd fe_astro && bun install && cd ..

# 4. Start backend (Terminal 1)
cd be_astro
PORT=7998 cargo run --bin be_astro

# 5. Start frontend (Terminal 2)
cd fe_astro
bun run dev
```

### Option 3: Using Makefile

```bash
# Start databases only
make dev

# Start backend on port 7998
make start-backend

# Start frontend with bun
make start-frontend

# Start both automatically (experimental)
make dev-local
```

## URLs

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:7998/api/v1
- **Health Check**: http://localhost:7998/health
- **Database**: localhost:5432
- **Redis**: localhost:6379

## Database Setup

The development environment automatically:
- Creates PostgreSQL database with sample data
- Sets up Redis cache
- Runs migrations on backend startup

### Manual Database Operations

```bash
# Reset database
make db-reset

# Run migrations only
make migrate

# Load sample data
make seed

# Backup database
make backup

# Restore from backup
make restore BACKUP_FILE=filename.sql
```

## Configuration

### Environment Variables

Development configuration is in `.env.dev`. Key settings:

- `PORT=7998` - Backend API port
- `DATABASE_URL` - PostgreSQL connection
- `REDIS_URL` - Redis connection
- `CORS_ORIGIN` - Frontend URLs for CORS

### Frontend Configuration

Frontend uses Vite with default port 5173. Configuration in:
- `fe_astro/vite.config.ts`
- `fe_astro/package.json`

## Troubleshooting

### Port Conflicts
If port 7998 is in use:
```bash
PORT=8000 cargo run --bin be_astro
```

### Database Connection Issues
```bash
# Check if PostgreSQL is running
docker-compose -f docker-compose.dev.yml ps postgres

# View database logs
docker-compose -f docker-compose.dev.yml logs postgres
```

### Frontend Issues
```bash
# Clear node_modules and reinstall
cd fe_astro
rm -rf node_modules
bun install  # or npm install
```

### Backend Compilation Issues
```bash
cd be_astro
cargo clean
cargo build
```

## Development Workflow

1. **Start Environment**: `./start-dev.sh`
2. **Run Servers**: `./run-servers.sh` or manually in separate terminals
3. **Make Changes**: Edit code in `be_astro/` or `fe_astro/`
4. **Test**: Both servers support hot reload
5. **Stop**: Ctrl+C in terminals or `docker-compose down`

## Testing

```bash
# Run all tests
make test

# API tests only
make test-api

# Database verification
make test-db
```

## Admin Access

- **Email**: <EMAIL>
- **Password**: admin123
- **Admin Routes**: `/manajemen/*` (frontend)

## Package Managers

The setup automatically detects and uses:
- **Bun** (preferred) - faster installation and runtime
- **npm** (fallback) - if bun is not available

## Next Steps

After successful setup:
1. Visit http://localhost:5173 for the frontend
2. Test API at http://localhost:7998/health
3. Access admin panel at http://localhost:5173/manajemen
4. Check database with your preferred PostgreSQL client
