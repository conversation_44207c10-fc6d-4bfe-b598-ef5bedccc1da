# DNS Setup Guide untuk api.astrokabinet.id

## 🎯 Overview
Panduan ini menjelaskan cara setup DNS untuk domain `api.astrokabinet.id` agar mengarah ke VPS Anda.

## 📋 Prerequisites
- Domain `astrokabinet.id` sudah dimiliki
- Akses ke DNS management (Cloudflare, Namecheap, dll)
- VPS IP: `*************`

## 🔧 DNS Configuration

### 1. A Record untuk API Subdomain
Tambahkan A record berikut di DNS provider Anda:

```
Type: A
Name: api
Value: *************
TTL: 300 (5 minutes) atau Auto
```

### 2. Optional: CNAME untuk Traefik Dashboard
```
Type: CNAME
Name: traefik.api
Value: api.astrokabinet.id
TTL: 300
```

## 🌐 DNS Provider Specific Instructions

### Cloudflare
1. Login ke Cloudflare Dashboard
2. Pilih domain `astrokabinet.id`
3. Go to DNS > Records
4. Click "Add record"
5. Set:
   - Type: A
   - Name: api
   - IPv4 address: *************
   - Proxy status: DNS only (gray cloud)
   - TTL: Auto
6. Save

### Namecheap
1. Login ke Namecheap
2. Go to Domain List
3. Click "Manage" next to astrokabinet.id
4. Go to Advanced DNS
5. Add New Record:
   - Type: A Record
   - Host: api
   - Value: *************
   - TTL: 5 min
6. Save

### GoDaddy
1. Login ke GoDaddy
2. Go to My Products > DNS
3. Select astrokabinet.id
4. Add Record:
   - Type: A
   - Name: api
   - Value: *************
   - TTL: 600 seconds
5. Save

## ✅ Verification

### 1. DNS Propagation Check
```bash
# Check DNS resolution
nslookup api.astrokabinet.id

# Should return:
# Name: api.astrokabinet.id
# Address: *************
```

### 2. Online Tools
- https://dnschecker.org/
- https://www.whatsmydns.net/
- Search for: `api.astrokabinet.id`

### 3. Test with dig
```bash
dig api.astrokabinet.id

# Should show:
# api.astrokabinet.id. 300 IN A *************
```

## ⏱️ Propagation Time
- **Local DNS**: 5-15 minutes
- **Global DNS**: 24-48 hours (worst case)
- **Cloudflare**: Usually 2-5 minutes
- **Other providers**: 15 minutes - 2 hours

## 🚀 After DNS Setup

### 1. Deploy with Traefik
```bash
# Deploy backend with Traefik
make deploy-vps

# This will:
# - Setup Traefik reverse proxy
# - Configure Let's Encrypt SSL
# - Map api.astrokabinet.id to backend
```

### 2. Test SSL Certificate
```bash
# Test HTTPS endpoint
curl https://api.astrokabinet.id/health

# Should return:
# {"status":"ok","timestamp":"..."}
```

### 3. Verify Traefik Dashboard
```bash
# Access dashboard (if configured)
https://traefik.api.astrokabinet.id
```

## 🔒 SSL Certificate
Traefik akan otomatis:
- Request SSL certificate dari Let's Encrypt
- Configure HTTPS redirect
- Renew certificate sebelum expired

## 🐛 Troubleshooting

### DNS Not Resolving
```bash
# Check if DNS record exists
dig api.astrokabinet.id

# If no result, check:
# 1. DNS record spelling
# 2. TTL settings
# 3. Wait for propagation
```

### SSL Certificate Issues
```bash
# Check Traefik logs
make vps-logs

# Look for:
# - ACME challenge errors
# - Certificate generation logs
# - Domain validation issues
```

### Connection Refused
```bash
# Check if services are running
make vps-status

# Check firewall
ssh servomo@************* 'sudo ufw status'

# Should show:
# 80/tcp    ALLOW
# 443/tcp   ALLOW
```

## 📝 DNS Record Summary

Setelah setup, DNS records Anda akan terlihat seperti:

```
astrokabinet.id          A     *************  (main domain)
api.astrokabinet.id      A     *************  (API subdomain)
traefik.api.astrokabinet.id CNAME api.astrokabinet.id (optional)
```

## 🎯 Final Test

Setelah DNS propagation selesai:

```bash
# Test API endpoints
curl https://api.astrokabinet.id/health
curl https://api.astrokabinet.id/api/v1/categories

# Run full verification
make verify-vps
```

## 📞 Support

Jika ada masalah:
1. Cek DNS propagation dengan online tools
2. Verify DNS records di provider
3. Check Traefik logs: `make vps-logs`
4. Test internal connectivity: `ssh servomo@************* 'curl localhost:7998/health'`
