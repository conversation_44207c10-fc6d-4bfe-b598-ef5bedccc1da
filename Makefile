# Astro Works E-commerce Platform Makefile
# ========================================

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
NC := \033[0m # No Color

# Project configuration
PROJECT_NAME := astro-works
DOMAIN := astro-works.local
POSTGRES_PASSWORD := astro_password_prod
JWT_SECRET := your-super-secret-jwt-key-change-this-in-production
TRAEFIK_AUTH := admin:$$2y$$10$$X2jnWkNVxHuAEzn7Jt8XxOzjqZrqQxQxQxQxQxQxQxQxQxQxQxQxQx

# Docker compose files
COMPOSE_DEV := docker-compose.yml
COMPOSE_PROD := docker-compose.prod.yml

# Default target
.DEFAULT_GOAL := help

# Detect OS
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
    OS := linux
endif
ifeq ($(UNAME_S),Darwin)
    OS := macos
endif
ifdef OS
    DETECTED_OS := $(OS)
else
    DETECTED_OS := windows
endif

##@ Help
.PHONY: help
help: ## Display this help message
	@echo "$(CYAN)Astro Works E-commerce Platform$(NC)"
	@echo "$(CYAN)================================$(NC)"
	@echo ""
	@echo "$(YELLOW)Detected OS: $(DETECTED_OS)$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make $(CYAN)<target>$(NC)\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  $(CYAN)%-15s$(NC) %s\n", $$1, $$2 } /^##@/ { printf "\n$(PURPLE)%s$(NC)\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Environment Setup
.PHONY: setup-env
setup-env: ## Setup environment files interactively
	@echo "$(BLUE)[INFO]$(NC) Setting up environment configuration..."
	@./scripts/development/setup-env.sh dev
	@echo "$(GREEN)[SUCCESS]$(NC) Environment configuration completed"

.PHONY: setup-env-prod
setup-env-prod: ## Setup production environment files
	@echo "$(BLUE)[INFO]$(NC) Setting up production environment..."
	@./scripts/development/setup-env.sh prod
	@echo "$(GREEN)[SUCCESS]$(NC) Production environment setup completed"

.PHONY: validate-env
validate-env: ## Validate environment configuration
	@echo "$(BLUE)[INFO]$(NC) Validating environment configuration..."
	@./scripts/development/validate-env.sh

.PHONY: update-env
update-env: ## Update environment configuration interactively
	@echo "$(BLUE)[INFO]$(NC) Updating environment configuration..."
	@./scripts/development/setup-env.sh update

.PHONY: wrangler-setup
wrangler-setup: ## Setup Wrangler for frontend environment management
	@echo "$(BLUE)[INFO]$(NC) Setting up Wrangler for frontend..."
	@cd fe_astro && npm install
	@echo "$(GREEN)[SUCCESS]$(NC) Wrangler setup completed"

.PHONY: wrangler-validate
wrangler-validate: ## Validate Wrangler environment configuration
	@echo "$(BLUE)[INFO]$(NC) Validating Wrangler environment configuration..."
	@cd fe_astro && npm run env:validate

.PHONY: wrangler-sync
wrangler-sync: ## Sync .env to Wrangler development environment
	@echo "$(BLUE)[INFO]$(NC) Syncing environment variables to Wrangler..."
	@cd fe_astro && npm run env:sync

.PHONY: wrangler-manage
wrangler-manage: ## Interactive Wrangler environment management
	@echo "$(BLUE)[INFO]$(NC) Starting Wrangler environment manager..."
	@cd fe_astro && npm run env:manage

.PHONY: setup-domain
setup-domain: ## Setup custom domain for astrokabinet.id
	@echo "$(BLUE)[INFO]$(NC) Setting up custom domain..."
	@cd fe_astro && ./scripts/setup-custom-domain.sh setup
	@echo "$(GREEN)[SUCCESS]$(NC) Custom domain setup completed"

.PHONY: deploy-production
deploy-production: ## Deploy to production with custom domain
	@echo "$(BLUE)[INFO]$(NC) Deploying to production..."
	@cd fe_astro && ./scripts/setup-custom-domain.sh deploy
	@echo "$(GREEN)[SUCCESS]$(NC) Production deployment completed"

.PHONY: verify-domain
verify-domain: ## Verify custom domain setup
	@echo "$(BLUE)[INFO]$(NC) Verifying domain setup..."
	@cd fe_astro && ./scripts/setup-custom-domain.sh verify

##@ VPS Deployment
.PHONY: setup-vps
setup-vps: ## Setup Docker context for VPS deployment
	@echo "$(BLUE)[INFO]$(NC) Setting up Docker context for VPS..."
	@./scripts/deployment/setup-docker-context.sh
	@echo "$(GREEN)[SUCCESS]$(NC) VPS Docker context setup completed"

.PHONY: deploy-vps
deploy-vps: ## Deploy backend to VPS using Docker context
	@echo "$(BLUE)[INFO]$(NC) Deploying to VPS..."
	@./scripts/deployment/deploy-vps.sh
	@echo "$(GREEN)[SUCCESS]$(NC) VPS deployment completed"

.PHONY: fast-deploy-vps
fast-deploy-vps: ## Fast optimized deployment to VPS
	@echo "$(BLUE)[INFO]$(NC) Fast deploying to VPS..."
	@./scripts/deployment/fast-build-vps.sh
	@echo "$(GREEN)[SUCCESS]$(NC) Fast VPS deployment completed"

.PHONY: status-vps
status-vps: ## Check VPS deployment status
	@echo "$(BLUE)[INFO]$(NC) Checking VPS status..."
	@./scripts/deployment/deploy-vps.sh --status

.PHONY: logs-vps
logs-vps: ## View VPS backend logs
	@echo "$(BLUE)[INFO]$(NC) Viewing VPS logs..."
	@./scripts/deployment/deploy-vps.sh --logs

.PHONY: rollback-vps
rollback-vps: ## Rollback VPS deployment
	@echo "$(YELLOW)[WARNING]$(NC) Rolling back VPS deployment..."
	@./scripts/deployment/deploy-vps.sh --rollback
	@echo "$(GREEN)[SUCCESS]$(NC) VPS rollback completed"

.PHONY: ssh-vps
ssh-vps: ## SSH into VPS
	@echo "$(BLUE)[INFO]$(NC) Connecting to VPS..."
	@ssh astro@**************

.PHONY: monitor-vps
monitor-vps: ## Monitor VPS deployment status
	@echo "$(BLUE)[INFO]$(NC) Monitoring VPS status..."
	@./scripts/deployment/monitor-vps.sh

.PHONY: health-vps
health-vps: ## Check VPS service health
	@echo "$(BLUE)[INFO]$(NC) Checking VPS health..."
	@./scripts/deployment/monitor-vps.sh --health

.PHONY: deploy-prod
deploy-prod: ## Deploy to production with resource monitoring
	@echo "$(BLUE)[INFO]$(NC) Deploying to production with resource monitoring..."
	@./scripts/deploy-with-monitoring.sh
	@echo "$(GREEN)[SUCCESS]$(NC) Production deployment completed"

.PHONY: deploy-prod-simple
deploy-prod-simple: ## Deploy to production (simple, no monitoring)
	@echo "$(BLUE)[INFO]$(NC) Simple deployment to production..."
	@docker --context astro-vps compose -f scripts/deploy/docker-compose.yml -f scripts/deploy/docker-compose.prod.yml --env-file scripts/deploy/.env.production up -d --build
	@echo "$(GREEN)[SUCCESS]$(NC) Simple deployment completed"

.PHONY: deploy-binary
deploy-binary: ## Build locally and deploy binary (prevents VPS overload)
	@echo "$(BLUE)[INFO]$(NC) Building locally and deploying binary..."
	@./scripts/build-and-deploy.sh
	@echo "$(GREEN)[SUCCESS]$(NC) Binary deployment completed"

.PHONY: deploy-1gb
deploy-1gb: ## Optimized deployment for 1GB RAM + 1 CPU VPS
	@echo "$(BLUE)[INFO]$(NC) Deploying with 1GB VPS optimization..."
	@./scripts/deploy-1gb-vps.sh
	@echo "$(GREEN)[SUCCESS]$(NC) 1GB VPS deployment completed"

.PHONY: logs-prod-new
logs-prod-new: ## View production logs using new deploy structure
	@echo "$(BLUE)[INFO]$(NC) Viewing production logs..."
	@docker --context astro-vps compose -f scripts/deploy/docker-compose.yml -f scripts/deploy/docker-compose.prod.yml logs -f --tail=100

.PHONY: restart-prod
restart-prod: ## Restart production services
	@echo "$(BLUE)[INFO]$(NC) Restarting production services..."
	@docker --context astro-vps compose -f scripts/deploy/docker-compose.yml -f scripts/deploy/docker-compose.prod.yml restart

.PHONY: stop-prod
stop-prod: ## Stop production services
	@echo "$(BLUE)[INFO]$(NC) Stopping production services..."
	@docker --context astro-vps compose -f scripts/deploy/docker-compose.yml -f scripts/deploy/docker-compose.prod.yml down

.PHONY: status-prod
status-prod: ## Check production services status
	@echo "$(BLUE)[INFO]$(NC) Checking production status..."
	@docker --context astro-vps compose -f scripts/deploy/docker-compose.yml -f scripts/deploy/docker-compose.prod.yml ps

##@ Build Optimization
.PHONY: optimize-rust
optimize-rust: ## Analyze and optimize Rust build performance
	@echo "$(BLUE)[INFO]$(NC) Analyzing Rust build performance..."
	@./scripts/development/optimize-rust-build.sh

.PHONY: check-unused-deps
check-unused-deps: ## Check for unused dependencies
	@echo "$(BLUE)[INFO]$(NC) Checking for unused dependencies..."
	@./scripts/development/optimize-rust-build.sh deps

.PHONY: analyze-binary-size
analyze-binary-size: ## Analyze binary size breakdown
	@echo "$(BLUE)[INFO]$(NC) Analyzing binary size..."
	@./scripts/development/optimize-rust-build.sh size

.PHONY: setup-domains
setup-domains: ## Setup local domains for development (requires sudo)
	@echo "$(BLUE)[INFO]$(NC) Setting up local domains..."
	@sudo ./scripts/development/setup-domains.sh
	@echo "$(GREEN)[SUCCESS]$(NC) Local domains configured"

.PHONY: start-traefik
start-traefik: ## Start services with Traefik reverse proxy
	@echo "$(BLUE)[INFO]$(NC) Starting services with Traefik..."
	@docker-compose -f docker-compose.traefik.yml up -d
	@echo "$(GREEN)[SUCCESS]$(NC) Services started with Traefik!"
	@echo ""
	@echo "$(CYAN)Access URLs:$(NC)"
	@echo "  Frontend: http://astrokabinet.id"
	@echo "  API:      http://api.astrokabinet.id"
	@echo "  Traefik:  http://traefik.astrokabinet.id"

.PHONY: stop-traefik
stop-traefik: ## Stop Traefik services
	@echo "$(BLUE)[INFO]$(NC) Stopping Traefik services..."
	@docker-compose -f docker-compose.traefik.yml down
	@echo "$(GREEN)[SUCCESS]$(NC) Traefik services stopped"

.PHONY: setup-https
setup-https: ## Complete setup with local HTTPS domains (requires sudo)
	@echo "$(BLUE)[INFO]$(NC) Setting up local HTTPS development..."
	@sudo ./scripts/ssl/setup-local-https.sh

.PHONY: generate-ssl
generate-ssl: ## Generate SSL certificates for local development
	@echo "$(BLUE)[INFO]$(NC) Generating SSL certificates..."
	@./scripts/ssl/generate-ssl.sh
	@echo "$(GREEN)[SUCCESS]$(NC) SSL certificates generated"

.PHONY: check-deps
check-deps: ## Check if all required dependencies are installed
	@echo "$(BLUE)[INFO]$(NC) Checking dependencies..."
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)[ERROR]$(NC) Docker is not installed"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "$(RED)[ERROR]$(NC) Docker Compose is not installed"; exit 1; }
	@command -v cargo >/dev/null 2>&1 || { echo "$(RED)[ERROR]$(NC) Rust/Cargo is not installed"; exit 1; }
	@command -v node >/dev/null 2>&1 || { echo "$(RED)[ERROR]$(NC) Node.js is not installed"; exit 1; }
	@command -v npm >/dev/null 2>&1 || { echo "$(RED)[ERROR]$(NC) npm is not installed"; exit 1; }
	@echo "$(GREEN)[SUCCESS]$(NC) All dependencies are installed"

.PHONY: setup
setup: check-deps setup-env ## Initial project setup (install dependencies, create .env files)
	@echo "$(BLUE)[INFO]$(NC) Setting up Astro Works E-commerce Platform..."

	# Create Traefik network
	@echo "$(BLUE)[INFO]$(NC) Creating Traefik network..."
	@docker network create traefik 2>/dev/null || echo "$(YELLOW)[WARNING]$(NC) Traefik network already exists"

	# Install frontend dependencies
	@echo "$(BLUE)[INFO]$(NC) Installing frontend dependencies..."
	@cd fe_astro && npm install

	# Build Rust backend
	@echo "$(BLUE)[INFO]$(NC) Building Rust backend..."
	@cd be_astro && cargo build

	@echo "$(GREEN)[SUCCESS]$(NC) Setup completed successfully!"
	@echo ""
	@echo "$(CYAN)Next steps:$(NC)"
	@echo "  1. Run 'make validate-env' to check your configuration"
	@echo "  2. Run 'make dev' to start development environment"
	@echo "  3. Run 'make migrate' to set up the database"
	@echo "  4. Run 'make seed' to load sample data"

##@ Development
.PHONY: dev
dev: check-deps ## Start development environment (databases + local servers)
	@echo "$(BLUE)[INFO]$(NC) Starting development environment..."

	# Start databases
	@echo "$(BLUE)[INFO]$(NC) Starting PostgreSQL and Redis..."
	@docker-compose -f docker-compose.dev.yml up -d postgres redis

	# Wait for PostgreSQL
	@echo "$(BLUE)[INFO]$(NC) Waiting for PostgreSQL to be ready..."
	@timeout 60 bash -c 'until docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U astro_user -d astro_ecommerce; do sleep 2; done' || { echo "$(RED)[ERROR]$(NC) PostgreSQL failed to start"; exit 1; }

	@echo "$(GREEN)[SUCCESS]$(NC) Development environment is ready!"
	@echo ""
	@echo "$(CYAN)Services:$(NC)"
	@echo "  PostgreSQL: localhost:5432"
	@echo "  Redis: localhost:6379"
	@echo ""
	@echo "$(CYAN)To start the application servers:$(NC)"
	@echo "  Backend (port 7998): cd be_astro && cargo run --bin be_astro"
	@echo "  Frontend (bun + vite): cd fe_astro && bun run dev"
	@echo ""
	@echo "$(CYAN)Or use these shortcuts:$(NC)"
	@echo "  make start-backend"
	@echo "  make start-frontend"

.PHONY: dev-local
dev-local: dev ## Start development with local servers (API port 7998, frontend with bun)
	@echo "$(BLUE)[INFO]$(NC) Starting local development servers..."
	@echo ""
	@echo "$(CYAN)Starting backend on port 7998...$(NC)"
	@cd be_astro && PORT=7998 cargo run --bin be_astro &
	@echo ""
	@echo "$(CYAN)Starting frontend with bun...$(NC)"
	@cd fe_astro && bun run dev &
	@echo ""
	@echo "$(GREEN)[SUCCESS]$(NC) Local development servers started!"
	@echo ""
	@echo "$(CYAN)URLs:$(NC)"
	@echo "  Frontend: http://localhost:5173"
	@echo "  Backend API: http://localhost:7998/api/v1"
	@echo "  Health Check: http://localhost:7998/health"

.PHONY: start-backend
start-backend: ## Start the Rust backend server on port 7998
	@echo "$(BLUE)[INFO]$(NC) Starting backend server on port 7998..."
	@cd be_astro && PORT=7998 cargo run --bin be_astro

.PHONY: start-frontend
start-frontend: ## Start the Svelte frontend server with bun
	@echo "$(BLUE)[INFO]$(NC) Starting frontend server with bun..."
	@cd fe_astro && bun run dev

.PHONY: start-frontend-npm
start-frontend-npm: ## Start the Svelte frontend server with npm
	@echo "$(BLUE)[INFO]$(NC) Starting frontend server with npm..."
	@cd fe_astro && npm run dev

.PHONY: dev-full
dev-full: dev migrate seed ## Start full development environment with database setup
	@echo "$(GREEN)[SUCCESS]$(NC) Full development environment is ready!"

##@ Database Management
.PHONY: migrate
migrate: ## Run database migrations
	@echo "$(BLUE)[INFO]$(NC) Running database migrations..."
	@cd be_astro && cargo run --bin migrate
	@echo "$(GREEN)[SUCCESS]$(NC) Database migrations completed"

.PHONY: seed
seed: ## Load sample data into database
	@echo "$(BLUE)[INFO]$(NC) Loading sample data..."
	@docker-compose -f $(COMPOSE_DEV) exec -T postgres psql -U astro_user -d astro_ecommerce < be_astro/database/sample_data.sql || \
	 psql -h localhost -U astro_user -d astro_ecommerce -f be_astro/database/sample_data.sql
	@echo "$(GREEN)[SUCCESS]$(NC) Sample data loaded successfully"

.PHONY: db-reset
db-reset: ## Reset database (drop and recreate)
	@echo "$(YELLOW)[WARNING]$(NC) This will destroy all data. Continue? [y/N]"
	@read -r response; \
	if [ "$$response" = "y" ] || [ "$$response" = "Y" ]; then \
		echo "$(BLUE)[INFO]$(NC) Resetting database..."; \
		docker-compose -f $(COMPOSE_DEV) down -v; \
		docker-compose -f $(COMPOSE_DEV) up -d postgres redis; \
		sleep 10; \
		$(MAKE) migrate; \
		$(MAKE) seed; \
		echo "$(GREEN)[SUCCESS]$(NC) Database reset completed"; \
	else \
		echo "$(BLUE)[INFO]$(NC) Database reset cancelled"; \
	fi

.PHONY: backup
backup: ## Backup database and Redis data
	@echo "$(BLUE)[INFO]$(NC) Creating backup..."
	@mkdir -p backups
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	docker-compose -f $(COMPOSE_DEV) exec -T postgres pg_dump -U astro_user astro_ecommerce > "backups/postgres_$$timestamp.sql"; \
	docker-compose -f $(COMPOSE_DEV) exec -T redis redis-cli --rdb - > "backups/redis_$$timestamp.rdb"; \
	echo "$(GREEN)[SUCCESS]$(NC) Backup created: backups/postgres_$$timestamp.sql, backups/redis_$$timestamp.rdb"

.PHONY: restore
restore: ## Restore from backup (specify BACKUP_FILE=filename)
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "$(RED)[ERROR]$(NC) Please specify BACKUP_FILE=filename"; \
		echo "Available backups:"; \
		ls -la backups/ 2>/dev/null || echo "No backups found"; \
		exit 1; \
	fi
	@echo "$(BLUE)[INFO]$(NC) Restoring from backup: $(BACKUP_FILE)"
	@docker-compose -f $(COMPOSE_DEV) exec -T postgres psql -U astro_user -d astro_ecommerce < "backups/$(BACKUP_FILE)"
	@echo "$(GREEN)[SUCCESS]$(NC) Backup restored successfully"

##@ Testing
.PHONY: test
test: ## Run all tests (API tests, performance tests, database verification)
	@echo "$(BLUE)[INFO]$(NC) Running comprehensive tests..."

	# Verify database
	@echo "$(BLUE)[INFO]$(NC) Verifying database setup..."
	@cd be_astro && cargo run --bin verify_db

	# Run API tests
	@echo "$(BLUE)[INFO]$(NC) Running API tests..."
	@chmod +x scripts/testing/test_api.sh && ./scripts/testing/test_api.sh

	# Run performance tests
	@echo "$(BLUE)[INFO]$(NC) Running performance tests..."
	@chmod +x scripts/testing/performance_test.sh && ./scripts/testing/performance_test.sh

	@echo "$(GREEN)[SUCCESS]$(NC) All tests completed"

.PHONY: test-api
test-api: ## Run API tests only
	@echo "$(BLUE)[INFO]$(NC) Running API tests..."
	@chmod +x scripts/testing/test_api.sh && ./scripts/testing/test_api.sh

.PHONY: test-performance
test-performance: ## Run performance tests only
	@echo "$(BLUE)[INFO]$(NC) Running performance tests..."
	@chmod +x scripts/testing/performance_test.sh && ./scripts/testing/performance_test.sh

.PHONY: test-db
test-db: ## Verify database setup
	@echo "$(BLUE)[INFO]$(NC) Verifying database..."
	@cd be_astro && cargo run --bin verify_db

##@ Production Deployment
.PHONY: setup-prod
setup-prod: ## Setup production environment
	@echo "$(BLUE)[INFO]$(NC) Setting up production environment..."
	@if [ ! -f .env.prod ]; then \
		echo "$(BLUE)[INFO]$(NC) Creating .env.prod from example..."; \
		cp .env.prod.example .env.prod; \
		echo "$(YELLOW)[WARNING]$(NC) Please edit .env.prod with your production values"; \
	fi
	@mkdir -p be_astro/static/uploads
	@mkdir -p data/backups
	@mkdir -p logs
	@docker network create traefik 2>/dev/null || echo "$(YELLOW)[WARNING]$(NC) Traefik network already exists"
	@echo "$(GREEN)[SUCCESS]$(NC) Production environment setup complete!"

.PHONY: build
build: ## Build all Docker images for production
	@echo "$(BLUE)[INFO]$(NC) Building production Docker images..."

	# Build backend
	@echo "$(BLUE)[INFO]$(NC) Building backend image..."
	@docker build -t $(PROJECT_NAME)-backend:latest -f be_astro/Dockerfile.prod be_astro/

	# Build frontend
	@echo "$(BLUE)[INFO]$(NC) Building frontend image..."
	@docker build -t $(PROJECT_NAME)-frontend:latest -f fe_astro/Dockerfile.prod fe_astro/

	@echo "$(GREEN)[SUCCESS]$(NC) All images built successfully"

.PHONY: deploy
deploy: build ## Deploy to production using docker-compose.prod.yml
	@echo "$(BLUE)[INFO]$(NC) Deploying to production..."

	# Create production environment file
	@echo "$(BLUE)[INFO]$(NC) Creating production environment..."
	@echo "DOMAIN=$(DOMAIN)" > .env.prod
	@echo "POSTGRES_PASSWORD=$(POSTGRES_PASSWORD)" >> .env.prod
	@echo "JWT_SECRET=$(JWT_SECRET)" >> .env.prod
	@echo "TRAEFIK_AUTH=$(TRAEFIK_AUTH)" >> .env.prod
	@echo "API_URL=https://$(DOMAIN)/api/v1" >> .env.prod
	@echo "FRONTEND_URL=https://$(DOMAIN)" >> .env.prod
	@echo "ACME_EMAIL=admin@$(DOMAIN)" >> .env.prod

	# Create Traefik network
	@docker network create traefik 2>/dev/null || echo "$(YELLOW)[WARNING]$(NC) Traefik network already exists"

	# Deploy services
	@docker-compose -f $(COMPOSE_PROD) --env-file .env.prod up -d

	# Wait for services to be healthy
	@echo "$(BLUE)[INFO]$(NC) Waiting for services to be healthy..."
	@sleep 30

	# Run migrations
	@echo "$(BLUE)[INFO]$(NC) Running production migrations..."
	@docker-compose -f $(COMPOSE_PROD) --env-file .env.prod exec backend cargo run --bin migrate

	@echo "$(GREEN)[SUCCESS]$(NC) Production deployment completed!"
	@echo ""
	@echo "$(CYAN)Services:$(NC)"
	@echo "  Frontend: https://$(DOMAIN)"
	@echo "  API: https://$(DOMAIN)/api/v1"
	@echo "  Traefik Dashboard: https://traefik.$(DOMAIN)"
	@echo "  Health Check: https://$(DOMAIN)/health"

.PHONY: deploy-vps
deploy-vps: ## Deploy backend API to VPS (port 7998)
	@echo "$(BLUE)[INFO]$(NC) Deploying backend API to VPS..."
	@./scripts/deploy-vps.sh

.PHONY: vps-logs
vps-logs: ## Show VPS deployment logs
	@echo "$(BLUE)[INFO]$(NC) Fetching VPS logs..."
	@if [ -f .env.vps.secure ]; then \
		source .env.vps.secure && \
		ssh $$VPS_HOST "cd $$VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml logs -f"; \
	else \
		echo "$(RED)[ERROR]$(NC) .env.vps.secure file not found"; \
	fi

.PHONY: vps-restart
vps-restart: ## Restart VPS services
	@echo "$(BLUE)[INFO]$(NC) Restarting VPS services..."
	@if [ -f .env.vps.secure ]; then \
		source .env.vps.secure && \
		ssh $$VPS_HOST "cd $$VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml restart"; \
	else \
		echo "$(RED)[ERROR]$(NC) .env.vps.secure file not found"; \
	fi

.PHONY: vps-status
vps-status: ## Check VPS services status
	@echo "$(BLUE)[INFO]$(NC) Checking VPS services status..."
	@if [ -f .env.vps.secure ]; then \
		source .env.vps.secure && \
		ssh $$VPS_HOST "cd $$VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml ps"; \
	else \
		echo "$(RED)[ERROR]$(NC) .env.vps.secure file not found"; \
	fi

.PHONY: setup-vps
setup-vps: ## Setup VPS for deployment (install Docker, configure firewall, etc.)
	@echo "$(BLUE)[INFO]$(NC) Setting up VPS for deployment..."
	@./scripts/setup-vps.sh

.PHONY: vps-backup
vps-backup: ## Create backup on VPS
	@echo "$(BLUE)[INFO]$(NC) Creating backup on VPS..."
	@if [ -f .env.vps.secure ]; then \
		source .env.vps.secure && \
		ssh $$VPS_HOST "$$VPS_DEPLOY_PATH/backup.sh"; \
	else \
		echo "$(RED)[ERROR]$(NC) .env.vps.secure file not found"; \
	fi

.PHONY: vps-health
vps-health: ## Check VPS API health
	@echo "$(BLUE)[INFO]$(NC) Checking VPS API health..."
	@echo "Testing https://furapi.astrokabinet.id/health..."
	@curl -f "https://furapi.astrokabinet.id/health" || echo "$(RED)[ERROR]$(NC) API health check failed"

.PHONY: verify-vps
verify-vps: ## Run comprehensive VPS deployment verification
	@echo "$(BLUE)[INFO]$(NC) Running VPS deployment verification..."
	@./scripts/verify-deployment.sh

.PHONY: vps-ssl
vps-ssl: ## Check SSL certificate status
	@echo "$(BLUE)[INFO]$(NC) Checking SSL certificate..."
	@curl -I https://furapi.astrokabinet.id/health | head -5

.PHONY: vps-traefik
vps-traefik: ## Show Traefik dashboard info
	@echo "$(BLUE)[INFO]$(NC) Traefik Dashboard Information:"
	@echo "$(CYAN)Dashboard URL:$(NC) https://traefik.furapi.astrokabinet.id"
	@echo "$(CYAN)Username:$(NC) admin"
	@echo "$(CYAN)Password:$(NC) (configured in Traefik labels)"
	@echo ""
	@echo "$(YELLOW)Traefik Logs:$(NC)"
	@if [ -f .env.vps.secure ]; then \
		source .env.vps.secure && \
		ssh $$VPS_HOST "cd $$VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml logs traefik --tail=10"; \
	else \
		echo "$(RED)[ERROR]$(NC) .env.vps.secure file not found"; \
	fi

.PHONY: deploy-frontend
deploy-frontend: ## Deploy frontend to Cloudflare Workers
	@echo "$(BLUE)[INFO]$(NC) Deploying frontend to Cloudflare Workers..."
	@./scripts/deploy-frontend.sh

.PHONY: deploy-full
deploy-full: deploy-vps deploy-frontend ## Deploy both backend and frontend
	@echo "$(GREEN)[SUCCESS]$(NC) Full deployment completed!"
	@echo ""
	@echo "$(CYAN)URLs:$(NC)"
	@echo "  Frontend: https://astrokabinet.id"
	@echo "  API: https://furapi.astrokabinet.id"
	@echo "  Health: https://furapi.astrokabinet.id/health"

.PHONY: deploy-staging
deploy-staging: ## Deploy to staging environment
	@echo "$(BLUE)[INFO]$(NC) Deploying to staging..."
	@DOMAIN=staging.$(DOMAIN) $(MAKE) deploy

.PHONY: ssl
ssl: ## Generate SSL certificates for local development
	@echo "$(BLUE)[INFO]$(NC) Generating SSL certificates for local development..."
	@mkdir -p ssl

	# Generate private key
	@openssl genrsa -out ssl/$(DOMAIN).key 2048

	# Generate certificate signing request
	@openssl req -new -key ssl/$(DOMAIN).key -out ssl/$(DOMAIN).csr -subj "/C=ID/ST=Jakarta/L=Jakarta/O=Astro Works/CN=$(DOMAIN)"

	# Generate self-signed certificate
	@openssl x509 -req -in ssl/$(DOMAIN).csr -signkey ssl/$(DOMAIN).key -out ssl/$(DOMAIN).crt -days 365

	@echo "$(GREEN)[SUCCESS]$(NC) SSL certificates generated in ssl/ directory"
	@echo "$(YELLOW)[WARNING]$(NC) These are self-signed certificates for development only"

##@ Monitoring and Logs
.PHONY: logs
logs: ## Show logs from all services
	@echo "$(BLUE)[INFO]$(NC) Showing logs from all services..."
	@docker-compose -f $(COMPOSE_DEV) logs -f

.PHONY: logs-backend
logs-backend: ## Show backend logs only
	@docker-compose -f $(COMPOSE_DEV) logs -f backend

.PHONY: logs-frontend
logs-frontend: ## Show frontend logs only
	@docker-compose -f $(COMPOSE_DEV) logs -f frontend

.PHONY: logs-db
logs-db: ## Show database logs only
	@docker-compose -f $(COMPOSE_DEV) logs -f postgres

.PHONY: logs-redis
logs-redis: ## Show Redis logs only
	@docker-compose -f $(COMPOSE_DEV) logs -f redis

.PHONY: logs-prod
logs-prod: ## Show production logs
	@docker-compose -f $(COMPOSE_PROD) logs -f

.PHONY: monitor
monitor: ## Start monitoring and health check dashboard
	@echo "$(BLUE)[INFO]$(NC) Starting monitoring dashboard..."
	@echo "$(CYAN)Monitoring URLs:$(NC)"
	@echo "  Health Check: http://localhost:8000/health"
	@echo "  Traefik Dashboard: http://localhost:8080"
	@echo "  Frontend: http://localhost:5173 (dev) or http://localhost:3000 (prod)"
	@echo ""
	@echo "$(BLUE)[INFO]$(NC) Checking service health..."
	@curl -f http://localhost:8000/health 2>/dev/null && echo "$(GREEN)✓$(NC) Backend is healthy" || echo "$(RED)✗$(NC) Backend is not responding"
	@curl -f http://localhost:5173 2>/dev/null && echo "$(GREEN)✓$(NC) Frontend (dev) is healthy" || echo "$(YELLOW)!$(NC) Frontend (dev) is not responding"
	@curl -f http://localhost:3000 2>/dev/null && echo "$(GREEN)✓$(NC) Frontend (prod) is healthy" || echo "$(YELLOW)!$(NC) Frontend (prod) is not responding"

.PHONY: status
status: ## Show status of all services
	@echo "$(BLUE)[INFO]$(NC) Service Status:"
	@echo "$(CYAN)Development Services:$(NC)"
	@docker-compose -f $(COMPOSE_DEV) ps
	@echo ""
	@echo "$(CYAN)Production Services:$(NC)"
	@docker-compose -f $(COMPOSE_PROD) ps 2>/dev/null || echo "Production services not running"

##@ Maintenance
.PHONY: clean
clean: ## Clean up containers, volumes, and build artifacts
	@echo "$(YELLOW)[WARNING]$(NC) This will remove all containers, volumes, and build artifacts. Continue? [y/N]"
	@read -r response; \
	if [ "$$response" = "y" ] || [ "$$response" = "Y" ]; then \
		echo "$(BLUE)[INFO]$(NC) Cleaning up..."; \
		docker-compose -f $(COMPOSE_DEV) down -v --remove-orphans; \
		docker-compose -f $(COMPOSE_PROD) down -v --remove-orphans 2>/dev/null || true; \
		docker system prune -f; \
		docker volume prune -f; \
		cd be_astro && cargo clean; \
		cd fe_astro && rm -rf node_modules dist; \
		rm -rf backups/*.sql backups/*.rdb; \
		echo "$(GREEN)[SUCCESS]$(NC) Cleanup completed"; \
	else \
		echo "$(BLUE)[INFO]$(NC) Cleanup cancelled"; \
	fi

.PHONY: clean-soft
clean-soft: ## Soft cleanup (containers only, keep volumes)
	@echo "$(BLUE)[INFO]$(NC) Performing soft cleanup..."
	@docker-compose -f $(COMPOSE_DEV) down --remove-orphans
	@docker-compose -f $(COMPOSE_PROD) down --remove-orphans 2>/dev/null || true
	@echo "$(GREEN)[SUCCESS]$(NC) Soft cleanup completed"

.PHONY: restart
restart: ## Restart all development services
	@echo "$(BLUE)[INFO]$(NC) Restarting development services..."
	@docker-compose -f $(COMPOSE_DEV) restart
	@echo "$(GREEN)[SUCCESS]$(NC) Services restarted"

.PHONY: restart-prod
restart-prod: ## Restart all production services
	@echo "$(BLUE)[INFO]$(NC) Restarting production services..."
	@docker-compose -f $(COMPOSE_PROD) restart
	@echo "$(GREEN)[SUCCESS]$(NC) Production services restarted"

.PHONY: update
update: ## Update all dependencies and rebuild
	@echo "$(BLUE)[INFO]$(NC) Updating dependencies..."

	# Update Rust dependencies
	@echo "$(BLUE)[INFO]$(NC) Updating Rust dependencies..."
	@cd be_astro && cargo update

	# Update Node.js dependencies
	@echo "$(BLUE)[INFO]$(NC) Updating Node.js dependencies..."
	@cd fe_astro && npm update

	# Rebuild
	@echo "$(BLUE)[INFO]$(NC) Rebuilding..."
	@$(MAKE) build

	@echo "$(GREEN)[SUCCESS]$(NC) Dependencies updated and rebuilt"

##@ Utilities
.PHONY: shell-backend
shell-backend: ## Open shell in backend container
	@docker-compose -f $(COMPOSE_DEV) exec backend bash || docker-compose -f $(COMPOSE_PROD) exec backend bash

.PHONY: shell-frontend
shell-frontend: ## Open shell in frontend container
	@docker-compose -f $(COMPOSE_DEV) exec frontend sh || docker-compose -f $(COMPOSE_PROD) exec frontend sh

.PHONY: shell-db
shell-db: ## Open PostgreSQL shell
	@docker-compose -f $(COMPOSE_DEV) exec postgres psql -U astro_user -d astro_ecommerce

.PHONY: shell-redis
shell-redis: ## Open Redis CLI
	@docker-compose -f $(COMPOSE_DEV) exec redis redis-cli

.PHONY: format
format: ## Format code (Rust and JavaScript)
	@echo "$(BLUE)[INFO]$(NC) Formatting code..."
	@cd be_astro && cargo fmt
	@cd fe_astro && npm run format 2>/dev/null || echo "$(YELLOW)[WARNING]$(NC) Frontend formatting not available"
	@echo "$(GREEN)[SUCCESS]$(NC) Code formatted"

.PHONY: lint
lint: ## Lint code (Rust and JavaScript)
	@echo "$(BLUE)[INFO]$(NC) Linting code..."
	@cd be_astro && cargo clippy -- -D warnings
	@cd fe_astro && npm run lint 2>/dev/null || echo "$(YELLOW)[WARNING]$(NC) Frontend linting not available"
	@echo "$(GREEN)[SUCCESS]$(NC) Code linted"

.PHONY: docs
docs: ## Generate documentation
	@echo "$(BLUE)[INFO]$(NC) Generating documentation..."
	@cd be_astro && cargo doc --no-deps --open
	@echo "$(GREEN)[SUCCESS]$(NC) Documentation generated"

##@ Quick Actions
.PHONY: quick-start
quick-start: setup dev-full ## Quick start: setup + dev environment + database
	@echo "$(GREEN)[SUCCESS]$(NC) Quick start completed!"
	@echo ""
	@echo "$(CYAN)Your Astro Works e-commerce platform is ready!$(NC)"
	@echo ""
	@echo "$(CYAN)Next steps:$(NC)"
	@echo "  1. Start backend: make start-backend"
	@echo "  2. Start frontend: make start-frontend"
	@echo "  3. Visit: http://localhost:5173"
	@echo "  4. API: http://localhost:8000/api/v1"

.PHONY: quick-test
quick-test: test-db test-api ## Quick test: database + API tests
	@echo "$(GREEN)[SUCCESS]$(NC) Quick tests completed!"

.PHONY: quick-deploy
quick-deploy: build deploy ## Quick deploy: build + deploy to production
	@echo "$(GREEN)[SUCCESS]$(NC) Quick deployment completed!"

##@ Information
.PHONY: info
info: ## Show project information and useful commands
	@echo "$(CYAN)Astro Works E-commerce Platform$(NC)"
	@echo "$(CYAN)================================$(NC)"
	@echo ""
	@echo "$(PURPLE)Project Structure:$(NC)"
	@echo "  be_astro/     - Rust backend (Axum + SeaORM)"
	@echo "  fe_astro/     - Svelte frontend"
	@echo "  database/     - Database schemas and sample data"
	@echo "  traefik/      - Traefik reverse proxy configuration"
	@echo ""
	@echo "$(PURPLE)Key Features:$(NC)"
	@echo "  ✓ Dynamic product pricing with dimensions and themes"
	@echo "  ✓ Real-time price calculations"
	@echo "  ✓ Product bundling system"
	@echo "  ✓ WhatsApp integration for orders"
	@echo "  ✓ Admin-only authentication"
	@echo "  ✓ Redis caching and rate limiting"
	@echo "  ✓ Traefik reverse proxy with SSL"
	@echo ""
	@echo "$(PURPLE)Development URLs:$(NC)"
	@echo "  Frontend: http://localhost:5173"
	@echo "  Backend API: http://localhost:8000/api/v1"
	@echo "  Health Check: http://localhost:8000/health"
	@echo "  Database: localhost:5432"
	@echo "  Redis: localhost:6379"
	@echo ""
	@echo "$(PURPLE)Production URLs:$(NC)"
	@echo "  Frontend: https://$(DOMAIN)"
	@echo "  API: https://$(DOMAIN)/api/v1"
	@echo "  Traefik Dashboard: https://traefik.$(DOMAIN)"
	@echo ""
	@echo "$(PURPLE)Common Commands:$(NC)"
	@echo "  make quick-start  - Complete setup and start development"
	@echo "  make dev         - Start development environment"
	@echo "  make test        - Run all tests"
	@echo "  make deploy      - Deploy to production"
	@echo "  make clean       - Clean up everything"
	@echo "  make help        - Show all available commands"

.PHONY: version
version: ## Show version information
	@echo "$(CYAN)Astro Works E-commerce Platform v1.0.0$(NC)"
	@echo ""
	@echo "$(PURPLE)Component Versions:$(NC)"
	@docker --version 2>/dev/null || echo "Docker: Not installed"
	@docker-compose --version 2>/dev/null || echo "Docker Compose: Not installed"
	@cargo --version 2>/dev/null || echo "Rust/Cargo: Not installed"
	@node --version 2>/dev/null || echo "Node.js: Not installed"
	@npm --version 2>/dev/null || echo "npm: Not installed"

# Ensure all shell commands use bash
SHELL := /bin/bash

# Prevent make from deleting intermediate files
.SECONDARY:

# Make all targets phony by default (except for file targets)
.PHONY: $(filter-out %.env %.sql %.yml %.yaml %.json %.toml, $(MAKECMDGOALS))
