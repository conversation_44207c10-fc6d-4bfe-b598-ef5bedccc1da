# Astro Works - Quick Start Guide

## 🚀 Menjalankan Project Secara Local

### Prerequisites
- Docker & Docker Compose
- Rust & Cargo
- Bun (recommended) atau Node.js & npm

### Option 1: Manual Setup (Recommended jika Docker bermasalah)

#### 1. Setup Database dengan Docker Compose
```bash
# Gunakan docker-compose yang sudah ada
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Atau jika bermasalah, coba restart Docker dan jalankan:
sudo systemctl restart docker
docker-compose -f docker-compose.dev.yml up -d postgres redis
```

#### 2. Setup Environment
```bash
# Copy file environment untuk development
cp .env.dev be_astro/.env
```

#### 3. Install Frontend Dependencies
```bash
cd fe_astro
bun install  # atau npm install jika bun tidak tersedia
cd ..
```

#### 4. <PERSON><PERSON><PERSON> Backend (Terminal 1)
```bash
cd be_astro
PORT=7998 cargo run --bin be_astro
```

#### 5. J<PERSON><PERSON> Frontend (Terminal 2)
```bash
cd fe_astro
bun run dev  # atau npm run dev
```

### Option 2: Menggunakan Script Otomatis

```bash
# Jika Docker berfungsi normal
./dev-simple.sh

# Kemudian jalankan backend dan frontend secara manual
```

### Option 3: Menggunakan Makefile

```bash
# Start databases
make dev

# Di terminal terpisah:
make start-backend    # Backend di port 7998
make start-frontend   # Frontend dengan bun
```

## 📍 URLs Setelah Setup

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:7998/api/v1
- **Health Check**: http://localhost:7998/health
- **Admin Panel**: http://localhost:5173/manajemen

## 🔧 Troubleshooting

### Docker Issues
```bash
# Restart Docker
sudo systemctl restart docker

# Clear Docker cache
docker system prune -f

# Check Docker status
docker info
```

### Port Conflicts
```bash
# Check what's using port 5432 (PostgreSQL)
sudo lsof -i :5432

# Check what's using port 7998 (Backend)
sudo lsof -i :7998

# Check what's using port 5173 (Frontend)
sudo lsof -i :5173
```

### Backend Issues
```bash
cd be_astro

# Clean and rebuild
cargo clean
cargo build

# Check environment
cat .env

# Run with debug logs
RUST_LOG=debug PORT=7998 cargo run --bin be_astro
```

### Frontend Issues
```bash
cd fe_astro

# Clear cache and reinstall
rm -rf node_modules
bun install  # atau npm install

# Check if bun is available
which bun

# Fallback to npm if needed
npm run dev
```

### Database Connection Issues
```bash
# Check if PostgreSQL is running
docker ps | grep postgres

# Test connection
docker exec -it <postgres_container_id> psql -U astro_user -d astro_ecommerce

# View logs
docker logs <postgres_container_id>
```

## 🎯 Quick Commands

```bash
# Stop all containers
docker stop $(docker ps -q)

# Remove all containers
docker rm $(docker ps -aq)

# Start fresh
docker system prune -f
./dev-simple.sh

# Check running processes
docker ps
```

## 📝 Development Workflow

1. **Start Databases**: `docker-compose -f docker-compose.dev.yml up -d`
2. **Backend**: `cd be_astro && PORT=7998 cargo run --bin be_astro`
3. **Frontend**: `cd fe_astro && bun run dev`
4. **Access**: Frontend di http://localhost:5173, API di http://localhost:7998

## 🔐 Admin Access

- **Email**: <EMAIL>
- **Password**: admin123
- **URL**: http://localhost:5173/manajemen

## 📊 Database Info

- **Host**: localhost
- **Port**: 5432
- **Database**: astro_ecommerce
- **User**: astro_user
- **Password**: astro_password

## 🚨 Jika Semua Gagal

1. Restart Docker: `sudo systemctl restart docker`
2. Clear semua: `docker system prune -af`
3. Jalankan manual:
   ```bash
   # Terminal 1: Database
   docker run -d -p 5432:5432 -e POSTGRES_DB=astro_ecommerce -e POSTGRES_USER=astro_user -e POSTGRES_PASSWORD=astro_password postgres:16-alpine
   
   # Terminal 2: Redis
   docker run -d -p 6379:6379 redis:7-alpine
   
   # Terminal 3: Backend
   cd be_astro && cp ../.env.dev .env && PORT=7998 cargo run --bin be_astro
   
   # Terminal 4: Frontend
   cd fe_astro && bun run dev
   ```
