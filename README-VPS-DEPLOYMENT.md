# VPS Deployment Guide - Astro Works Backend API

## Overview
This guide explains how to deploy the Astro Works backend API to your VPS using Docker with port 7998.

## Prerequisites

### 1. VPS Requirements
- Ubuntu/Debian server
- Minimum 2GB RAM, 20GB storage
- SSH access configured with key authentication
- Root or sudo access

### 2. Local Requirements
- Docker installed locally
- SSH key access to VPS
- Git repository cloned

## Quick Start

### 1. Configure VPS Connection
```bash
# Copy the secure environment template
cp .env.vps.secure.example .env.vps.secure

# Edit with your actual VPS details
nano .env.vps.secure
```

Example `.env.vps.secure`:
```bash
VPS_USER=servomo
VPS_IP=*************
VPS_HOST=servomo@*************
DOCKER_CONTEXT_NAME=astro-vps
VPS_DEPLOY_PATH=/srv/app/astro-works
```

### 2. Setup VPS (One-time)
```bash
# Setup VPS with <PERSON><PERSON>, firewall, monitoring
make setup-vps
```

### 3. Deploy Backend API
```bash
# Deploy backend to VPS with port 7998
make deploy-vps
```

### 4. Verify Deployment
```bash
# Check service status
make vps-status

# Test API health
make vps-health

# View logs
make vps-logs
```

## Security Configuration

### 1. Environment Files
- `.env.vps.secure` - VPS connection details (NOT in Git)
- `.env.vps` - Production configuration (safe to commit)

### 2. VPS Environment
The `.env.vps` file contains production configuration for:
- Database credentials
- JWT secrets
- API endpoints
- Business configuration

## Complete Deployment Process

### Step 1: Initial VPS Setup (One-time)
```bash
# Setup VPS with Docker, firewall, monitoring, backup
make setup-vps
```

This will:
- Install Docker and Docker Compose
- Configure UFW firewall (ports 22, 80, 443, 7998)
- Create deployment directories
- Setup log rotation
- Configure automated backups
- Install monitoring tools

### Step 2: Deploy Backend API
```bash
# Deploy backend to VPS with port 7998
make deploy-vps
```

This will:
- Setup Docker context for VPS
- Copy application files to VPS
- Build and start Docker containers
- Run database migrations
- Configure health checks

### Step 3: Verify Deployment
```bash
# Run comprehensive verification
make verify-vps
```

This will test:
- VPS connectivity
- Service status
- API endpoints
- Database connectivity
- Redis connectivity
- System resources
- Performance metrics

### Step 4: Monitor and Manage
```bash
# Check service status
make vps-status

# View real-time logs
make vps-logs

# Test API health
make vps-health

# Create backup
make vps-backup

# Restart services if needed
make vps-restart
```

## Service Configuration

### Backend API
- **Port**: 7998
- **Health Check**: `http://YOUR_VPS_IP:7998/health`
- **API Base**: `http://YOUR_VPS_IP:7998/api/v1`

### Database
- **PostgreSQL**: Internal Docker network
- **Redis**: Internal Docker network
- **Data Persistence**: Docker volumes

## Management Commands

### Service Management
```bash
# Check service status
make vps-status

# Restart services
make vps-restart

# View real-time logs
make vps-logs

# SSH to VPS
ssh servomo@YOUR_VPS_IP
```

### Docker Commands on VPS
```bash
# On VPS, navigate to deployment directory
cd /srv/app/astro-works

# View services
docker compose -f docker-compose.vps.yml ps

# View logs
docker compose -f docker-compose.vps.yml logs -f backend

# Restart specific service
docker compose -f docker-compose.vps.yml restart backend

# Update and rebuild
docker compose -f docker-compose.vps.yml up -d --build
```

## Troubleshooting

### 1. Connection Issues
```bash
# Test SSH connection
ssh servomo@YOUR_VPS_IP

# Test Docker context
docker --context astro-vps version
```

### 2. Service Issues
```bash
# Check service health
curl http://YOUR_VPS_IP:7998/health

# Check container logs
make vps-logs

# Restart services
make vps-restart
```

### 3. Database Issues
```bash
# Connect to database container
ssh servomo@YOUR_VPS_IP
cd /srv/app/astro-works
docker compose -f docker-compose.vps.yml exec postgres psql -U astro_user -d astro_ecommerce
```

## Security Best Practices

### 1. Environment Variables
- Never commit `.env.vps.secure` to version control
- Use strong passwords for all services
- Regularly rotate JWT secrets

### 2. VPS Security
- Keep system packages updated
- Configure firewall (UFW)
- Monitor access logs
- Use SSH key authentication only

### 3. Docker Security
- Run containers as non-root user
- Limit container resources
- Regular security updates

## Monitoring

### 1. Health Checks
- API: `http://YOUR_VPS_IP:7998/health`
- Database: Built-in Docker health checks
- Redis: Built-in Docker health checks

### 2. Logs
```bash
# Application logs
make vps-logs

# System logs
ssh servomo@YOUR_VPS_IP 'journalctl -u docker -f'
```

## Backup Strategy

### 1. Database Backup
```bash
# Create backup
ssh servomo@YOUR_VPS_IP 'cd /srv/app/astro-works && docker compose -f docker-compose.vps.yml exec postgres pg_dump -U astro_user astro_ecommerce > backup.sql'

# Restore backup
ssh servomo@YOUR_VPS_IP 'cd /srv/app/astro-works && docker compose -f docker-compose.vps.yml exec -T postgres psql -U astro_user astro_ecommerce < backup.sql'
```

### 2. Application Data
```bash
# Backup static files
rsync -avz servomo@YOUR_VPS_IP:/srv/app/astro-works/be_astro/static/ ./backup/static/

# Backup data directory
rsync -avz servomo@YOUR_VPS_IP:/srv/app/astro-works/data/ ./backup/data/
```

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review service logs
3. Verify VPS connectivity
4. Check Docker service status

## File Structure on VPS
```
/srv/app/astro-works/
├── docker-compose.vps.yml
├── .env
├── be_astro/
│   ├── Dockerfile
│   ├── src/
│   └── static/
└── data/
```
