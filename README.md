# Astro Works E-commerce Platform

A complete e-commerce solution for furniture sales with flexible product bundling, admin-only management, and WhatsApp integration.

## 🏗️ Architecture

- **Frontend**: Svelte with TailwindCSS
- **Backend**: Rust with Axum framework
- **Database**: PostgreSQL with SeaORM
- **Cache**: Redis
- **Containerization**: Docker & Docker Compose

## 🚀 Features

### Core Business Logic
- **Flexible Product Units**: Support for meters, pieces, sets, and custom units
- **Product Bundling**: Main products can be bundled with accessories
- **Admin-Only System**: No customer accounts, direct checkout process
- **WhatsApp Integration**: Automatic order confirmation links
- **Manual Payment**: BCA bank transfer with proof upload

### Product Management
- Multiple product categories with hierarchy
- Special "accessory" category for bundling
- Multiple images per product
- Detailed specifications (JSON-based)
- SEO-friendly URLs and metadata

### Order Processing
- Auto-generated order numbers
- Complete Indonesian address format
- Order status tracking
- WhatsApp link generation
- Payment proof management

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Rust (latest stable)
- Node.js 18+ (for frontend)
- Make (optional, but recommended)

### One-Command Setup

```bash
# Complete setup and start development environment
make quick-start
```

This will:
- ✅ Check all dependencies
- ✅ Create environment files
- ✅ Start database services
- ✅ Run migrations
- ✅ Load sample data
- ✅ Build the application

### Manual Setup

1. **Validate your environment**
```bash
./scripts/development/validate-setup.sh
```

2. **Initial setup**
```bash
make setup
```

3. **Start development environment**
```bash
make dev
```

4. **Run migrations and load data**
```bash
make migrate
make seed
```

5. **Start application servers**
```bash
# Terminal 1: Backend
make start-backend

# Terminal 2: Frontend
make start-frontend
```

### Production Deployment

```bash
# Build and deploy to production
make deploy

# Or step by step
make build
make deploy
```

### Available Make Commands

```bash
make help                 # Show all available commands
make setup               # Initial project setup
make dev                 # Start development environment
make test                # Run all tests
make build               # Build production images
make deploy              # Deploy to production
make clean               # Clean up everything
make logs                # Show service logs
make status              # Show service status
make backup              # Backup database
make monitor             # Health check dashboard
```

### Using Docker Compose (Alternative)

```bash
# Development environment
docker-compose up -d postgres redis

# Production environment
docker-compose -f docker-compose.prod.yml up -d

# Access the application
# Frontend: http://localhost:5173 (dev) 
# Backend API: http://localhost:8000 (dev
# Database: localhost:5432
# Redis: localhost:6379
```

## 🏗️ Architecture & Infrastructure

### Reverse Proxy: Traefik
- **Automatic HTTPS**: Let's Encrypt SSL certificates
- **Load Balancing**: Health checks and failover
- **Rate Limiting**: API and web traffic protection
- **Security Headers**: HSTS, XSS protection, etc.
- **Dashboard**: Monitoring and configuration UI

### Production Features
- **SSL/TLS**: Automatic certificate management
- **Rate Limiting**: Configurable per endpoint
- **Security Headers**: Comprehensive protection
- **Health Checks**: Automatic service monitoring
- **Logging**: Structured JSON logs
- **Metrics**: Prometheus-compatible metrics
- **Caching**: Redis-based response caching

### Development Features
- **Hot Reload**: Frontend and backend auto-restart
- **Debug Logging**: Detailed development logs
- **Database Tools**: Easy migration and seeding
- **API Testing**: Built-in test scripts
- **Performance Monitoring**: Response time tracking

## 📊 Database Schema

The database is designed around these core entities:

- **admin_users**: Admin authentication and management
- **categories**: Hierarchical product categories
- **products**: Main product catalog with flexible units
- **product_images**: Multiple images per product
- **product_bundles**: Bundle configurations
- **bundle_items**: Items within bundles
- **orders**: Customer orders and information
- **order_items**: Individual items in orders

See the backend documentation in [`docs/backend/`](docs/backend/) for detailed schema documentation.

## 🔧 API Endpoints

### Authentication
- `POST /api/v1/auth/login` - Admin login
- `POST /api/v1/auth/refresh` - Refresh token

### Categories
- `GET /api/v1/categories` - List categories
- `POST /api/v1/categories` - Create category
- `GET /api/v1/categories/:id` - Get category
- `PATCH /api/v1/categories/:id` - Update category
- `DELETE /api/v1/categories/:id` - Delete category

### Products
- `GET /api/v1/products` - List products
- `POST /api/v1/products` - Create product
- `GET /api/v1/products/:id` - Get product
- `PATCH /api/v1/products/:id` - Update product
- `DELETE /api/v1/products/:id` - Delete product

### Bundles
- `GET /api/v1/bundles` - List bundles
- `POST /api/v1/bundles` - Create bundle
- `GET /api/v1/bundles/:id` - Get bundle
- `PATCH /api/v1/bundles/:id` - Update bundle
- `DELETE /api/v1/bundles/:id` - Delete bundle

### Orders
- `GET /api/v1/orders` - List orders
- `POST /api/v1/orders` - Create order
- `GET /api/v1/orders/:id` - Get order
- `PATCH /api/v1/orders/:id` - Update order
- `GET /api/v1/orders/:id/whatsapp` - Generate WhatsApp link

## 🏢 Business Examples

### Product Bundle Example
**Fantasy TV Cabinet Complete Package**
- Main Product: Fantasy TV Cabinet (1 piece) - Rp 1,590,000
- LED Strip: 10 meters @ Rp 1,000,000/meter
- LED Gola: 2 meters @ Rp 3,000,000/meter  
- Tandem Box: 3 pieces @ Rp 1,500,000/piece
- **Total**: Rp 94,900,000 (with 5% bundle discount)

### Order Flow
1. Customer selects products/bundles on website
2. Fills out delivery information (no account needed)
3. Order created with auto-generated number (e.g., ************)
4. WhatsApp link generated for order confirmation
5. Customer transfers payment to BCA account
6. Admin updates order status and uploads payment proof

## 🔒 Security Features

- **Password Hashing**: Bcrypt with salt
- **JWT Authentication**: Secure admin sessions
- **UUID Primary Keys**: Prevent enumeration attacks
- **Input Validation**: Comprehensive request validation
- **CORS Configuration**: Secure cross-origin requests

## 📈 Performance Optimizations

- **Database Indexing**: Optimized queries for common operations
- **Redis Caching**: Session and frequently accessed data
- **Connection Pooling**: Efficient database connections
- **Pagination**: Large dataset handling
- **JSON Fields**: Flexible product specifications

## 🧪 Testing

### Comprehensive Testing
```bash
make test                 # Run all tests
make test-api            # API functionality tests
make test-performance    # Performance and load tests
make test-db             # Database verification
```

### Manual Testing
```bash
# Backend tests
cd be_astro && cargo test

# Frontend tests
cd fe_astro && npm test

# API testing
./scripts/testing/test_api.sh

# Performance testing
./scripts/testing/performance_test.sh
```

### Load Sample Data
```bash
make seed                # Load sample data
make db-reset           # Reset database with fresh data
```

## 🛠️ Makefile Commands Reference

### Environment Setup
- `make setup` - Initial project setup
- `make check-deps` - Validate dependencies
- `make ssl` - Generate SSL certificates

### Development
- `make dev` - Start development environment
- `make dev-full` - Full setup with database
- `make start-backend` - Start Rust backend
- `make start-frontend` - Start Svelte frontend

### Database Management
- `make migrate` - Run database migrations
- `make seed` - Load sample data
- `make db-reset` - Reset database
- `make backup` - Create database backup
- `make restore BACKUP_FILE=filename` - Restore from backup

### Testing & Quality
- `make test` - Run all tests
- `make test-api` - API tests only
- `make test-performance` - Performance tests
- `make lint` - Code linting
- `make format` - Code formatting

### Production
- `make build` - Build Docker images
- `make deploy` - Deploy to production
- `make deploy-staging` - Deploy to staging

### Monitoring & Maintenance
- `make logs` - Show all service logs
- `make logs-backend` - Backend logs only
- `make status` - Service status
- `make monitor` - Health check dashboard
- `make clean` - Clean up everything
- `make restart` - Restart services

### Utilities
- `make shell-backend` - Backend container shell
- `make shell-db` - Database shell
- `make info` - Project information
- `make help` - Show all commands

## 📝 Configuration

### Environment Variables

**Backend (.env)**
```env
DATABASE_URL=postgres://astro_user:astro_password@localhost:5432/astro_ecommerce
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key
WHATSAPP_PHONE_NUMBER=***********
BANK_ACCOUNT_NUMBER=**********
```

**Frontend**
```env
VITE_API_URL=http://localhost:8000/api/v1
```

## 🚀 Deployment

### Production Checklist
- [ ] Set strong JWT secret
- [ ] Configure production database
- [ ] Set up Redis cluster
- [ ] Configure CORS origins
- [ ] Set up SSL certificates
- [ ] Configure file upload storage
- [ ] Set up monitoring and logging

### Docker Production
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📚 Documentation

Comprehensive documentation is organized in the [`docs/`](docs/) directory:

- **🚀 [Deployment](docs/deployment/)** - VPS deployment, Docker setup, production guides
- **⚙️ [Environment](docs/environment/)** - Environment variables, Wrangler setup
- **🎨 [Frontend](docs/frontend/)** - Component implementation, homepage guides
- **🖼️ [Images](docs/images/)** - Image optimization, download procedures
- **🔒 [SSL/HTTPS](docs/ssl/)** - Domain setup, certificate management
- **📖 [Guides](docs/guides/)** - Troubleshooting, 404 fixes

See [`docs/README.md`](docs/README.md) for the complete documentation index.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the comprehensive documentation in [`docs/`](docs/) directory

## 🔄 Version History

- **v1.0.0**: Initial release with core functionality
  - Product catalog management
  - Bundle system
  - Order processing
  - WhatsApp integration
  - Admin authentication

---

Built with ❤️ for Astro Works Indonesia
