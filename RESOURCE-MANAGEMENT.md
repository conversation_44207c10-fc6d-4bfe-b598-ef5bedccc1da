# 🔧 Resource Management Configuration

Konfigurasi untuk mencegah server overload saat deployment dan runtime.

## 📊 Resource Limits yang Diterapkan

### Backend (Rust Application) - Optimized for 1GB VPS
```yaml
deploy:
  resources:
    limits:
      cpus: '0.5'        # Maksimal 50% dari 1 CPU core
      memory: 512M       # Maksimal 512MB RAM (50% dari 1GB)
    reservations:
      cpus: '0.1'        # Reserve 10% dari 1 CPU core
      memory: 256M       # Reserve 256MB RAM
```

### PostgreSQL Database - Optimized for 1GB VPS
```yaml
deploy:
  resources:
    limits:
      cpus: '0.3'        # Maksimal 30% dari 1 CPU core
      memory: 256M       # Maksimal 256MB RAM (25% dari 1GB)
    reservations:
      cpus: '0.05'       # Reserve 5% dari 1 CPU core
      memory: 128M       # Reserve 128MB RAM
```

### Redis Cache - Optimized for 1GB VPS
```yaml
deploy:
  resources:
    limits:
      cpus: '0.15'       # Maksimal 15% dari 1 CPU core
      memory: 128M       # Maksimal 128MB RAM (12.5% dari 1GB)
    reservations:
      cpus: '0.05'       # Reserve 5% dari 1 CPU core
      memory: 64M        # Reserve 64MB RAM
```

## 🏗️ Optimasi Build Process

### Dockerfile Optimizations
- **Limited Build Jobs**: `CARGO_BUILD_JOBS=2`
- **Optimized Compilation**: `--jobs 2` untuk mencegah overload
- **Multi-stage Build**: Memisahkan build dan runtime stage

### Build Environment Variables
```dockerfile
ENV RUSTFLAGS="-C opt-level=3 -C target-cpu=native" \
    CARGO_NET_GIT_FETCH_WITH_CLI=true \
    CARGO_REGISTRIES_CRATES_IO_PROTOCOL=sparse \
    CARGO_BUILD_JOBS=2
```

## 🚀 Deployment Strategies

### 1. Standard Deployment dengan Monitoring
```bash
make deploy-prod
# atau
./scripts/deploy-with-monitoring.sh
```
**Features:**
- Resource monitoring real-time
- Pre-deployment resource check
- Health check post-deployment
- Alert system untuk usage tinggi

### 2. Binary Deployment (Recommended untuk VPS terbatas)
```bash
make deploy-binary
# atau
./scripts/build-and-deploy.sh
```
**Features:**
- Build lokal, deploy binary saja
- Tidak ada kompilasi di VPS
- Resource usage minimal
- Deployment lebih cepat

### 3. Simple Deployment
```bash
make deploy-prod-simple
```
**Features:**
- Deployment standar tanpa monitoring
- Untuk troubleshooting

## 📈 Resource Monitoring

### Real-time Monitoring
Script `deploy-with-monitoring.sh` menyediakan:
- Memory usage tracking
- CPU load monitoring  
- Disk usage check
- Alert system otomatis

### Pre-deployment Checks
- Memory usage > 85% → Warning
- Disk usage > 85% → Warning
- CPU load tinggi → Alert

### During Build Monitoring
```bash
# Output contoh:
14:30:15 - Memory: 67.2% | CPU: 45.3%
14:30:25 - Memory: 72.1% | CPU: 52.8%
🚨 ALERT: Memory usage critical (91.2%)
```

## ⚙️ Configuration Files

### Docker Compose Resource Limits
File: `scripts/deploy/docker-compose.prod.yml`
- Backend: 1GB RAM, 0.8 CPU
- PostgreSQL: 512MB RAM, 0.5 CPU  
- Redis: 256MB RAM, 0.3 CPU

### Dockerfile Optimizations
File: `be_astro/Dockerfile`
- Limited parallel jobs
- Optimized build flags
- Multi-stage build

## 🔍 Troubleshooting

### Server Overload Symptoms
- SSH connection timeout
- Docker build hanging
- High memory/CPU usage
- Slow response times

### Solutions
1. **Use Binary Deployment**:
   ```bash
   make deploy-binary
   ```

2. **Check Resources**:
   ```bash
   # On VPS
   free -h
   top
   df -h
   ```

3. **Monitor During Deployment**:
   ```bash
   # Use monitoring script
   ./scripts/deploy-with-monitoring.sh
   ```

4. **Reduce Resource Usage**:
   - Stop unnecessary services
   - Clear disk space
   - Use swap if needed

### Emergency Commands
```bash
# Stop all containers
docker --context astro-vps stop $(docker --context astro-vps ps -q)

# Check resource usage
docker --context astro-vps stats

# View logs
docker --context astro-vps logs astro_backend_prod --tail 50
```

## 📋 Best Practices

### For VPS with Limited Resources
1. **Always use binary deployment** untuk production
2. **Monitor resources** sebelum deployment
3. **Schedule deployments** saat traffic rendah
4. **Keep swap enabled** untuk emergency
5. **Regular cleanup** untuk disk space

### For Development
1. **Use resource limits** di local development
2. **Test deployment scripts** sebelum production
3. **Monitor build times** dan optimize jika perlu

### Resource Planning
- **Minimum VPS**: 2GB RAM, 2 CPU cores
- **Recommended VPS**: 4GB RAM, 2-4 CPU cores
- **Reserve 20%** resources untuk system overhead

## 🎯 Expected Resource Usage

### During Deployment
- **Memory**: 60-80% peak usage
- **CPU**: 70-90% during compilation
- **Disk**: Temporary spike untuk build cache

### During Runtime
- **Memory**: 30-50% normal usage
- **CPU**: 10-30% normal load
- **Disk**: Stable, logs growth over time

## 📞 Support

Jika mengalami masalah resource management:
1. Check monitoring logs
2. Verify resource limits
3. Use binary deployment method
4. Contact system administrator

---

**Note**: Konfigurasi ini dirancang untuk VPS dengan resource terbatas. Adjust sesuai dengan spesifikasi server Anda.
