# 🚀 Menjalankan Astro Works Secara Local

## ⚠️ Docker Networking Issue Detected

Saat ini ada masalah dengan Docker networking di sistem Anda. Berikut adalah beberapa solusi alternatif:

## 🔧 Solusi 1: Install PostgreSQL & Redis Lokal

### Install PostgreSQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE astro_ecommerce;
CREATE USER astro_user WITH PASSWORD 'astro_password';
GRANT ALL PRIVILEGES ON DATABASE astro_ecommerce TO astro_user;
\q
```

### Install Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### Setup Environment
```bash
# Copy environment file
cp .env.dev be_astro/.env

# Edit jika perlu
nano be_astro/.env
```

### Jalankan Project
```bash
# Terminal 1: Backend
cd be_astro
PORT=7998 cargo run --bin be_astro

# Terminal 2: Frontend
cd fe_astro
bun install  # atau npm install
bun run dev  # atau npm run dev
```

## 🔧 Solusi 2: Fix Docker Networking

### Restart Docker Service
```bash
sudo systemctl stop docker
sudo systemctl start docker

# Atau restart
sudo systemctl restart docker
```

### Reset Docker Network
```bash
# Stop all containers
docker stop $(docker ps -q)

# Remove all containers
docker rm $(docker ps -aq)

# Remove all networks
docker network prune -f

# Restart Docker
sudo systemctl restart docker
```

### Coba Docker Lagi
```bash
# Test basic Docker
docker run hello-world

# Jika berhasil, coba database
docker run -d -p 5432:5432 -e POSTGRES_DB=astro_ecommerce -e POSTGRES_USER=astro_user -e POSTGRES_PASSWORD=astro_password postgres:16-alpine
docker run -d -p 6379:6379 redis:7-alpine
```

## 🔧 Solusi 3: Menggunakan Host Network

Jika masalah networking persisten, coba gunakan host network:

```bash
# PostgreSQL dengan host network
docker run -d --network host -e POSTGRES_DB=astro_ecommerce -e POSTGRES_USER=astro_user -e POSTGRES_PASSWORD=astro_password postgres:16-alpine

# Redis dengan host network
docker run -d --network host redis:7-alpine
```

## 🔧 Solusi 4: Menggunakan Podman (Alternatif Docker)

```bash
# Install Podman
sudo apt install podman

# Gunakan podman sebagai pengganti docker
podman run -d -p 5432:5432 -e POSTGRES_DB=astro_ecommerce -e POSTGRES_USER=astro_user -e POSTGRES_PASSWORD=astro_password postgres:16-alpine
podman run -d -p 6379:6379 redis:7-alpine
```

## 📋 Checklist Setelah Database Berjalan

1. **Cek koneksi database**:
   ```bash
   # Test PostgreSQL
   psql -h localhost -p 5432 -U astro_user -d astro_ecommerce
   
   # Test Redis
   redis-cli ping
   ```

2. **Setup environment**:
   ```bash
   cp .env.dev be_astro/.env
   ```

3. **Install frontend dependencies**:
   ```bash
   cd fe_astro
   bun install  # atau npm install
   ```

4. **Jalankan backend**:
   ```bash
   cd be_astro
   PORT=7998 cargo run --bin be_astro
   ```

5. **Jalankan frontend**:
   ```bash
   cd fe_astro
   bun run dev  # atau npm run dev
   ```

## 🎯 URLs Setelah Setup

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:7998/api/v1
- **Health Check**: http://localhost:7998/health
- **Admin Panel**: http://localhost:5173/manajemen

## 🔐 Kredensial

### Database
- **Host**: localhost
- **Port**: 5432
- **Database**: astro_ecommerce
- **User**: astro_user
- **Password**: astro_password

### Admin Panel
- **Email**: <EMAIL>
- **Password**: admin123

## 🚨 Troubleshooting

### Port sudah digunakan
```bash
# Cek port 5432
sudo lsof -i :5432

# Cek port 6379
sudo lsof -i :6379

# Cek port 7998
sudo lsof -i :7998

# Cek port 5173
sudo lsof -i :5173
```

### Backend tidak bisa connect ke database
```bash
# Cek status PostgreSQL
sudo systemctl status postgresql

# Cek log backend
cd be_astro
RUST_LOG=debug PORT=7998 cargo run --bin be_astro
```

### Frontend error
```bash
cd fe_astro

# Clear cache
rm -rf node_modules
bun install  # atau npm install

# Cek bun
which bun

# Fallback ke npm
npm run dev
```

## 💡 Tips

1. **Gunakan tmux atau screen** untuk menjalankan multiple terminal
2. **Set alias** untuk command yang sering digunakan
3. **Monitor logs** untuk debugging
4. **Backup database** sebelum testing

## 📞 Jika Masih Bermasalah

Jika semua solusi di atas tidak berhasil, kemungkinan ada masalah dengan:
1. Docker daemon configuration
2. Network interface configuration  
3. Firewall settings
4. Kernel modules

Silakan restart sistem atau konsultasi dengan system administrator.
