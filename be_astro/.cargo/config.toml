# Cargo configuration for faster builds
[build]
# Use parallel compilation
jobs = 4

# Use faster linker
[target.x86_64-unknown-linux-gnu]
linker = "clang"
rustflags = ["-C", "link-arg=-fuse-ld=lld"]

# Optimize for faster builds in development
[profile.dev]
# Reduce optimization for faster compilation
opt-level = 0
# Enable debug info but reduce it
debug = 1
# Disable overflow checks for speed
overflow-checks = false
# Use faster codegen
codegen-units = 256
# Disable incremental compilation in Docker
incremental = false

# Production optimizations
[profile.release]
# Maximum optimization
opt-level = 3
# Enable link-time optimization
lto = "fat"
# Reduce binary size
codegen-units = 1
# Enable panic abort for smaller binaries
panic = "abort"
# Strip debug symbols
strip = true

# Fast release profile for testing
[profile.release-fast]
inherits = "release"
opt-level = 2
lto = "thin"
codegen-units = 16

# Registry configuration for faster downloads
[registries.crates-io]
protocol = "sparse"

# Network configuration
[net]
git-fetch-with-cli = true
