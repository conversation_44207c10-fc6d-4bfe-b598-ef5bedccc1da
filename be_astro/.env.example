# =============================================================================
# ASTRO WORKS E-COMMERCE - BACKEND CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and update the values according to your environment
# For production, use strong passwords and secrets

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgres://astro_user:astro_password@localhost:5432/astro_ecommerce
POSTGRES_USER=astro_user
POSTGRES_PASSWORD=astro_password
POSTGRES_DB=astro_ecommerce
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Database Pool Configuration
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5
DB_ACQUIRE_TIMEOUT_SECONDS=30
DB_IDLE_TIMEOUT_SECONDS=600
DB_MAX_LIFETIME_SECONDS=1800

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Cache Configuration
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=100MB

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
HOST=0.0.0.0
PORT=8000
RUST_LOG=debug
ENVIRONMENT=development

# CORS Configuration
CORS_ORIGIN=http://localhost:5173,http://localhost:5174,http://astrokabinet.id,https://astrokabinet.id

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration (CHANGE IN PRODUCTION!)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-min-32-chars
JWT_EXPIRES_IN=24h

# Admin Configuration (CHANGE IN PRODUCTION!)
ADMIN_DEFAULT_EMAIL=<EMAIL>
ADMIN_DEFAULT_PASSWORD=admin123

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=********
UPLOAD_PATH=/app/uploads
STATIC_PATH=/app/static
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,webp

# Image Processing
IMAGE_QUALITY=85
IMAGE_MAX_WIDTH=1920
IMAGE_MAX_HEIGHT=1080
WEBP_QUALITY=65

# =============================================================================
# BUSINESS CONFIGURATION
# =============================================================================
# WhatsApp Configuration
WHATSAPP_BASE_URL=https://wa.me/
WHATSAPP_PHONE_NUMBER=***********

# Bank Transfer Configuration
BANK_NAME=BCA
BANK_ACCOUNT_NUMBER=**********
BANK_ACCOUNT_NAME=Astro Works Indonesia PT

# Company Information
COMPANY_NAME=Astro Works Indonesia
COMPANY_EMAIL=<EMAIL>
COMPANY_ADDRESS=Jakarta, Indonesia

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_LEVEL=debug
LOG_FORMAT=json

# Health Check
HEALTH_CHECK_INTERVAL_SECONDS=30
