[package]
name = "be_astro"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "be_astro"
path = "src/main.rs"

# Exclude all utility binaries for Docker build to avoid dependency issues
# These can be run separately when needed
# [[bin]]
# name = "generate_hash"
# path = "src/bin/generate_hash.rs"
#
# [[bin]]
# name = "convert_images_to_webp"
# path = "src/bin/convert_images_to_webp.rs"
#
# [[bin]]
# name = "cleanup_old_images"
# path = "src/bin/cleanup_old_images.rs"
#
# [[bin]]
# name = "migrate"
# path = "src/bin/migrate.rs"
#
# [[bin]]
# name = "simple_webp_convert"
# path = "src/bin/simple_webp_convert.rs"
#
# [[bin]]
# name = "test_main"
# path = "src/bin/test_main.rs"

# [[bin]]
# name = "test_image_optimization"
# path = "src/bin/test_image_optimization.rs"

# [[bin]]
# name = "migrate"
# path = "src/bin/migrate.rs"

# [[bin]]
# name = "verify_db"
# path = "src/bin/verify_db.rs"

[lib]
name = "be_astro"
path = "src/lib.rs"

[features]
default = []
# Feature to disable SQLx compile-time checking for Docker builds
runtime-only = []

[dependencies]
# Web framework
axum = { version = "0.7.9", features = ["multipart"] }
tokio = { version = "1.0", features = ["full"] }
tower = "0.5"
tower-http = { version = "0.6", features = ["cors", "fs"] }

# Database with SQLx (optimized)
sqlx = { version = "0.7", features = [
    "runtime-tokio-rustls",
    "postgres",
    "chrono",
    "uuid",
    "rust_decimal",
    "macros",
    "migrate"
], default-features = false }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Date/Time
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# Environment variables
dotenvy = "0.15"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Redis client
redis = { version = "0.27", features = ["tokio-comp"] }

# Password hashing (Argon2 - more secure than bcrypt)
argon2 = "0.5"
rand_core = "0.6"

# JWT tokens
jsonwebtoken = "9.0"

# Validation
validator = { version = "0.18", features = ["derive"] }

# URL encoding for WhatsApp links
urlencoding = "2.1"

# Async trait support
async-trait = "0.1"

# Decimal arithmetic
rust_decimal = { version = "1.32", features = ["serde"] }

# Tracing for logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# HTTP client for health checks
reqwest = { version = "0.12", features = ["json"] }

# Random number generation
rand = "0.8"

# Regular expressions for validation
regex = "1.0"

# File upload and multipart form handling (kept for API file uploads)
multer = "3.0"
bytes = "1.0"
futures-util = "0.3"

# Image processing (enhanced configuration)
image = { version = "0.25", features = ["webp", "png", "jpeg"] }
webp = "0.2"
walkdir = "2.4"

# MIME type detection (kept for API file handling)
mime_guess = "2.0"
