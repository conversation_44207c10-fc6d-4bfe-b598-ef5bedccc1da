FROM rust:latest as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Set Rust build env optimized for single CPU
ENV RUSTFLAGS="-C opt-level=s -C target-cpu=generic" \
    CARGO_NET_GIT_FETCH_WITH_CLI=true \
    CARGO_REGISTRIES_CRATES_IO_PROTOCOL=sparse \
    CARGO_BUILD_JOBS=1

# Copy manifests
COPY Cargo.toml Cargo.lock ./

# Prebuild dependencies with dummy files (single job for 1 CPU)
RUN mkdir src && echo "fn main() {}" > src/main.rs && echo "" > src/lib.rs
RUN cargo build --release --jobs 1 && rm -rf src

# Copy real source
COPY src ./src

# Final app build (single job for 1 CPU VPS) with debug info
RUN cargo build --release --jobs 1

# --- Runtime stage ---
FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    libpq5 \
    libssl3 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY --from=builder /app/target/release/be_astro /app/be_astro

RUN useradd -r -s /bin/false astro \
    && chown -R astro:astro /app

USER astro

EXPOSE 7998

HEALTHCHECK --interval=30s --timeout=5s \
  CMD curl -f http://localhost:7998/health || exit 1

CMD ["./be_astro"]
