# Debug Dockerfile for troubleshooting
FROM rust:latest

# Install dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    strace \
    gdb \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy source
COPY . .

# Build with debug info
ENV RUST_LOG=debug
ENV RUST_BACKTRACE=1
ENV SQLX_OFFLINE=true
RUN cargo build --release

EXPOSE 7998

# Add debug script
RUN echo '#!/bin/bash\necho "Starting debug container..."\necho "Environment variables:"\nenv | grep -E "(DATABASE_URL|REDIS_URL|HOST|PORT|RUST_)"\necho "Binary info:"\nls -la target/release/be_astro\necho "Starting application..."\nexec ./target/release/be_astro' > /app/debug.sh && chmod +x /app/debug.sh

CMD ["/app/debug.sh"]
