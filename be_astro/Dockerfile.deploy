# Lightweight deployment image - no compilation needed
FROM debian:bookworm-slim

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    libpq5 \
    libssl3 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy pre-built binary
COPY be_astro /app/be_astro

# Create user
RUN useradd -r -s /bin/false astro \
    && chown -R astro:astro /app \
    && chmod +x /app/be_astro

USER astro

EXPOSE 7998

HEALTHCHECK --interval=30s --timeout=5s \
  CMD curl -f http://localhost:7998/health || exit 1

CMD ["./be_astro"]
