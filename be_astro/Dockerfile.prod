# Multi-stage build for production
FROM rust:latest as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Set Rust compilation optimizations for faster builds
ENV CARGO_NET_GIT_FETCH_WITH_CLI=true
ENV CARGO_REGISTRIES_CRATES_IO_PROTOCOL=sparse
ENV RUSTFLAGS="-C target-cpu=native -C opt-level=3"
# Disable SQLx compile-time checking for Docker builds
# Use runtime queries instead of compile-time checked queries
ENV SQLX_OFFLINE=false

# Copy manifests
COPY Cargo.toml Cargo.lock ./

# Create dummy source files to build dependencies first
RUN mkdir src && \
    echo "fn main() {}" > src/main.rs && \
    echo "// Dummy lib.rs for dependency build" > src/lib.rs

# Build dependencies (this layer will be cached)
RUN cargo build --release --bin be_astro && rm -rf src

# Copy source code
COPY src ./src

# Build for release
RUN cargo build --release && \
    cp target/release/be_astro /tmp/be_astro

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /tmp/be_astro /app/be_astro

# Copy static files
COPY static /app/static

# Create directories for uploads and data
RUN mkdir -p /app/static/uploads/products /app/static/images /app/logs /app/data

# Create non-root user
RUN useradd -r -s /bin/false astro \
    && chown -R astro:astro /app

USER astro

EXPOSE 7998

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
    CMD curl -f http://localhost:7998/health || exit 1

CMD ["./be_astro"]
