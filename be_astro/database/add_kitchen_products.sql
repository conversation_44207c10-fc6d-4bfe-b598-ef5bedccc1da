-- Add some kitchen products to demonstrate all categories

-- Insert kitchen products
INSERT INTO products (id, name, description, base_price, is_featured, is_active, image_url, specifications, dimensions, created_at, updated_at) VALUES
(gen_random_uuid(), 'Modern Kitchen Set', 'Complete kitchen set with premium materials and modern design', 2500000.00, true, true, '/images/products/modern-kitchen-set.jpg', '{"material": "Plywood Melamine", "finish": "Soft Close", "style": "Modern"}', '{"width": "3.0m", "height": "2.4m", "depth": "0.6m"}', NOW(), NOW()),
(gen_random_uuid(), 'Kitchen Island Premium', 'Luxury kitchen island with marble top and built-in storage', 5200000.00, false, true, '/images/products/kitchen-island-premium.jpg', '{"material": "Marble Top", "finish": "Premium", "style": "Luxury"}', '{"width": "2.0m", "height": "0.9m", "depth": "1.0m"}', NOW(), NOW()),
(gen_random_uuid(), 'Pantry Cabinet Large', 'Spacious pantry cabinet with multiple shelves and soft-close doors', 1800000.00, false, true, '/images/products/pantry-cabinet-large.jpg', '{"material": "Plywood", "finish": "Soft Close", "style": "Modern"}', '{"width": "1.2m", "height": "2.4m", "depth": "0.6m"}', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Assign kitchen products to Kitchen category
DO $$
DECLARE
    kitchen_id UUID;
    modern_kitchen_id UUID;
    kitchen_island_id UUID;
    pantry_cabinet_id UUID;
BEGIN
    -- Get category ID
    SELECT id INTO kitchen_id FROM categories WHERE name = 'Kitchen';
    
    -- Get product IDs
    SELECT id INTO modern_kitchen_id FROM products WHERE name = 'Modern Kitchen Set';
    SELECT id INTO kitchen_island_id FROM products WHERE name = 'Kitchen Island Premium';
    SELECT id INTO pantry_cabinet_id FROM products WHERE name = 'Pantry Cabinet Large';
    
    -- Assign products to Kitchen category
    IF kitchen_id IS NOT NULL THEN
        IF modern_kitchen_id IS NOT NULL THEN
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (modern_kitchen_id, kitchen_id)
            ON CONFLICT (product_id, category_id) DO NOTHING;
            RAISE NOTICE 'Assigned Modern Kitchen Set to Kitchen category';
        END IF;
        
        IF kitchen_island_id IS NOT NULL THEN
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (kitchen_island_id, kitchen_id)
            ON CONFLICT (product_id, category_id) DO NOTHING;
            RAISE NOTICE 'Assigned Kitchen Island Premium to Kitchen category';
        END IF;
        
        IF pantry_cabinet_id IS NOT NULL THEN
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (pantry_cabinet_id, kitchen_id)
            ON CONFLICT (product_id, category_id) DO NOTHING;
            RAISE NOTICE 'Assigned Pantry Cabinet Large to Kitchen category';
        END IF;
    END IF;
    
    RAISE NOTICE 'Kitchen products added and assigned successfully';
END $$;
