-- Add slug column and populate slugs for all products
-- This script adds slug column if it doesn't exist and generates slugs for products

-- Add slug column if it doesn't exist
ALTER TABLE products ADD COLUMN IF NOT EXISTS slug VARCHAR(255) UNIQUE;

-- Create index for slug
CREATE INDEX IF NOT EXISTS idx_products_slug ON products(slug);

-- Add slugs to existing products
UPDATE products SET slug = 'fantasy-tv-cabinet' WHERE name = 'Fantasy TV Cabinet' AND slug IS NULL;
UPDATE products SET slug = 'glass-door' WHERE name = 'Glass Door' AND slug IS NULL;
UPDATE products SET slug = 'soft-close-hinges' WHERE name = 'Soft Close Hinges' AND slug IS NULL;
UPDATE products SET slug = 'pull-out-basket' WHERE name = 'Pull Out Basket' AND slug IS NULL;
UPDATE products SET slug = 'cabinet-lighting' WHERE name = 'Cabinet Lighting' AND slug IS NULL;
UPDATE products SET slug = 'led-gola' WHERE name = 'Led Gola' AND slug IS NULL;
UPDATE products SET slug = 'tandem-box' WHERE name = 'Tandem Box' AND slug IS NULL;
UPDATE products SET slug = 'modern-kitchen-set' WHERE name = 'Modern Kitchen Set' AND slug IS NULL;
UPDATE products SET slug = 'kitchen-island-premium' WHERE name = 'Kitchen Island Premium' AND slug IS NULL;
UPDATE products SET slug = 'pantry-cabinet-large' WHERE name = 'Pantry Cabinet Large' AND slug IS NULL;

-- Verify the updates
SELECT name, slug FROM products ORDER BY name;
