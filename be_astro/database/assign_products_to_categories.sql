-- Assign existing products to appropriate categories
-- This script assigns products to categories based on their names

-- Get category IDs
DO $$
DECLARE
    tv_cabinet_id UUID;
    kitchen_id UUID;
    accessories_id UUID;
    
    -- Product IDs
    fantasy_tv_cabinet_id UUID;
    glass_door_id UUID;
    soft_close_hinges_id UUID;
    pull_out_basket_id UUID;
    cabinet_lighting_id UUID;
    led_gola_id UUID;
    tandem_box_id UUID;
BEGIN
    -- Get category IDs
    SELECT id INTO tv_cabinet_id FROM categories WHERE name = 'TV Cabinet';
    SELECT id INTO kitchen_id FROM categories WHERE name = 'Kitchen';
    SELECT id INTO accessories_id FROM categories WHERE name = 'Accessories';
    
    -- Get product IDs
    SELECT id INTO fantasy_tv_cabinet_id FROM products WHERE name = 'Fantasy TV Cabinet';
    SELECT id INTO glass_door_id FROM products WHERE name = 'Glass Door';
    SELECT id INTO soft_close_hinges_id FROM products WHERE name = 'Soft Close Hinges';
    SELECT id INTO pull_out_basket_id FROM products WHERE name = 'Pull Out Basket';
    SELECT id INTO cabinet_lighting_id FROM products WHERE name = 'Cabinet Lighting';
    SELECT id INTO led_gola_id FROM products WHERE name = 'Led Gola';
    SELECT id INTO tandem_box_id FROM products WHERE name = 'Tandem Box';
    
    -- Assign Fantasy TV Cabinet to TV Cabinet category
    IF fantasy_tv_cabinet_id IS NOT NULL AND tv_cabinet_id IS NOT NULL THEN
        INSERT INTO product_categories (product_id, category_id) 
        VALUES (fantasy_tv_cabinet_id, tv_cabinet_id)
        ON CONFLICT (product_id, category_id) DO NOTHING;
        RAISE NOTICE 'Assigned Fantasy TV Cabinet to TV Cabinet category';
    END IF;
    
    -- Assign accessories to Accessories category
    IF accessories_id IS NOT NULL THEN
        -- Glass Door
        IF glass_door_id IS NOT NULL THEN
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (glass_door_id, accessories_id)
            ON CONFLICT (product_id, category_id) DO NOTHING;
            RAISE NOTICE 'Assigned Glass Door to Accessories category';
        END IF;
        
        -- Soft Close Hinges
        IF soft_close_hinges_id IS NOT NULL THEN
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (soft_close_hinges_id, accessories_id)
            ON CONFLICT (product_id, category_id) DO NOTHING;
            RAISE NOTICE 'Assigned Soft Close Hinges to Accessories category';
        END IF;
        
        -- Pull Out Basket
        IF pull_out_basket_id IS NOT NULL THEN
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (pull_out_basket_id, accessories_id)
            ON CONFLICT (product_id, category_id) DO NOTHING;
            RAISE NOTICE 'Assigned Pull Out Basket to Accessories category';
        END IF;
        
        -- Cabinet Lighting
        IF cabinet_lighting_id IS NOT NULL THEN
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (cabinet_lighting_id, accessories_id)
            ON CONFLICT (product_id, category_id) DO NOTHING;
            RAISE NOTICE 'Assigned Cabinet Lighting to Accessories category';
        END IF;
        
        -- Led Gola
        IF led_gola_id IS NOT NULL THEN
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (led_gola_id, accessories_id)
            ON CONFLICT (product_id, category_id) DO NOTHING;
            RAISE NOTICE 'Assigned Led Gola to Accessories category';
        END IF;
        
        -- Tandem Box
        IF tandem_box_id IS NOT NULL THEN
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (tandem_box_id, accessories_id)
            ON CONFLICT (product_id, category_id) DO NOTHING;
            RAISE NOTICE 'Assigned Tandem Box to Accessories category';
        END IF;
    END IF;
    
    RAISE NOTICE 'Product category assignments completed';
END $$;
