-- Sample data for Astro Works E-commerce Database
-- Run this after migrations to populate the database with test data

-- Insert admin users
INSERT INTO admin_users (id, email, password_hash, name, is_active, created_at, updated_at) VALUES
(gen_random_uuid(), '<EMAIL>', '$2b$12$LQv3c1yqBwLFaAudPNr0SElLhDdkykjFHL7fzAhbnpyxibHiQlaBO', 'Admin User', true, NOW(), NOW()),
(gen_random_uuid(), '<EMAIL>', '$2b$12$LQv3c1yqBwLFaAudPNr0SElLhDdkykjFHL7fzAhbnpyxibHiQlaBO', 'Manager User', true, NOW(), NOW());
-- Password for both users is: admin123

-- Insert main categories
INSERT INTO categories (id, name, slug, description, parent_id, is_accessory, sort_order, is_active, created_at, updated_at) VALUES
(gen_random_uuid(), 'Kitchen', 'kitchen', 'Kitchen furniture and cabinets', NULL, false, 1, true, NOW(), NOW()),
(gen_random_uuid(), 'Wardrobe', 'wardrobe', 'Wardrobe and closet solutions', NULL, false, 2, true, NOW(), NOW()),
(gen_random_uuid(), 'TV Cabinet', 'tv-cabinet', 'TV cabinets and entertainment units', NULL, false, 3, true, NOW(), NOW()),
(gen_random_uuid(), 'Accessories', 'accessories', 'Furniture accessories and add-ons', NULL, true, 4, true, NOW(), NOW());

-- Insert accessory subcategories
INSERT INTO categories (id, name, slug, description, parent_id, is_accessory, sort_order, is_active, created_at, updated_at) VALUES
(gen_random_uuid(), 'LED Strips', 'led-strips', 'LED lighting strips for furniture', (SELECT id FROM categories WHERE slug = 'accessories'), true, 1, true, NOW(), NOW()),
(gen_random_uuid(), 'Tandem Box', 'tandem-box', 'Drawer systems and organizers', (SELECT id FROM categories WHERE slug = 'accessories'), true, 2, true, NOW(), NOW()),
(gen_random_uuid(), 'Hardware', 'hardware', 'Furniture hardware and fittings', (SELECT id FROM categories WHERE slug = 'accessories'), true, 3, true, NOW(), NOW());

-- Insert main products
INSERT INTO products (id, name, slug, description, short_description, sku, price, unit, minimum_quantity, specifications, dimensions, weight, is_active, is_featured, sort_order, meta_title, meta_description, created_at, updated_at) VALUES
(gen_random_uuid(), 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola', 'fantasy-tv-cabinet-hitam-gola', 'Kabinet dapur ukuran 2x3m, Body plywood melamine, Pintu Plywood Soft Close, aksesoris box laci aluminium soft closing, dilengkapi laci 5ml, dad dasd dasdas dasd dasdadasd dasdadasd', 'Premium TV cabinet with modern design', 'FTV-001', 1590000.00, 'pieces', 1, '{"color": "Hitam", "theme": "Gola", "material": "Plywood Melamine", "finish": "Soft Close"}', '{"width": "2.4m", "height": "2.7m", "depth": "0.6m"}', 50.00, true, true, 1, 'Fantasy TV Cabinet - Premium Furniture', 'High-quality TV cabinet with modern design and premium materials', NOW(), NOW()),

(gen_random_uuid(), 'Fantasy Pantry Cabinet', 'fantasy-pantry-cabinet', 'Premium pantry cabinet for kitchen storage', 'Spacious pantry cabinet with multiple shelves', 'FPC-001', 2500000.00, 'pieces', 1, '{"material": "Plywood Melamine", "shelves": 5, "finish": "Soft Close"}', '{"width": "1.2m", "height": "2.4m", "depth": "0.6m"}', 40.00, true, true, 2, 'Fantasy Pantry Cabinet - Kitchen Storage', 'Premium pantry cabinet for organized kitchen storage', NOW(), NOW()),

(gen_random_uuid(), 'SORA Kitchen Set', 'sora-kitchen-set', 'Complete kitchen set with modern design', 'All-in-one kitchen solution', 'SKS-001', 15000000.00, 'set', 1, '{"style": "Modern", "material": "Premium Plywood", "includes": "Upper and lower cabinets"}', '{"width": "3.0m", "height": "2.4m", "depth": "0.6m"}', 200.00, true, true, 3, 'SORA Kitchen Set - Complete Kitchen Solution', 'Modern kitchen set with premium materials and design', NOW(), NOW());

-- Insert accessory products
INSERT INTO products (id, name, slug, description, short_description, sku, price, unit, minimum_quantity, specifications, dimensions, weight, is_active, is_featured, sort_order, meta_title, meta_description, created_at, updated_at) VALUES
(gen_random_uuid(), 'Led ambalan', 'led-ambalan', 'LED strip lighting for furniture', 'Flexible LED strip for cabinet lighting', 'LED-001', 1000000.00, 'meters', 1, '{"type": "LED Strip", "color": "Warm White", "voltage": "12V"}', '{"length": "1m", "width": "10mm"}', 0.1, true, false, 1, 'LED Strip Lighting', 'High-quality LED strips for furniture lighting', NOW(), NOW()),

(gen_random_uuid(), 'Led Gola', 'led-gola', 'Premium LED strip for Gola design', 'Specialized LED for Gola cabinet design', 'LED-002', 3000000.00, 'meters', 1, '{"type": "Gola LED", "color": "Cool White", "voltage": "24V"}', '{"length": "1m", "width": "15mm"}', 0.15, true, false, 2, 'Gola LED Strip', 'Premium LED strips for Gola design cabinets', NOW(), NOW()),

(gen_random_uuid(), 'Tandem box', 'tandem-box', 'Premium drawer system with soft close', 'High-quality drawer organizer', 'TB-001', 1500000.00, 'pieces', 1, '{"type": "Soft Close", "material": "Metal", "capacity": "35kg"}', '{"width": "500mm", "depth": "450mm", "height": "150mm"}', 2.5, true, false, 3, 'Tandem Box Drawer System', 'Premium soft-close drawer system for cabinets', NOW(), NOW()),

(gen_random_uuid(), 'Stone Top Table', 'stone-top-table', 'Premium stone surface table top', 'Durable stone table surface', 'STT-001', 1500000.00, 'pieces', 1, '{"material": "Natural Stone", "finish": "Polished", "thickness": "20mm"}', '{"width": "1200mm", "depth": "600mm", "thickness": "20mm"}', 25.0, true, false, 4, 'Stone Top Table', 'Premium natural stone table top surface', NOW(), NOW()),

-- Additional accessories to match homepage mock data
(gen_random_uuid(), 'Glass Door', 'glass-door', 'Tempered glass door panels for elegant cabinet fronts', 'Premium glass cabinet doors', 'GD-001', 320000.00, 'pieces', 1, '{"material": "Tempered Glass", "thickness": "6mm", "frame": "Aluminum"}', '{"width": "400mm", "height": "600mm", "thickness": "6mm"}', 3.5, true, false, 5, 'Glass Cabinet Doors', 'Tempered glass door panels for elegant cabinet fronts', NOW(), NOW()),

(gen_random_uuid(), 'Soft Close Hinges', 'soft-close-hinges', 'Premium soft-close hinges for cabinet doors', 'High-quality cabinet hinges', 'SCH-001', 150000.00, 'pieces', 1, '{"type": "Soft Close", "material": "Steel", "angle": "110°"}', '{"length": "100mm", "width": "35mm"}', 0.2, true, false, 6, 'Soft Close Cabinet Hinges', 'Premium soft-close hinges for cabinet doors', NOW(), NOW()),

(gen_random_uuid(), 'Pull Out Basket', 'pull-out-basket', 'Wire basket organizer with smooth sliding mechanism', 'Cabinet storage basket', 'POB-001', 280000.00, 'pieces', 1, '{"material": "Stainless Steel", "type": "Pull Out", "capacity": "15kg"}', '{"width": "450mm", "depth": "400mm", "height": "150mm"}', 1.8, true, false, 7, 'Pull Out Storage Basket', 'Wire basket organizer with smooth sliding mechanism', NOW(), NOW()),

(gen_random_uuid(), 'Cabinet Lighting', 'cabinet-lighting', 'Under-cabinet LED lighting system with dimmer control', 'LED cabinet lighting', 'CL-001', 350000.00, 'pieces', 1, '{"type": "LED Strip", "color": "Warm White", "dimmer": "Yes", "voltage": "12V"}', '{"length": "500mm", "width": "12mm"}', 0.3, true, false, 8, 'Cabinet LED Lighting', 'Under-cabinet LED lighting system with dimmer control', NOW(), NOW());

-- Link products to categories
INSERT INTO product_categories (id, product_id, category_id, created_at) VALUES
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), (SELECT id FROM categories WHERE slug = 'tv-cabinet'), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FPC-001'), (SELECT id FROM categories WHERE slug = 'kitchen'), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'SKS-001'), (SELECT id FROM categories WHERE slug = 'kitchen'), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'LED-001'), (SELECT id FROM categories WHERE slug = 'led-strips'), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'LED-002'), (SELECT id FROM categories WHERE slug = 'led-strips'), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'TB-001'), (SELECT id FROM categories WHERE slug = 'tandem-box'), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'STT-001'), (SELECT id FROM categories WHERE slug = 'hardware'), NOW());

-- Insert product images
INSERT INTO product_images (id, product_id, image_url, alt_text, is_primary, sort_order, created_at, updated_at) VALUES
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), '/images/products/fantasy-tv-cabinet-1.jpg', 'Fantasy TV Cabinet - Front View', true, 1, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), '/images/products/fantasy-tv-cabinet-2.jpg', 'Fantasy TV Cabinet - Side View', false, 2, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), '/images/products/fantasy-tv-cabinet-3.jpg', 'Fantasy TV Cabinet - Interior', false, 3, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FPC-001'), '/images/products/fantasy-pantry-1.jpg', 'Fantasy Pantry Cabinet', true, 1, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'SKS-001'), '/images/products/sora-kitchen-1.jpg', 'SORA Kitchen Set', true, 1, NOW(), NOW());

-- Create product bundles
INSERT INTO product_bundles (id, name, description, main_product_id, bundle_price, discount_amount, discount_percentage, is_active, sort_order, created_at, updated_at) VALUES
(gen_random_uuid(), 'Fantasy TV Cabinet Complete Package', 'TV Cabinet with LED strips and accessories', (SELECT id FROM products WHERE sku = 'FTV-001'), 94900000.00, 0, 5.00, true, 1, NOW(), NOW()),
(gen_random_uuid(), 'Kitchen Set Astro Complete', 'Complete kitchen set with all accessories', (SELECT id FROM products WHERE sku = 'SKS-001'), NULL, 1000000.00, 0, true, 2, NOW(), NOW());

-- Add bundle items
INSERT INTO bundle_items (id, bundle_id, product_id, quantity, unit_price, is_optional, sort_order, created_at, updated_at) VALUES
-- Fantasy TV Cabinet Bundle
(gen_random_uuid(), (SELECT id FROM product_bundles WHERE name = 'Fantasy TV Cabinet Complete Package'), (SELECT id FROM products WHERE sku = 'LED-001'), 10, 1000000.00, false, 1, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM product_bundles WHERE name = 'Fantasy TV Cabinet Complete Package'), (SELECT id FROM products WHERE sku = 'LED-002'), 2, 3000000.00, false, 2, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM product_bundles WHERE name = 'Fantasy TV Cabinet Complete Package'), (SELECT id FROM products WHERE sku = 'TB-001'), 3, 1500000.00, false, 3, NOW(), NOW()),

-- Kitchen Set Bundle
(gen_random_uuid(), (SELECT id FROM product_bundles WHERE name = 'Kitchen Set Astro Complete'), (SELECT id FROM products WHERE sku = 'LED-001'), 5, 1000000.00, true, 1, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM product_bundles WHERE name = 'Kitchen Set Astro Complete'), (SELECT id FROM products WHERE sku = 'STT-001'), 1, 1500000.00, true, 2, NOW(), NOW());

-- Insert sample orders
INSERT INTO orders (id, order_number, customer_name, customer_email, customer_phone, customer_address, customer_city, customer_postal_code, customer_province, subtotal_amount, discount_amount, shipping_amount, total_amount, status, notes, whatsapp_url, payment_proof, created_at, updated_at) VALUES
(gen_random_uuid(), '************', 'John Doe', '<EMAIL>', '081234567890', 'Jl. Contoh No. 123', 'Jakarta', '12345', 'DKI Jakarta', 94900000.00, 0, 500000.00, 95400000.00, 'pending', 'Mohon konfirmasi ketersediaan barang', NULL, NULL, NOW(), NOW()),
(gen_random_uuid(), '************', 'Jane Smith', '<EMAIL>', '081234567891', 'Jl. Sample No. 456', 'Bandung', '54321', 'Jawa Barat', 15000000.00, 0, 750000.00, 15750000.00, 'confirmed', NULL, NULL, NULL, NOW(), NOW());

-- Insert product dimensions
INSERT INTO product_dimensions (id, product_id, name, width, height, depth, unit, price_modifier_type, price_modifier_value, is_default, sort_order, is_active, created_at, updated_at) VALUES
-- Fantasy TV Cabinet dimensions
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), '2.4 x 2.7m Standard', 2.4, 2.7, 0.6, 'm', 'addition', 0, true, 1, true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), '3.0 x 2.7m Large', 3.0, 2.7, 0.6, 'm', 'addition', 500000.00, false, 2, true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), '2.0 x 2.4m Compact', 2.0, 2.4, 0.6, 'm', 'subtraction', 300000.00, false, 3, true, NOW(), NOW()),

-- SORA Kitchen Set dimensions
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'SKS-001'), '3.0m Standard', 3.0, 2.4, 0.6, 'm', 'addition', 0, true, 1, true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'SKS-001'), '4.0m Extended', 4.0, 2.4, 0.6, 'm', 'addition', 3000000.00, false, 2, true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'SKS-001'), '2.5m Compact', 2.5, 2.4, 0.6, 'm', 'subtraction', 2000000.00, false, 3, true, NOW(), NOW());

-- Insert product themes
INSERT INTO product_themes (id, product_id, name, description, color_code, image_url, price_modifier_type, price_modifier_value, is_default, sort_order, is_active, created_at, updated_at) VALUES
-- Fantasy TV Cabinet themes
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), 'Hitam Gola', 'Desain modern dengan aksen gola hitam', '#2D2D2D', NULL, 'addition', 0, true, 1, true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), 'Walnut Premium', 'Finishing kayu walnut premium', '#8B4513', NULL, 'addition', 1000000.00, false, 2, true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), 'White Minimalist', 'Desain putih minimalis modern', '#FFFFFF', NULL, 'addition', 500000.00, false, 3, true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'FTV-001'), 'Oak Natural', 'Finishing kayu oak natural', '#DEB887', NULL, 'addition', 750000.00, false, 4, true, NOW(), NOW()),

-- SORA Kitchen Set themes
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'SKS-001'), 'White Classic', 'Putih klasik dengan aksen chrome', '#FFFFFF', NULL, 'addition', 0, true, 1, true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'SKS-001'), 'Grey Modern', 'Abu-abu modern dengan handle stainless', '#808080', NULL, 'addition', 1500000.00, false, 2, true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE sku = 'SKS-001'), 'Wood Grain', 'Motif serat kayu premium', '#8B4513', NULL, 'addition', 2000000.00, false, 3, true, NOW(), NOW());

-- Insert order items
INSERT INTO order_items (id, order_id, product_id, bundle_id, product_name, product_sku, quantity, unit, unit_price, total_price, product_specifications, created_at, updated_at) VALUES
-- Order 1 items (Bundle)
(gen_random_uuid(), (SELECT id FROM orders WHERE order_number = '************'), NULL, (SELECT id FROM product_bundles WHERE name = 'Fantasy TV Cabinet Complete Package'), 'Fantasy TV Cabinet Complete Package', 'FTV-BUNDLE-001', 1, 'bundle', 94900000.00, 94900000.00, '{"bundle_items": "TV Cabinet + LED + Accessories"}', NOW(), NOW()),

-- Order 2 items (Individual product with configuration)
(gen_random_uuid(), (SELECT id FROM orders WHERE order_number = '************'), (SELECT id FROM products WHERE sku = 'SKS-001'), NULL, 'SORA Kitchen Set', 'SKS-001', 1, 'set', 15000000.00, 15000000.00, '{"dimension": "3.0m Standard", "theme": "White Classic", "configuration_id": null}', NOW(), NOW());
