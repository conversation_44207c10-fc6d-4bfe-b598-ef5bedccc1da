-- Simple sample data for Astro Works E-commerce Database
-- This matches the current simplified schema

-- Insert admin users
INSERT INTO admin_users (id, username, email, password_hash, is_active, created_at, updated_at) VALUES
(gen_random_uuid(), 'admin', '<EMAIL>', '$2b$12$LQv3c1yqBwLFaAudPNr0SElLhDdkykjFHL7fzAhbnpyxibHiQlaBO', true, NOW(), NOW()),
(gen_random_uuid(), 'manager', '<EMAIL>', '$2b$12$LQv3c1yqBwLFaAudPNr0SElLhDdkykjFHL7fzAhbnpyxibHiQlaBO', true, NOW(), NOW());
-- Password for both users is: admin123

-- Insert main categories
INSERT INTO categories (id, name, description, parent_id, is_active, created_at, updated_at) VALUES
(gen_random_uuid(), 'Kitchen', 'Kitchen furniture and cabinets', NULL, true, NOW(), NOW()),
(gen_random_uuid(), 'Wardrobe', 'Wardrobe and closet solutions', NULL, true, NOW(), NOW()),
(gen_random_uuid(), 'TV Cabinet', 'TV cabinets and entertainment units', NULL, true, NOW(), NOW()),
(gen_random_uuid(), 'Accessories', 'Furniture accessories and add-ons', NULL, true, NOW(), NOW());

-- Insert main products
INSERT INTO products (id, name, description, base_price, is_featured, is_active, image_url, specifications, dimensions, created_at, updated_at) VALUES
(gen_random_uuid(), 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola', 'Kabinet TV ukuran 2.4x2.7m, Body plywood melamine, Pintu Plywood Soft Close, aksesoris box laci aluminium soft closing, dilengkapi laci 5ml', 15900000.00, true, true, '/images/products/fantasy-tv-cabinet.jpg', '{"color": "Hitam", "theme": "Gola", "material": "Plywood Melamine", "finish": "Soft Close"}', '{"width": "2.4m", "height": "2.7m", "depth": "0.6m"}', NOW(), NOW()),

(gen_random_uuid(), 'Fantasy Pantry Cabinet', 'Premium pantry cabinet for kitchen storage with multiple shelves and soft-close doors', 25000000.00, true, true, '/images/products/fantasy-pantry.jpg', '{"material": "Plywood Melamine", "shelves": 5, "finish": "Soft Close"}', '{"width": "1.2m", "height": "2.4m", "depth": "0.6m"}', NOW(), NOW()),

(gen_random_uuid(), 'SORA Kitchen Set', 'Complete kitchen set with modern design, includes upper and lower cabinets', 150000000.00, true, true, '/images/products/sora-kitchen.jpg', '{"style": "Modern", "material": "Premium Plywood", "includes": "Upper and lower cabinets"}', '{"width": "3.0m", "height": "2.4m", "depth": "0.6m"}', NOW(), NOW());

-- Insert accessory products
INSERT INTO products (id, name, description, base_price, is_featured, is_active, image_url, specifications, dimensions, created_at, updated_at) VALUES
(gen_random_uuid(), 'Led Ambalan', 'LED strip lighting for furniture, flexible and energy efficient', 10000000.00, false, true, '/images/accessories/led-ambalan.jpg', '{"type": "LED Strip", "color": "Warm White", "voltage": "12V"}', '{"length": "1m", "width": "10mm"}', NOW(), NOW()),

(gen_random_uuid(), 'Led Gola', 'Premium LED strip for Gola design, specialized lighting for modern cabinets', 30000000.00, false, true, '/images/accessories/led-gola.jpg', '{"type": "Gola LED", "color": "Cool White", "voltage": "24V"}', '{"length": "1m", "width": "15mm"}', NOW(), NOW()),

(gen_random_uuid(), 'Tandem Box', 'Premium drawer system with soft close mechanism, high-quality metal construction', 15000000.00, false, true, '/images/accessories/tandem-box.jpg', '{"type": "Soft Close", "material": "Metal", "capacity": "35kg"}', '{"width": "500mm", "depth": "450mm", "height": "150mm"}', NOW(), NOW()),

(gen_random_uuid(), 'Stone Top Table', 'Premium stone surface table top, durable and elegant', 15000000.00, false, true, '/images/accessories/stone-top.jpg', '{"material": "Natural Stone", "finish": "Polished", "thickness": "20mm"}', '{"width": "1200mm", "depth": "600mm", "thickness": "20mm"}', NOW(), NOW());

-- Link products to categories (simplified)
INSERT INTO product_categories (product_id, category_id, created_at) VALUES
((SELECT id FROM products WHERE name = 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola'), (SELECT id FROM categories WHERE name = 'TV Cabinet'), NOW()),
((SELECT id FROM products WHERE name = 'Fantasy Pantry Cabinet'), (SELECT id FROM categories WHERE name = 'Kitchen'), NOW()),
((SELECT id FROM products WHERE name = 'SORA Kitchen Set'), (SELECT id FROM categories WHERE name = 'Kitchen'), NOW()),
((SELECT id FROM products WHERE name = 'Led Ambalan'), (SELECT id FROM categories WHERE name = 'Accessories'), NOW()),
((SELECT id FROM products WHERE name = 'Led Gola'), (SELECT id FROM categories WHERE name = 'Accessories'), NOW()),
((SELECT id FROM products WHERE name = 'Tandem Box'), (SELECT id FROM categories WHERE name = 'Accessories'), NOW()),
((SELECT id FROM products WHERE name = 'Stone Top Table'), (SELECT id FROM categories WHERE name = 'Accessories'), NOW());

-- Insert product dimensions (simplified)
INSERT INTO product_dimensions (id, product_id, name, price_modifier_type, price_modifier_value, is_default, sort_order, created_at, updated_at) VALUES
-- Fantasy TV Cabinet dimensions
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola'), '2.4 x 2.7m Standard', 'addition', 0, true, 1, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola'), '3.0 x 2.7m Large', 'addition', 5000000.00, false, 2, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola'), '2.0 x 2.4m Compact', 'subtraction', 3000000.00, false, 3, NOW(), NOW()),

-- SORA Kitchen Set dimensions
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'SORA Kitchen Set'), '3.0m Standard', 'addition', 0, true, 1, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'SORA Kitchen Set'), '4.0m Extended', 'addition', 30000000.00, false, 2, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'SORA Kitchen Set'), '2.5m Compact', 'subtraction', 20000000.00, false, 3, NOW(), NOW());

-- Insert product themes (simplified)
INSERT INTO product_themes (id, product_id, name, price_modifier_type, price_modifier_value, is_default, sort_order, created_at, updated_at) VALUES
-- Fantasy TV Cabinet themes
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola'), 'Hitam Gola', 'addition', 0, true, 1, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola'), 'Walnut Premium', 'addition', 10000000.00, false, 2, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola'), 'White Minimalist', 'addition', 5000000.00, false, 3, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'Fantasy TV Cabinet Hitam 2,4 x 2,7m Gola'), 'Oak Natural', 'addition', 7500000.00, false, 4, NOW(), NOW()),

-- SORA Kitchen Set themes
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'SORA Kitchen Set'), 'White Classic', 'addition', 0, true, 1, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'SORA Kitchen Set'), 'Grey Modern', 'addition', 15000000.00, false, 2, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM products WHERE name = 'SORA Kitchen Set'), 'Wood Grain', 'addition', 20000000.00, false, 3, NOW(), NOW());
