-- Update all products to use WebP images
-- This script updates image_url to use WebP format and proper paths

-- Update products with proper WebP image paths
UPDATE products SET image_url = '/static/uploads/products/4x5_' || slug || '_1.webp' 
WHERE slug IN (
    'fantasy-tv-cabinet',
    'modern-kitchen-set', 
    'kitchen-island-premium',
    'pantry-cabinet-large',
    'cabinet-lighting',
    'glass-door',
    'led-gola',
    'pull-out-basket',
    'soft-close-hinges',
    'tandem-box'
);

-- Update specific products with existing WebP files
UPDATE products SET image_url = '/static/uploads/products/4x5_rak-tv-baru_1.webp' 
WHERE slug = 'rak-tv-baru-1';

UPDATE products SET image_url = '/static/uploads/products/4x5_testing-produk_1_3e5badb9.webp' 
WHERE slug = 'testing-produk';

UPDATE products SET image_url = '/static/uploads/products/4x5_test-product_1.webp' 
WHERE slug = 'test-product';

-- Update accessories to use proper WebP paths from additional-items folder
UPDATE products SET image_url = '/static/images/additional-items/' || slug || '.webp'
WHERE slug IN (
    'soft-close-hinges',
    'pull-out-basket', 
    'glass-door',
    'cabinet-lighting'
) AND image_url IS NULL OR image_url NOT LIKE '%.webp';

-- Set default WebP images for products without images
UPDATE products SET image_url = '/static/uploads/products/4x5_' || slug || '_1.webp'
WHERE image_url IS NULL AND slug NOT IN ('testing-produk', 'rak-tv-baru-1', 'test-product');

-- Verify the updates
SELECT id, name, slug, image_url 
FROM products 
WHERE is_active = true 
ORDER BY created_at DESC;
