# Image Optimization System

## Overview

The enhanced image optimization system provides comprehensive image processing capabilities with advanced WebP conversion, metadata removal, and intelligent quality control. This system is designed to optimize product images for the e-commerce platform while maintaining visual quality and minimizing file sizes.

## Features

### ✅ Advanced Image Optimization
- **Format Support**: JPEG, PNG, WebP, GIF, BMP input formats
- **Output Format**: All images converted to WebP for optimal compression
- **Metadata Removal**: Automatically strips all EXIF and metadata
- **Quality Control**: Configurable quality settings (0-100)
- **Target Size Optimization**: Iterative quality adjustment to meet file size targets

### ✅ Dual Aspect Ratio Processing
- **Mobile (4:5)**: Optimized for mobile viewing
- **Desktop (16:9)**: Optimized for desktop/landscape viewing
- **Smart Cropping**: Intelligent center-crop to maintain subject focus
- **Dimension Limits**: Configurable maximum dimensions (default: 2000px)

### ✅ Comprehensive Statistics
- **Compression Ratios**: Detailed before/after size comparisons
- **Format Detection**: Automatic input format identification
- **Space Savings**: Precise byte and KB savings calculations
- **Processing Results**: Per-aspect-ratio optimization details

## Configuration

### Default Settings
```rust
ImageProcessingConfig {
    webp_quality: 85.0,        // WebP quality (0-100)
    max_dimension: 2000,       // Maximum width/height in pixels
    target_file_size: 500 * 1024, // Target size in bytes (500KB)
}
```

### Quality Guidelines
- **90-100**: Near lossless, larger files
- **80-90**: High quality, recommended for products (default: 85)
- **60-80**: Good quality, smaller files
- **<60**: Visible compression artifacts

## API Endpoints

### Upload Product Images
```
POST /api/v1/uploads/products
Content-Type: multipart/form-data

Fields:
- product_slug: string (required)
- start_index: number (optional, default: 1)
- images: file[] (required)
```

### Response Format
```json
{
  "success": true,
  "message": "Successfully processed 2 images",
  "results": [
    {
      "success": true,
      "filename": "product-image.jpg",
      "original_size": 2048000,
      "optimized_size": 1024000,
      "compression_ratio": 50.0,
      "original_format": "Jpeg",
      "optimized_format": "WebP",
      "space_saved_bytes": 1024000,
      "space_saved_kb": 1000.0,
      "aspect_ratios": [
        {
          "aspect_ratio": "16x9",
          "filename": "16x9_product-slug_1.webp",
          "dimensions": [1920, 1080],
          "file_size": 512000,
          "file_size_kb": 500.0,
          "compression_ratio": 45.0,
          "file_path": "/static/uploads/products/16x9_product-slug_1.webp"
        },
        {
          "aspect_ratio": "4x5",
          "filename": "4x5_product-slug_1.webp",
          "dimensions": [800, 1000],
          "file_size": 512000,
          "file_size_kb": 500.0,
          "compression_ratio": 55.0,
          "file_path": "/static/uploads/products/4x5_product-slug_1.webp"
        }
      ],
      "error": null
    }
  ],
  "total_processed": 2,
  "total_failed": 0
}
```

## File Naming Convention

Generated files follow a structured naming pattern:
```
{aspect_ratio}_{product_slug}_{index}.webp

Examples:
- 16x9_modern-cabinet_1.webp
- 4x5_modern-cabinet_1.webp
- 16x9_modern-cabinet_2.webp
- 4x5_modern-cabinet_2.webp
```

## Storage Structure

```
static/uploads/products/
├── 16x9_product-a_1.webp
├── 4x5_product-a_1.webp
├── 16x9_product-a_2.webp
├── 4x5_product-a_2.webp
├── 16x9_product-b_1.webp
└── 4x5_product-b_1.webp
```

## Validation Rules

### File Size Limits
- **Upload Limit**: 10MB per file
- **Target Output**: 500KB per optimized image
- **Minimum Dimensions**: 400x400 pixels

### Supported Formats
- **Input**: JPEG, PNG, WebP, GIF, BMP
- **Output**: WebP only

## Usage Examples

### Basic Upload (cURL)
```bash
curl -X POST http://localhost:8000/api/v1/uploads/products \
  -F "product_slug=modern-cabinet" \
  -F "start_index=1" \
  -F "images=@image1.jpg" \
  -F "images=@image2.png"
```

### Testing Optimization
```bash
# Run the optimization test suite
cargo run --bin test_image_optimization
```

## Performance Characteristics

### Optimization Results (Test Data)
- **JPEG → WebP**: ~29% size reduction
- **PNG → WebP**: ~32% size reduction
- **Processing Speed**: ~2-3 seconds per image (dual aspect ratios)
- **Memory Usage**: Efficient streaming processing

### Target Size Optimization
The system can automatically adjust quality to meet specific file size targets:
- Uses binary search algorithm for optimal quality
- Maximum 8 iterations for convergence
- Fallback to best achievable quality if target unreachable

## Error Handling

### Common Error Scenarios
1. **Invalid Format**: Unsupported image format
2. **File Too Large**: Exceeds 10MB upload limit
3. **Dimensions Too Small**: Below 400x400 minimum
4. **Processing Failed**: Corruption or invalid image data

### Error Response Format
```json
{
  "success": false,
  "filename": "corrupted-image.jpg",
  "original_size": 1024000,
  "optimized_size": null,
  "compression_ratio": null,
  "aspect_ratios": [],
  "error": "Failed to decode image. Please ensure the file is a valid image format"
}
```

## Monitoring and Cleanup

### Upload Statistics
```
GET /api/v1/uploads/stats
```

### Legacy Image Cleanup
```
POST /api/v1/uploads/cleanup
{
  "dry_run": true
}
```

## Integration with Frontend

The optimized images are automatically available for the DynamicImage components:
- Mobile viewport: Uses 4x5 aspect ratio images
- Desktop viewport: Uses 16x9 aspect ratio images
- Automatic format detection and loading

## Best Practices

1. **Upload Quality**: Use high-quality source images (minimal compression)
2. **Batch Processing**: Upload multiple images in single request for efficiency
3. **Monitoring**: Regularly check upload statistics and cleanup legacy files
4. **Testing**: Use the test binary to verify optimization performance

## Troubleshooting

### Common Issues
- **Low Compression**: Source image already heavily compressed
- **Large Output**: Complex images may not reach target size
- **Processing Slow**: Large images or high-resolution sources

### Solutions
- Use uncompressed or lightly compressed source images
- Adjust quality settings for specific use cases
- Consider pre-resizing very large images before upload
