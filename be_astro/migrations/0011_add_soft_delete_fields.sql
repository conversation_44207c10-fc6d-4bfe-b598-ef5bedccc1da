-- Add soft delete functionality to all tables
-- Add deleted_at field to all existing tables for soft delete functionality

-- Admin users table
ALTER TABLE admin_users ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Categories table  
ALTER TABLE categories ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Products table
ALTER TABLE products ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Product categories junction table
ALTER TABLE product_categories ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Product dimensions table
ALTER TABLE product_dimensions ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Product themes table
ALTER TABLE product_themes ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Product accessories table
ALTER TABLE product_accessories ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Bundles table
ALTER TABLE bundles ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Bundle items table
ALTER TABLE bundle_items ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Create indexes for soft delete fields to improve query performance
CREATE INDEX IF NOT EXISTS idx_admin_users_deleted_at ON admin_users(deleted_at);
CREATE INDEX IF NOT EXISTS idx_categories_deleted_at ON categories(deleted_at);
CREATE INDEX IF NOT EXISTS idx_products_deleted_at ON products(deleted_at);
CREATE INDEX IF NOT EXISTS idx_product_categories_deleted_at ON product_categories(deleted_at);
CREATE INDEX IF NOT EXISTS idx_product_dimensions_deleted_at ON product_dimensions(deleted_at);
CREATE INDEX IF NOT EXISTS idx_product_themes_deleted_at ON product_themes(deleted_at);
CREATE INDEX IF NOT EXISTS idx_product_accessories_deleted_at ON product_accessories(deleted_at);
CREATE INDEX IF NOT EXISTS idx_bundles_deleted_at ON bundles(deleted_at);
CREATE INDEX IF NOT EXISTS idx_bundle_items_deleted_at ON bundle_items(deleted_at);
CREATE INDEX IF NOT EXISTS idx_orders_deleted_at ON orders(deleted_at);

-- Add missing updated_at field to product_categories table
ALTER TABLE product_categories ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Add missing updated_at field to bundle_items table  
ALTER TABLE bundle_items ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP;
