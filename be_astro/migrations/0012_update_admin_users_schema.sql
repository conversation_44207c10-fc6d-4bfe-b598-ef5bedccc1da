-- Migration: Update admin_users table schema for email-based authentication
-- Created: 2025-06-09
-- Description: Ensure admin_users table is properly configured for email/password login

-- Clean up any old username-related constraints (if they exist)
ALTER TABLE admin_users DROP CONSTRAINT IF EXISTS admin_users_username_key;
DROP INDEX IF EXISTS idx_admin_users_username;

-- Update the unique email index to exclude deleted records
DROP INDEX IF EXISTS idx_admin_users_email;
CREATE UNIQUE INDEX idx_admin_users_email ON admin_users(email) WHERE deleted_at IS NULL;

-- Insert initial admin user with correct schema
-- Password: SecureAdmin123! (bcrypt hashed with cost factor 12)
INSERT INTO admin_users (email, password_hash, name, is_active) VALUES (
    '<EMAIL>',
    '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- SecureAdmin123!
    'System Administrator',
    true
) ON CONFLICT (email) DO NOTHING;

-- Add comments for documentation
COMMENT ON COLUMN admin_users.name IS 'Admin user full name';
COMMENT ON COLUMN admin_users.email IS 'Admin user email address for authentication';
COMMENT ON COLUMN admin_users.password_hash IS 'Argon2 hashed password';
COMMENT ON COLUMN admin_users.is_active IS 'Whether the admin user account is active';
