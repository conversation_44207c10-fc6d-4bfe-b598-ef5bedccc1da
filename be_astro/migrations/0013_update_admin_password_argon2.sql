-- Migration: Update admin password to use Argon2 hash
-- Created: 2025-06-09
-- Description: Replace bcrypt hash with Argon2 hash for better security

-- Update admin password with Argon2 hash for "SecureAdmin123!"
-- This hash was generated using Argon2 with default parameters
UPDATE admin_users 
SET password_hash = '$argon2id$v=19$m=19456,t=2,p=1$gZiV/M1gPc22ElAH/Jh1Hw$CWOrkoo7oJGAUJk98VmOTkwIBdCZLhCahvQGRVzRTC8'
WHERE email = '<EMAIL>';

-- Add comment for documentation
COMMENT ON COLUMN admin_users.password_hash IS 'Argon2 hashed password (upgraded from bcrypt for better security)';
