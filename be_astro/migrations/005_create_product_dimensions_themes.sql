-- Create price modifier type enum
CREATE TYPE price_modifier_type AS ENUM ('addition', 'subtraction', 'replacement', 'percentage');

-- Create product dimensions table
CREATE TABLE IF NOT EXISTS product_dimensions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    name VARCHAR NOT NULL,
    price_modifier_type price_modifier_type NOT NULL,
    price_modifier_value DECIMAL(15,2) NOT NULL,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create product themes table
CREATE TABLE IF NOT EXISTS product_themes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    name VA<PERSON>HAR NOT NULL,
    price_modifier_type price_modifier_type NOT NULL,
    price_modifier_value DECIMAL(15,2) NOT NULL,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_product_dimensions_product_id ON product_dimensions(product_id);
CREATE INDEX IF NOT EXISTS idx_product_dimensions_is_default ON product_dimensions(is_default);
CREATE INDEX IF NOT EXISTS idx_product_themes_product_id ON product_themes(product_id);
CREATE INDEX IF NOT EXISTS idx_product_themes_is_default ON product_themes(is_default);
