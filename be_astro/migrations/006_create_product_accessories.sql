-- Create product accessories table
CREATE TABLE IF NOT EXISTS product_accessories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    description TEXT,
    price DECIMAL(15,2) NOT NULL,
    is_required BOOLEAN NOT NULL DEFAULT FALSE,
    category VA<PERSON>HAR NOT NULL,
    image_url VARCHAR,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_product_accessories_category ON product_accessories(category);
CREATE INDEX IF NOT EXISTS idx_product_accessories_is_active ON product_accessories(is_active);
CREATE INDEX IF NOT EXISTS idx_product_accessories_is_required ON product_accessories(is_required);
