use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub success: bool,
    pub token: Option<String>,
    pub user: Option<AdminUserResponse>,
    pub message: String,
}

#[derive(Debug, Serialize)]
pub struct AdminUserResponse {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub is_active: bool,
    pub last_login: Option<chrono::DateTime<chrono::Utc>>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

impl LoginResponse {
    pub fn success(token: String, user: AdminUserResponse) -> Self {
        Self {
            success: true,
            token: Some(token),
            user: Some(user),
            message: "Login successful".to_string(),
        }
    }

    pub fn failure(message: String) -> Self {
        Self {
            success: false,
            token: None,
            user: None,
            message,
        }
    }
}

impl From<crate::domain::entities::AdminUser> for AdminUserResponse {
    fn from(user: crate::domain::entities::AdminUser) -> Self {
        Self {
            id: user.id.0,
            username: user.name, // Updated to use 'name' field
            email: user.email, // Email is now String, not Email type
            is_active: user.is_active,
            last_login: user.last_login_at, // Updated field name
            created_at: user.created_at,
        }
    }
}
