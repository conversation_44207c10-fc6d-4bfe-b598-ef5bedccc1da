use crate::domain::entities::*;
use crate::domain::value_objects::*;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductResponse {
    pub id: Uuid,
    pub name: String,
    pub slug: Option<String>,
    pub description: Option<String>,
    pub base_price: Decimal,
    pub is_featured: bool,
    pub is_active: bool,
    pub image_url: Option<String>,
    pub specifications: Option<HashMap<String, serde_json::Value>>,
    pub dimensions: Option<HashMap<String, serde_json::Value>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub categories: Option<Vec<CategoryResponse>>,
    pub pricing_options: Option<ProductPricingOptions>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CategoryResponse {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub parent_id: Option<Uuid>,
    pub image_url: Option<String>,
    pub is_active: bool,
    pub sort_order: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductPricingOptions {
    pub dimensions: Vec<ProductDimensionResponse>,
    pub themes: Vec<ProductThemeResponse>,
    pub accessories: Vec<ProductAccessoryResponse>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductDimensionResponse {
    pub name: String,
    pub price_modifier_type: String,
    pub price_modifier_value: Decimal,
    pub is_default: bool,
    pub sort_order: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductThemeResponse {
    pub name: String,
    pub price_modifier_type: String,
    pub price_modifier_value: Decimal,
    pub is_default: bool,
    pub sort_order: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductAccessoryResponse {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub price: Decimal,
    pub is_required: bool,
    pub category: String,
    pub image_url: Option<String>,
    pub is_active: bool,
}

#[derive(Debug, Deserialize)]
pub struct CreateProductRequest {
    pub name: String,
    pub slug: Option<String>,
    pub description: Option<String>,
    pub base_price: Decimal,
    pub is_featured: Option<bool>,
    pub is_active: Option<bool>,
    pub image_url: Option<String>,
    pub specifications: Option<HashMap<String, serde_json::Value>>,
    pub dimensions: Option<HashMap<String, serde_json::Value>>,
    pub category_id: Option<Uuid>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateProductRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub base_price: Option<Decimal>,
    pub is_featured: Option<bool>,
    pub is_active: Option<bool>,
    pub image_url: Option<String>,
    pub specifications: Option<HashMap<String, serde_json::Value>>,
    pub dimensions: Option<HashMap<String, serde_json::Value>>,
}

#[derive(Debug, Deserialize)]
pub struct CreateCategoryRequest {
    pub name: String,
    pub description: Option<String>,
    pub parent_id: Option<Uuid>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateCategoryRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub parent_id: Option<Uuid>,
    pub is_active: Option<bool>,
    pub sort_order: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct CalculatePriceRequest {
    pub product_id: Uuid,
    pub dimension_name: Option<String>,
    pub theme_name: Option<String>,
    pub accessories: Vec<AccessorySelectionRequest>,
}

#[derive(Debug, Deserialize)]
pub struct AccessorySelectionRequest {
    pub accessory_id: Uuid,
    pub quantity: i32,
}

#[derive(Debug, Serialize)]
pub struct PriceCalculationResponse {
    pub base_price: Decimal,
    pub dimension_adjustment: Decimal,
    pub theme_adjustment: Decimal,
    pub accessories_total: Decimal,
    pub final_price: Decimal,
    pub breakdown: Vec<PriceItemResponse>,
}

#[derive(Debug, Serialize)]
pub struct PriceItemResponse {
    pub name: String,
    pub price: Decimal,
    pub item_type: String,
}

// Conversion implementations
impl From<Product> for ProductResponse {
    fn from(product: Product) -> Self {
        Self {
            id: product.id.0,
            name: product.name,
            slug: product.slug,
            description: product.description,
            base_price: product.base_price.amount,
            is_featured: product.is_featured,
            is_active: product.is_active,
            image_url: product.image_url,
            specifications: product.specifications,
            dimensions: product.dimensions,
            created_at: product.created_at,
            updated_at: product.updated_at,
            categories: None,
            pricing_options: None,
        }
    }
}

impl From<Category> for CategoryResponse {
    fn from(category: Category) -> Self {
        Self {
            id: category.id.0,
            name: category.name,
            description: category.description,
            parent_id: category.parent_id.map(|id| id.0),
            image_url: category.image_url,
            is_active: category.is_active,
            sort_order: category.sort_order,
            created_at: category.created_at,
            updated_at: category.updated_at,
        }
    }
}

impl From<ProductDimension> for ProductDimensionResponse {
    fn from(dimension: ProductDimension) -> Self {
        Self {
            name: dimension.name,
            price_modifier_type: match dimension.price_modifier_type {
                PriceModifierType::Addition => "addition".to_string(),
                PriceModifierType::Subtraction => "subtraction".to_string(),
                PriceModifierType::Replacement => "replacement".to_string(),
                PriceModifierType::Percentage => "percentage".to_string(),
            },
            price_modifier_value: dimension.price_modifier_value,
            is_default: dimension.is_default,
            sort_order: dimension.sort_order,
        }
    }
}

impl From<ProductTheme> for ProductThemeResponse {
    fn from(theme: ProductTheme) -> Self {
        Self {
            name: theme.name,
            price_modifier_type: match theme.price_modifier_type {
                PriceModifierType::Addition => "addition".to_string(),
                PriceModifierType::Subtraction => "subtraction".to_string(),
                PriceModifierType::Replacement => "replacement".to_string(),
                PriceModifierType::Percentage => "percentage".to_string(),
            },
            price_modifier_value: theme.price_modifier_value,
            is_default: theme.is_default,
            sort_order: theme.sort_order,
        }
    }
}

impl From<ProductAccessory> for ProductAccessoryResponse {
    fn from(accessory: ProductAccessory) -> Self {
        Self {
            id: accessory.id.0,
            name: accessory.name,
            description: accessory.description,
            price: accessory.price.amount,
            is_required: accessory.is_required,
            category: accessory.category,
            image_url: accessory.image_url,
            is_active: accessory.is_active,
        }
    }
}

impl From<PriceCalculation> for PriceCalculationResponse {
    fn from(calculation: PriceCalculation) -> Self {
        Self {
            base_price: calculation.base_price.amount,
            dimension_adjustment: calculation.dimension_adjustment.amount,
            theme_adjustment: calculation.theme_adjustment.amount,
            accessories_total: calculation.accessories_total.amount,
            final_price: calculation.final_price.amount,
            breakdown: calculation.breakdown.into_iter().map(|item| item.into()).collect(),
        }
    }
}

impl From<PriceItem> for PriceItemResponse {
    fn from(item: PriceItem) -> Self {
        Self {
            name: item.name,
            price: item.price.amount,
            item_type: match item.item_type {
                PriceItemType::Base => "base".to_string(),
                PriceItemType::Dimension => "dimension".to_string(),
                PriceItemType::Theme => "theme".to_string(),
                PriceItemType::Accessory => "accessory".to_string(),
            },
        }
    }
}

impl From<CalculatePriceRequest> for ProductConfiguration {
    fn from(request: CalculatePriceRequest) -> Self {
        Self {
            product_id: ProductId::from_uuid(request.product_id),
            dimension_name: request.dimension_name,
            theme_name: request.theme_name,
            accessories: request.accessories.into_iter().map(|a| AccessorySelection {
                accessory_id: AccessoryId::from_uuid(a.accessory_id),
                quantity: a.quantity,
            }).collect(),
        }
    }
}
