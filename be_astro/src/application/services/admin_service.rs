use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use std::sync::Arc;

pub struct AdminService {
    admin_repo: Arc<dyn AdminRepository>,
}

impl AdminService {
    pub fn new(admin_repo: Arc<dyn AdminRepository>) -> Self {
        Self { admin_repo }
    }

    // TODO: Update AdminService to work with new AdminUser entity and AuthService
    // For now, authentication is handled by AuthService
    pub async fn authenticate(
        &self,
        _username: &str,
        _password: &str,
    ) -> Result<Option<AdminUser>, String> {
        // Temporarily disabled - use AuthService for authentication
        Err("Use AuthService for authentication".to_string())
    }

    pub async fn get_admin_user(&self, id: &AdminUserId) -> Result<Option<AdminUser>, String> {
        self.admin_repo.find_by_id(id).await
    }

    pub async fn get_admin_by_username(&self, username: &str) -> Result<Option<AdminUser>, String> {
        self.admin_repo.find_by_username(username).await
    }

    pub async fn get_admin_by_email(&self, email: &Email) -> Result<Option<AdminUser>, String> {
        self.admin_repo.find_by_email(email).await
    }

    // Placeholder methods - to be updated to work with new AdminUser entity
    pub async fn create_admin_user(
        &self,
        _username: String,
        _email: String,
        _password: String,
    ) -> Result<AdminUser, String> {
        // Temporarily disabled - use AuthService for admin user management
        Err("Use AuthService for admin user management".to_string())
    }

    // All other methods temporarily disabled - use AuthService for admin user management
    pub async fn update_admin_user(
        &self,
        _id: &AdminUserId,
        _username: Option<String>,
        _email: Option<String>,
        _password: Option<String>,
        _is_active: Option<bool>,
    ) -> Result<(), String> {
        Err("Use AuthService for admin user management".to_string())
    }

    pub async fn delete_admin_user(&self, _id: &AdminUserId) -> Result<(), String> {
        Err("Use AuthService for admin user management".to_string())
    }

    pub async fn list_admin_users(&self, _page: u32, _per_page: u32) -> Result<Vec<AdminUser>, String> {
        Err("Use AuthService for admin user management".to_string())
    }

    pub async fn count_admin_users(&self) -> Result<u64, String> {
        Err("Use AuthService for admin user management".to_string())
    }

    pub async fn change_password(
        &self,
        _id: &AdminUserId,
        _current_password: &str,
        _new_password: &str,
    ) -> Result<(), String> {
        Err("Use AuthService for admin user management".to_string())
    }

    pub async fn activate_admin_user(&self, _id: &AdminUserId) -> Result<(), String> {
        Err("Use AuthService for admin user management".to_string())
    }

    pub async fn deactivate_admin_user(&self, _id: &AdminUserId) -> Result<(), String> {
        Err("Use AuthService for admin user management".to_string())
    }
}
