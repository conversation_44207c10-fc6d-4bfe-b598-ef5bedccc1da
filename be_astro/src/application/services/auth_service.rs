use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use chrono::{DateTime, Utc};
use jsonwebtoken::{decode, encode, Decoding<PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::domain::entities::AdminUser;
use crate::domain::repositories::AdminUserRepository;
use crate::domain::value_objects::AdminUserId;

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,        // Subject (admin user ID)
    pub email: String,      // Admin email
    pub name: String,       // Admin name
    pub exp: usize,         // Expiration time
    pub iat: usize,         // Issued at
}

#[derive(Debug, Clone)]
pub struct LoginAttempt {
    pub count: u32,
    pub last_attempt: Instant,
}

pub struct AuthService {
    admin_user_repo: Arc<dyn AdminUserRepository>,
    jwt_secret: String,
    jwt_expires_in: Duration,
    login_attempts: Arc<Mutex<HashMap<String, LoginAttempt>>>,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub admin_user: AdminUserInfo,
}

#[derive(Debug, Serialize)]
pub struct AdminUserInfo {
    pub id: String,
    pub email: String,
    pub name: String,
    pub is_active: bool,
    pub last_login_at: Option<DateTime<Utc>>,
}

impl AuthService {
    pub fn new(
        admin_user_repo: Arc<dyn AdminUserRepository>,
        jwt_secret: String,
        jwt_expires_in: Duration,
    ) -> Self {
        Self {
            admin_user_repo,
            jwt_secret,
            jwt_expires_in,
            login_attempts: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Authenticate admin user and return JWT token
    pub async fn login(&self, email: String, password: String, ip_address: String) -> Result<LoginResponse, String> {
        // Check rate limiting
        if self.is_rate_limited(&ip_address) {
            self.log_security_event(&format!("Rate limited login attempt from IP: {}", ip_address));
            return Err("Too many login attempts. Please try again later.".to_string());
        }

        // Find admin user by email (including deleted ones for proper error handling)
        let admin_user = self.admin_user_repo
            .find_by_email_including_deleted(&email)
            .await
            .map_err(|e| {
                self.log_security_event(&format!("Database error during login for {}: {}", email, e));
                "Invalid credentials".to_string()
            })?;

        let mut admin_user = match admin_user {
            Some(user) => user,
            None => {
                self.record_failed_attempt(&ip_address);
                self.log_security_event(&format!("Login attempt with non-existent email: {} from IP: {}", email, ip_address));
                return Err("Invalid credentials".to_string());
            }
        };

        // Check if user can login (active and not deleted)
        if !admin_user.can_login() {
            self.record_failed_attempt(&ip_address);
            self.log_security_event(&format!("Login attempt for inactive/deleted user: {} from IP: {}", email, ip_address));
            return Err("Invalid credentials".to_string());
        }

        // Verify password
        if !admin_user.verify_password(&password) {
            self.record_failed_attempt(&ip_address);
            self.log_security_event(&format!("Failed login attempt for {}: invalid password from IP: {}", email, ip_address));
            return Err("Invalid credentials".to_string());
        }

        // Clear failed attempts on successful login
        self.clear_failed_attempts(&ip_address);

        // Update last login timestamp
        admin_user.update_last_login();
        
        // Save updated admin user
        self.admin_user_repo.save(&admin_user).await
            .map_err(|e| {
                self.log_security_event(&format!("Failed to update last login for {}: {}", email, e));
                "Login failed".to_string()
            })?;

        // Generate JWT token
        let token = self.generate_token(&admin_user)?;

        self.log_security_event(&format!("Successful login for {} from IP: {}", email, ip_address));

        Ok(LoginResponse {
            token,
            admin_user: AdminUserInfo {
                id: admin_user.id.0.to_string(),
                email: admin_user.email,
                name: admin_user.name,
                is_active: admin_user.is_active,
                last_login_at: admin_user.last_login_at,
            },
        })
    }

    /// Refresh JWT token
    pub async fn refresh_token(&self, token: String) -> Result<String, String> {
        let claims = self.verify_token(&token)?;
        
        // Find admin user to ensure they're still active
        let admin_id = AdminUserId::from_uuid(
            Uuid::parse_str(&claims.sub)
                .map_err(|_| "Invalid token".to_string())?
        );
        
        let admin_user = self.admin_user_repo
            .find_by_id(&admin_id)
            .await
            .map_err(|_| "Invalid token".to_string())?
            .ok_or("Invalid token".to_string())?;

        if !admin_user.can_login() {
            return Err("Invalid token".to_string());
        }

        // Generate new token
        let new_token = self.generate_token(&admin_user)?;
        
        self.log_security_event(&format!("Token refreshed for admin: {}", admin_user.email));
        
        Ok(new_token)
    }

    /// Verify JWT token and return claims
    pub fn verify_token(&self, token: &str) -> Result<Claims, String> {
        let decoding_key = DecodingKey::from_secret(self.jwt_secret.as_ref());
        let validation = Validation::default();

        decode::<Claims>(token, &decoding_key, &validation)
            .map(|token_data| token_data.claims)
            .map_err(|e| format!("Invalid token: {}", e))
    }

    /// Get admin user from token
    pub async fn get_admin_from_token(&self, token: &str) -> Result<AdminUser, String> {
        let claims = self.verify_token(token)?;
        
        let admin_id = AdminUserId::from_uuid(
            Uuid::parse_str(&claims.sub)
                .map_err(|_| "Invalid token".to_string())?
        );
        
        self.admin_user_repo
            .find_by_id(&admin_id)
            .await
            .map_err(|_| "Invalid token".to_string())?
            .ok_or("Invalid token".to_string())
    }

    /// Generate JWT token for admin user
    fn generate_token(&self, admin_user: &AdminUser) -> Result<String, String> {
        let now = Utc::now();
        let exp = now + chrono::Duration::from_std(self.jwt_expires_in)
            .map_err(|_| "Invalid token expiration".to_string())?;

        let claims = Claims {
            sub: admin_user.id.0.to_string(),
            email: admin_user.email.clone(),
            name: admin_user.name.clone(),
            exp: exp.timestamp() as usize,
            iat: now.timestamp() as usize,
        };

        let encoding_key = EncodingKey::from_secret(self.jwt_secret.as_ref());
        
        encode(&Header::default(), &claims, &encoding_key)
            .map_err(|e| format!("Failed to generate token: {}", e))
    }

    /// Check if IP address is rate limited
    fn is_rate_limited(&self, ip_address: &str) -> bool {
        let mut attempts = self.login_attempts.lock().unwrap();
        
        if let Some(attempt) = attempts.get(ip_address) {
            let time_since_last = attempt.last_attempt.elapsed();
            
            // Reset attempts after 15 minutes
            if time_since_last > Duration::from_secs(15 * 60) {
                attempts.remove(ip_address);
                return false;
            }
            
            // Check if exceeded max attempts (5)
            attempt.count >= 5
        } else {
            false
        }
    }

    /// Record failed login attempt
    fn record_failed_attempt(&self, ip_address: &str) {
        let mut attempts = self.login_attempts.lock().unwrap();
        
        let attempt = attempts.entry(ip_address.to_string()).or_insert(LoginAttempt {
            count: 0,
            last_attempt: Instant::now(),
        });
        
        attempt.count += 1;
        attempt.last_attempt = Instant::now();
    }

    /// Clear failed login attempts for IP
    fn clear_failed_attempts(&self, ip_address: &str) {
        let mut attempts = self.login_attempts.lock().unwrap();
        attempts.remove(ip_address);
    }

    /// Log security events
    fn log_security_event(&self, message: &str) {
        eprintln!("[SECURITY] {}: {}", Utc::now().format("%Y-%m-%d %H:%M:%S UTC"), message);
    }
}
