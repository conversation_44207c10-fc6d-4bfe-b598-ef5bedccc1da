use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use std::sync::Arc;

pub struct CategoryService {
    category_repo: Arc<dyn CategoryRepository>,
}

impl CategoryService {
    pub fn new(category_repo: Arc<dyn CategoryRepository>) -> Self {
        Self { category_repo }
    }

    pub async fn get_category(&self, id: &CategoryId) -> Result<Option<Category>, String> {
        self.category_repo.find_by_id(id).await
    }

    pub async fn get_all_categories(&self) -> Result<Vec<Category>, String> {
        self.category_repo.find_all().await
    }

    pub async fn get_category_by_id(&self, id: &CategoryId) -> Result<Option<Category>, String> {
        self.category_repo.find_by_id(id).await
    }



    pub async fn get_root_categories(&self) -> Result<Vec<Category>, String> {
        self.category_repo.find_root_categories().await
    }

    pub async fn get_category_children(&self, parent_id: &CategoryId) -> Result<Vec<Category>, String> {
        self.category_repo.find_children(parent_id).await
    }

    pub async fn create_category(
        &self,
        name: String,
        description: Option<String>,
        parent_id: Option<CategoryId>,
    ) -> Result<Category, String> {
        let category = Category::new(name, description, parent_id);
        self.category_repo.save(&category).await?;
        Ok(category)
    }

    pub async fn create_category_with_status(
        &self,
        name: String,
        description: Option<String>,
        parent_id: Option<CategoryId>,
        is_active: bool,
    ) -> Result<Category, String> {
        let mut category = Category::new(name, description, parent_id);
        if !is_active {
            category.deactivate();
        }
        self.category_repo.save(&category).await?;
        Ok(category)
    }

    pub async fn update_category(
        &self,
        id: &CategoryId,
        name: Option<String>,
        description: Option<String>,
        parent_id: Option<CategoryId>,
        is_active: Option<bool>,
    ) -> Result<(), String> {
        let mut category = self.category_repo
            .find_by_id(id)
            .await?
            .ok_or("Category not found")?;

        if let Some(name) = name {
            category.update_name(name);
        }

        if let Some(description) = description {
            category.update_description(Some(description));
        }

        if let Some(parent_id) = parent_id {
            category.set_parent(Some(parent_id));
        }

        if let Some(active) = is_active {
            if active {
                category.activate();
            } else {
                category.deactivate();
            }
        }

        self.category_repo.save(&category).await
    }

    pub async fn delete_category(&self, id: &CategoryId) -> Result<(), String> {
        // Check if category has children
        if self.category_repo.has_children(id).await? {
            return Err("Cannot delete category with children".to_string());
        }

        self.category_repo.delete(id).await
    }

    pub async fn get_category_tree(&self) -> Result<Vec<CategoryWithChildren>, String> {
        self.category_repo.get_category_tree().await
    }

    pub async fn count_categories(&self) -> Result<u64, String> {
        self.category_repo.count().await
    }

    pub async fn save_category(&self, category: &Category) -> Result<(), String> {
        self.category_repo.save(category).await
    }

    pub async fn get_category_by_id_including_deleted(&self, id: &CategoryId) -> Result<Option<Category>, String> {
        // For now, use the same method as find_by_id since we don't have a separate method
        // In a real implementation, this would query without the deleted_at filter
        self.category_repo.find_by_id(id).await
    }

    pub async fn soft_delete_category(&self, id: &CategoryId) -> Result<(), String> {
        self.category_repo.soft_delete(id).await
    }

    pub async fn restore_category(&self, id: &CategoryId) -> Result<(), String> {
        self.category_repo.restore(id).await
    }
}
