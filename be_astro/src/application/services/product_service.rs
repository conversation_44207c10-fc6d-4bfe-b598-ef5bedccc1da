use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::services::*;
use crate::domain::value_objects::*;
use std::sync::Arc;

pub struct ProductService {
    product_repo: Arc<dyn ProductRepository>,
    pricing_service: Arc<PricingService>,
}

impl ProductService {
    pub fn new(
        product_repo: Arc<dyn ProductRepository>,
        pricing_service: Arc<PricingService>,
    ) -> Self {
        Self {
            product_repo,
            pricing_service,
        }
    }

    pub async fn get_product(&self, id: &ProductId) -> Result<Option<Product>, String> {
        self.product_repo.find_by_id(id).await
    }

    pub async fn get_product_by_slug(&self, slug: &str) -> Result<Option<Product>, String> {
        self.product_repo.find_by_slug(slug).await
    }

    pub async fn get_product_by_slug(&self, slug: &str) -> Result<Option<Product>, String> {
        self.product_repo.find_by_slug(slug).await
    }

    pub async fn get_products(&self, page: u32, per_page: u32) -> Result<Vec<Product>, String> {
        self.product_repo.find_all(page, per_page).await
    }

    pub async fn get_featured_products(&self, limit: Option<u32>) -> Result<Vec<Product>, String> {
        self.product_repo.find_featured(limit).await
    }

    pub async fn search_products(&self, query: &str, page: u32, per_page: u32) -> Result<Vec<Product>, String> {
        self.product_repo.search(query, page, per_page).await
    }

    pub async fn get_products_by_category(&self, category_id: &CategoryId) -> Result<Vec<Product>, String> {
        self.product_repo.find_by_category(category_id).await
    }

    pub async fn get_accessory_products(&self) -> Result<Vec<Product>, String> {
        self.product_repo.find_by_accessory_categories().await
    }

    pub async fn create_product(
        &self,
        name: String,
        slug: Option<String>,
        description: Option<String>,
        base_price: rust_decimal::Decimal,
        is_featured: Option<bool>,
        is_active: Option<bool>,
    ) -> Result<Product, String> {
        let price = Price::new(base_price);

        // Generate unique slug if not provided
        let final_slug = match slug {
            Some(s) => self.ensure_unique_slug(s).await?,
            None => self.generate_slug_from_name(&name).await?,
        };

        let mut product = Product::new(name, Some(final_slug), description, price);

        // Set optional fields
        if let Some(featured) = is_featured {
            product.set_featured(featured);
        }
        if let Some(active) = is_active {
            if active {
                product.activate();
            } else {
                product.deactivate();
            }
        }

        self.product_repo.save(&product).await?;
        Ok(product)
    }

    async fn generate_slug_from_name(&self, name: &str) -> Result<String, String> {
        let base_slug = name
            .to_lowercase()
            .chars()
            .map(|c| if c.is_alphanumeric() { c } else { '-' })
            .collect::<String>()
            .split('-')
            .filter(|s| !s.is_empty())
            .collect::<Vec<&str>>()
            .join("-");

        self.ensure_unique_slug(base_slug).await
    }

    async fn ensure_unique_slug(&self, base_slug: String) -> Result<String, String> {
        // Check if base slug is available
        if !self.slug_exists(&base_slug).await? {
            return Ok(base_slug);
        }

        // Try with numbers
        for i in 1..=100 {
            let candidate = format!("{}-{}", base_slug, i);
            if !self.slug_exists(&candidate).await? {
                return Ok(candidate);
            }
        }

        Err("Could not generate unique slug".to_string())
    }

    async fn slug_exists(&self, slug: &str) -> Result<bool, String> {
        // Check if slug exists in database (including deleted products)
        // We need to implement a direct database query for this
        match self.product_repo.find_by_slug(slug).await {
            Ok(Some(_)) => Ok(true),
            Ok(None) => Ok(false),
            Err(e) => Err(e),
        }
    }

    pub async fn update_product(
        &self,
        id: &ProductId,
        name: Option<String>,
        description: Option<String>,
        base_price: Option<rust_decimal::Decimal>,
        is_featured: Option<bool>,
        is_active: Option<bool>,
    ) -> Result<(), String> {
        let mut product = self.product_repo
            .find_by_id(id)
            .await?
            .ok_or("Product not found")?;

        if let Some(name) = name {
            product.name = name;
            product.updated_at = chrono::Utc::now();
        }

        if let Some(description) = description {
            product.description = Some(description);
            product.updated_at = chrono::Utc::now();
        }

        if let Some(price) = base_price {
            product.update_price(Price::new(price));
        }

        if let Some(featured) = is_featured {
            product.set_featured(featured);
        }

        if let Some(active) = is_active {
            if active {
                product.activate();
            } else {
                product.deactivate();
            }
        }

        self.product_repo.save(&product).await
    }

    pub async fn delete_product(&self, id: &ProductId) -> Result<(), String> {
        self.product_repo.delete(id).await
    }

    pub async fn calculate_product_price(
        &self,
        product_id: &ProductId,
        configuration: &ProductConfiguration,
    ) -> Result<PriceCalculation, String> {
        let product = self.product_repo
            .find_by_id(product_id)
            .await?
            .ok_or("Product not found")?;

        self.pricing_service
            .calculate_product_price(&product, configuration)
            .await
    }

    pub async fn add_product_to_category(
        &self,
        product_id: &ProductId,
        category_id: &CategoryId,
    ) -> Result<(), String> {
        // Verify product exists
        self.product_repo
            .find_by_id(product_id)
            .await?
            .ok_or("Product not found")?;

        self.product_repo
            .add_to_category(product_id, category_id)
            .await
    }

    pub async fn remove_product_from_category(
        &self,
        product_id: &ProductId,
        category_id: &CategoryId,
    ) -> Result<(), String> {
        self.product_repo
            .remove_from_category(product_id, category_id)
            .await
    }

    pub async fn get_product_categories(&self, product_id: &ProductId) -> Result<Vec<Category>, String> {
        self.product_repo.find_categories(product_id).await
    }

    pub async fn get_product_dimensions(&self, product_id: &ProductId) -> Result<Vec<ProductDimension>, String> {
        self.product_repo.find_dimensions(product_id).await
    }

    pub async fn get_product_themes(&self, product_id: &ProductId) -> Result<Vec<ProductTheme>, String> {
        self.product_repo.find_themes(product_id).await
    }

    pub async fn save_product_dimension(&self, dimension: &ProductDimension) -> Result<(), String> {
        self.product_repo.save_dimension(dimension).await
    }

    pub async fn save_product_theme(&self, theme: &ProductTheme) -> Result<(), String> {
        self.product_repo.save_theme(theme).await
    }

    pub async fn count_products(&self) -> Result<u64, String> {
        self.product_repo.count().await
    }


}
