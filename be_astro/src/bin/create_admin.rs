use argon2::{
    password_hash::{rand_core::OsRng, PasswordHash, PasswordHasher, PasswordVerifier, SaltString},
    Argon2,
};
use sqlx::PgPool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Load environment variables
    dotenvy::dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgres://postgres:postgres@localhost:5432/astro_ecommerce".to_string());
    
    // Connect to database
    let pool = PgPool::connect(&database_url).await?;
    
    // Admin credentials
    let email = "<EMAIL>";
    let password = "Astrokabinet25!";
    let name = "System Administrator";
    
    // Generate Argon2 hash
    let salt = SaltString::generate(&mut OsRng);
    let argon2 = Argon2::default();
    let password_hash = argon2
        .hash_password(password.as_bytes(), &salt)?
        .to_string();
    
    println!("Generated password hash: {}", password_hash);
    
    // Delete existing admin user if exists
    sqlx::query("DELETE FROM admin_users WHERE email = $1")
        .bind(email)
        .execute(&pool)
        .await?;
    
    // Insert new admin user
    sqlx::query(
        "INSERT INTO admin_users (email, password_hash, name, is_active, created_at, updated_at) 
         VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)"
    )
    .bind(email)
    .bind(&password_hash)
    .bind(name)
    .bind(true)
    .execute(&pool)
    .await?;
    
    println!("✅ Admin user created successfully!");
    println!("Email: {}", email);
    println!("Password: {}", password);
    
    // Verify the password works
    let stored_hash = sqlx::query_scalar::<_, String>(
        "SELECT password_hash FROM admin_users WHERE email = $1"
    )
    .bind(email)
    .fetch_one(&pool)
    .await?;
    
    let parsed_hash = PasswordHash::new(&stored_hash)?;
    match argon2.verify_password(password.as_bytes(), &parsed_hash) {
        Ok(()) => println!("✅ Password verification successful!"),
        Err(e) => println!("❌ Password verification failed: {}", e),
    }
    
    pool.close().await;
    Ok(())
}
