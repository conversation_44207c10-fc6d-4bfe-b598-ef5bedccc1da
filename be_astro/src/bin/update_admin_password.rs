use argon2::{
    password_hash::{rand_core::OsRng, Password<PERSON>asher, SaltString},
    Argon2,
};
use sqlx::PgPool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Load environment variables
    dotenvy::dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgres://postgres:postgres@localhost:5432/astro_ecommerce".to_string());
    
    // Connect to database
    let pool = PgPool::connect(&database_url).await?;
    
    // Admin credentials
    let email = "<EMAIL>";
    let password = "Astrokabinet25!";
    
    // Generate Argon2 hash
    let salt = SaltString::generate(&mut OsRng);
    let argon2 = Argon2::default();
    let password_hash = argon2
        .hash_password(password.as_bytes(), &salt)
        .map_err(|e| format!("Failed to hash password: {}", e))?
        .to_string();
    
    println!("Updating admin password...");
    println!("Email: {}", email);
    println!("New Argon2 hash: {}", password_hash);
    
    // Update admin user password
    let result = sqlx::query(
        "UPDATE admin_users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2"
    )
    .bind(&password_hash)
    .bind(email)
    .execute(&pool)
    .await?;
    
    if result.rows_affected() > 0 {
        println!("✅ Admin password updated successfully!");
    } else {
        println!("❌ No admin user found with email: {}", email);
    }
    
    pool.close().await;
    Ok(())
}
