use std::env;

#[derive(Debug, Clone)]
pub struct Config {
    // Database configuration
    pub database_url: String,
    pub db_max_connections: u32,
    pub db_min_connections: u32,

    // Redis configuration
    pub redis_url: String,

    // Server configuration
    pub host: String,
    pub port: u16,
    pub environment: String,
    pub cors_origin: String,
    pub api_base_url: String,
    pub frontend_url: String,

    // Security configuration
    pub jwt_secret: String,
    pub jwt_expires_in: String,
    pub admin_default_email: String,
    pub admin_default_password: String,

    // Rate limiting
    pub rate_limit_requests_per_minute: u32,
    pub rate_limit_burst: u32,

    // File upload configuration
    pub max_file_size: u64,
    pub upload_path: String,
    pub static_path: String,
    pub allowed_image_types: String,

    // Image processing
    pub image_quality: u8,
    pub webp_quality: u8,

    // Business configuration
    pub whatsapp_base_url: String,
    pub whatsapp_phone_number: String,
    pub bank_name: String,
    pub bank_account_number: String,
    pub bank_account_name: String,
    pub company_name: String,
    pub company_email: String,
    pub company_address: String,

    // Monitoring
    pub enable_metrics: bool,
    pub metrics_port: u16,
}

impl Config {
    pub fn from_env() -> anyhow::Result<Self> {
        Ok(Self {
            // Database configuration
            database_url: env::var("DATABASE_URL")
                .unwrap_or_else(|_| {
                    let host = env::var("DB_HOST").unwrap_or_else(|_| "localhost".to_string());
                    let port = env::var("DB_PORT").unwrap_or_else(|_| "5432".to_string());
                    let user = env::var("DB_USER").unwrap_or_else(|_| "astro_user".to_string());
                    let password = env::var("DB_PASSWORD").unwrap_or_else(|_| "astro_password".to_string());
                    let database = env::var("DB_NAME").unwrap_or_else(|_| "astro_ecommerce".to_string());
                    format!("postgres://{}:{}@{}:{}/{}", user, password, host, port, database)
                }),
            db_max_connections: env::var("DB_MAX_CONNECTIONS")
                .unwrap_or_else(|_| "20".to_string())
                .parse()?,
            db_min_connections: env::var("DB_MIN_CONNECTIONS")
                .unwrap_or_else(|_| "5".to_string())
                .parse()?,

            // Redis configuration
            redis_url: env::var("REDIS_URL")
                .unwrap_or_else(|_| {
                    let host = env::var("REDIS_HOST").unwrap_or_else(|_| "localhost".to_string());
                    let port = env::var("REDIS_PORT").unwrap_or_else(|_| "6379".to_string());
                    format!("redis://{}:{}", host, port)
                }),

            // Server configuration
            host: env::var("HOST")
                .unwrap_or_else(|_| "0.0.0.0".to_string()),
            port: env::var("PORT")
                .unwrap_or_else(|_| "7998".to_string())
                .parse()?,
            environment: env::var("ENVIRONMENT")
                .unwrap_or_else(|_| "development".to_string()),
            cors_origin: env::var("CORS_ORIGIN")
                .unwrap_or_else(|_| "http://localhost:5173,http://localhost:5174".to_string()),
            api_base_url: env::var("API_BASE_URL")
                .unwrap_or_else(|_| "http://localhost:7998".to_string()),
            frontend_url: env::var("FE_URL")
                .unwrap_or_else(|_| "http://localhost:5173".to_string()),

            // Security configuration
            jwt_secret: env::var("JWT_SECRET")
                .unwrap_or_else(|_| "your-super-secret-jwt-key-change-this-in-production".to_string()),
            jwt_expires_in: env::var("JWT_EXPIRES_IN")
                .unwrap_or_else(|_| "24h".to_string()),
            admin_default_email: env::var("ADMIN_DEFAULT_EMAIL")
                .unwrap_or_else(|_| "<EMAIL>".to_string()),
            admin_default_password: env::var("ADMIN_DEFAULT_PASSWORD")
                .unwrap_or_else(|_| "admin123".to_string()),

            // Rate limiting
            rate_limit_requests_per_minute: env::var("RATE_LIMIT_REQUESTS_PER_MINUTE")
                .unwrap_or_else(|_| "100".to_string())
                .parse()?,
            rate_limit_burst: env::var("RATE_LIMIT_BURST")
                .unwrap_or_else(|_| "20".to_string())
                .parse()?,

            // File upload configuration
            max_file_size: env::var("MAX_FILE_SIZE")
                .unwrap_or_else(|_| "10485760".to_string())
                .parse()?,
            upload_path: env::var("UPLOAD_PATH")
                .unwrap_or_else(|_| "/app/uploads".to_string()),
            static_path: env::var("STATIC_PATH")
                .unwrap_or_else(|_| "/app/static".to_string()),
            allowed_image_types: env::var("ALLOWED_IMAGE_TYPES")
                .unwrap_or_else(|_| "jpg,jpeg,png,webp".to_string()),

            // Image processing
            image_quality: env::var("IMAGE_QUALITY")
                .unwrap_or_else(|_| "85".to_string())
                .parse()?,
            webp_quality: env::var("WEBP_QUALITY")
                .unwrap_or_else(|_| "65".to_string())
                .parse()?,

            // Business configuration
            whatsapp_base_url: env::var("WHATSAPP_BASE_URL")
                .unwrap_or_else(|_| "https://wa.me/".to_string()),
            whatsapp_phone_number: env::var("WHATSAPP_PHONE_NUMBER")
                .unwrap_or_else(|_| "***********".to_string()),
            bank_name: env::var("BANK_NAME")
                .unwrap_or_else(|_| "BCA".to_string()),
            bank_account_number: env::var("BANK_ACCOUNT_NUMBER")
                .unwrap_or_else(|_| "**********".to_string()),
            bank_account_name: env::var("BANK_ACCOUNT_NAME")
                .unwrap_or_else(|_| "Astro Works Indonesia PT".to_string()),
            company_name: env::var("COMPANY_NAME")
                .unwrap_or_else(|_| "Astro Works Indonesia".to_string()),
            company_email: env::var("COMPANY_EMAIL")
                .unwrap_or_else(|_| "<EMAIL>".to_string()),
            company_address: env::var("COMPANY_ADDRESS")
                .unwrap_or_else(|_| "Jakarta, Indonesia".to_string()),

            // Monitoring
            enable_metrics: env::var("ENABLE_METRICS")
                .unwrap_or_else(|_| "true".to_string())
                .parse()?,
            metrics_port: env::var("METRICS_PORT")
                .unwrap_or_else(|_| "9090".to_string())
                .parse()?,
        })
    }
}
