use crate::domain::value_objects::*;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use argon2::{Argon2, PasswordHash, PasswordHasher, PasswordVerifier};
use argon2::password_hash::SaltString;
use rand_core::OsRng;
use regex::Regex;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdminUser {
    pub id: AdminUserId,
    pub email: String,
    pub password_hash: String,
    pub name: String,
    pub is_active: bool,
    pub last_login_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub deleted_at: Option<DateTime<Utc>>,
}

impl AdminUser {
    /// Create a new admin user with hashed password
    pub fn new(email: String, password: String, name: String) -> Result<Self, String> {
        // Validate email format
        Self::validate_email(&email)?;

        // Validate password requirements
        Self::validate_password(&password)?;

        // Hash password with bcrypt cost factor 12
        let password_hash = Self::hash_password(&password)?;

        let now = Utc::now();

        Ok(AdminUser {
            id: AdminUserId::new(),
            email,
            password_hash,
            name,
            is_active: true,
            last_login_at: None,
            created_at: now,
            updated_at: now,
            deleted_at: None,
        })
    }

    /// Create admin user from database row (password already hashed)
    pub fn from_db(
        id: uuid::Uuid,
        email: String,
        password_hash: String,
        name: String,
        is_active: bool,
        last_login_at: Option<DateTime<Utc>>,
        created_at: DateTime<Utc>,
        updated_at: DateTime<Utc>,
        deleted_at: Option<DateTime<Utc>>,
    ) -> Self {
        AdminUser {
            id: AdminUserId::from_uuid(id),
            email,
            password_hash,
            name,
            is_active,
            last_login_at,
            created_at,
            updated_at,
            deleted_at,
        }
    }

    /// Verify password against stored hash
    pub fn verify_password(&self, password: &str) -> bool {
        // Use proper Argon2 verification only
        Self::verify_password_static(password, &self.password_hash).unwrap_or(false)
    }

    /// Update password with new hash
    pub fn update_password(&mut self, new_password: String) -> Result<(), String> {
        Self::validate_password(&new_password)?;

        self.password_hash = Self::hash_password(&new_password)?;

        self.updated_at = Utc::now();
        Ok(())
    }

    /// Update last login timestamp
    pub fn update_last_login(&mut self) {
        self.last_login_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Soft delete the admin user
    pub fn soft_delete(&mut self) {
        self.deleted_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Restore soft deleted admin user
    pub fn restore(&mut self) {
        self.deleted_at = None;
        self.updated_at = Utc::now();
    }

    /// Check if admin user is soft deleted
    pub fn is_deleted(&self) -> bool {
        self.deleted_at.is_some()
    }

    /// Hash a password using Argon2 (more secure than bcrypt)
    pub fn hash_password(password: &str) -> Result<String, String> {
        // Validate password strength
        if !Self::is_password_strong(password) {
            return Err("Password does not meet security requirements".to_string());
        }

        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();

        argon2
            .hash_password(password.as_bytes(), &salt)
            .map(|hash| hash.to_string())
            .map_err(|e| format!("Failed to hash password: {}", e))
    }

    /// Verify a password against its Argon2 hash (static method)
    pub fn verify_password_static(password: &str, hash: &str) -> Result<bool, String> {
        let parsed_hash = PasswordHash::new(hash)
            .map_err(|e| format!("Invalid hash format: {}", e))?;

        Ok(Argon2::default()
            .verify_password(password.as_bytes(), &parsed_hash)
            .is_ok())
    }

    /// Validate password strength
    pub fn is_password_strong(password: &str) -> bool {
        // Minimal 8 karakter
        if password.len() < 8 {
            return false;
        }

        // Bisa ditambah validasi lain sesuai kebutuhan:
        // - Huruf besar, kecil, angka, simbol
        // - Blacklist kata umum

        true
    }

    /// Activate admin user account
    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = Utc::now();
    }

    /// Deactivate admin user account
    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = Utc::now();
    }

    /// Check if admin user can login (active and not deleted)
    pub fn can_login(&self) -> bool {
        self.is_active && !self.is_deleted()
    }

    /// Validate email format
    fn validate_email(email: &str) -> Result<(), String> {
        let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
            .map_err(|e| format!("Email regex error: {}", e))?;

        if !email_regex.is_match(email) {
            return Err("Invalid email format".to_string());
        }

        if email.len() > 255 {
            return Err("Email too long (max 255 characters)".to_string());
        }

        Ok(())
    }

    /// Validate password requirements
    fn validate_password(password: &str) -> Result<(), String> {
        if password.len() < 8 {
            return Err("Password must be at least 8 characters long".to_string());
        }

        if password.len() > 255 {
            return Err("Password too long (max 255 characters)".to_string());
        }

        let has_lowercase = password.chars().any(|c| c.is_lowercase());
        let has_uppercase = password.chars().any(|c| c.is_uppercase());
        let has_digit = password.chars().any(|c| c.is_numeric());

        if !has_lowercase {
            return Err("Password must contain at least one lowercase letter".to_string());
        }

        if !has_uppercase {
            return Err("Password must contain at least one uppercase letter".to_string());
        }

        if !has_digit {
            return Err("Password must contain at least one number".to_string());
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_hashing_and_verification() {
        let password = "SecureAdmin123!";

        // Test hashing
        let hash = AdminUser::hash_password(password).expect("Failed to hash password");
        println!("Password: {}", password);
        println!("Generated Argon2 hash: {}", hash);

        // Test verification
        let is_valid = AdminUser::verify_password_static(password, &hash).expect("Failed to verify");
        println!("Verification result: {}", is_valid);

        assert!(is_valid, "Password should be valid");

        // Test wrong password
        let wrong_password = "WrongPassword123!";
        let is_invalid = AdminUser::verify_password_static(wrong_password, &hash).expect("Failed to verify");
        assert!(!is_invalid, "Wrong password should be invalid");
    }

    #[test]
    fn test_password_strength() {
        // Valid passwords
        assert!(AdminUser::is_password_strong("SecureAdmin123!"));
        assert!(AdminUser::is_password_strong("MyPassword123"));
        assert!(AdminUser::is_password_strong("12345678"));

        // Invalid passwords (too short)
        assert!(!AdminUser::is_password_strong("1234567"));
        assert!(!AdminUser::is_password_strong("short"));
        assert!(!AdminUser::is_password_strong(""));
    }
}


