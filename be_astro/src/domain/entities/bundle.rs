use crate::domain::value_objects::*;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bundle {
    pub id: BundleId,
    pub name: String,
    pub description: Option<String>,
    pub discount_percentage: Decimal,
    pub is_active: bool,
    pub image_url: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BundleItem {
    pub bundle_id: BundleId,
    pub product_id: ProductId,
    pub quantity: i32,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BundlePricing {
    pub bundle: Bundle,
    pub items: Vec<BundleItemWithProduct>,
    pub original_total: Price,
    pub discount_amount: Price,
    pub final_price: Price,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BundleItemWithProduct {
    pub product_id: ProductId,
    pub product_name: String,
    pub quantity: i32,
    pub unit_price: Price,
    pub total_price: Price,
}

impl Bundle {
    pub fn new(
        name: String,
        description: Option<String>,
        discount_percentage: Decimal,
    ) -> Result<Self, String> {
        if discount_percentage < Decimal::ZERO || discount_percentage > Decimal::from(100) {
            return Err("Discount percentage must be between 0 and 100".to_string());
        }

        let now = Utc::now();
        Ok(Self {
            id: BundleId::new(),
            name,
            description,
            discount_percentage,
            is_active: true,
            image_url: None,
            created_at: now,
            updated_at: now,
        })
    }

    pub fn update_name(&mut self, name: String) {
        self.name = name;
        self.updated_at = Utc::now();
    }

    pub fn update_description(&mut self, description: Option<String>) {
        self.description = description;
        self.updated_at = Utc::now();
    }

    pub fn update_discount(&mut self, discount_percentage: Decimal) -> Result<(), String> {
        if discount_percentage < Decimal::ZERO || discount_percentage > Decimal::from(100) {
            return Err("Discount percentage must be between 0 and 100".to_string());
        }

        self.discount_percentage = discount_percentage;
        self.updated_at = Utc::now();
        Ok(())
    }

    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = Utc::now();
    }

    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = Utc::now();
    }

    pub fn set_image_url(&mut self, image_url: Option<String>) {
        self.image_url = image_url;
        self.updated_at = Utc::now();
    }
}

impl BundleItem {
    pub fn new(bundle_id: BundleId, product_id: ProductId, quantity: i32) -> Result<Self, String> {
        if quantity <= 0 {
            return Err("Quantity must be positive".to_string());
        }

        Ok(Self {
            bundle_id,
            product_id,
            quantity,
            created_at: Utc::now(),
        })
    }

    pub fn update_quantity(&mut self, new_quantity: i32) -> Result<(), String> {
        if new_quantity <= 0 {
            return Err("Quantity must be positive".to_string());
        }

        self.quantity = new_quantity;
        Ok(())
    }
}

impl BundlePricing {
    pub fn calculate(
        bundle: Bundle,
        items: Vec<BundleItemWithProduct>,
    ) -> Result<Self, String> {
        let mut original_total = Price::zero();

        // Calculate original total
        for item in &items {
            original_total = original_total.add(&item.total_price)?;
        }

        // Calculate discount
        let discount_factor = bundle.discount_percentage / Decimal::from(100);
        let discount_amount = original_total.multiply(discount_factor);
        
        // Calculate final price
        let final_price = Price::new(original_total.amount - discount_amount.amount);

        Ok(Self {
            bundle,
            items,
            original_total,
            discount_amount,
            final_price,
        })
    }

    pub fn savings(&self) -> &Price {
        &self.discount_amount
    }

    pub fn savings_percentage(&self) -> Decimal {
        if self.original_total.amount.is_zero() {
            Decimal::ZERO
        } else {
            (self.discount_amount.amount / self.original_total.amount) * Decimal::from(100)
        }
    }
}

impl BundleItemWithProduct {
    pub fn new(
        product_id: ProductId,
        product_name: String,
        quantity: i32,
        unit_price: Price,
    ) -> Result<Self, String> {
        if quantity <= 0 {
            return Err("Quantity must be positive".to_string());
        }

        let total_price = unit_price.multiply(Decimal::from(quantity));

        Ok(Self {
            product_id,
            product_name,
            quantity,
            unit_price,
            total_price,
        })
    }
}
