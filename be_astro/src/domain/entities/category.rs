use crate::domain::value_objects::*;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Category {
    pub id: CategoryId,
    pub name: String,
    pub description: Option<String>,
    pub parent_id: Option<CategoryId>,
    pub image_url: Option<String>,
    pub is_active: bool,
    pub sort_order: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub deleted_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductCategory {
    pub product_id: ProductId,
    pub category_id: CategoryId,
    pub created_at: DateTime<Utc>,
}



impl Category {
    pub fn new(
        name: String,
        description: Option<String>,
        parent_id: Option<CategoryId>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: CategoryId::new(),
            name,
            description,
            parent_id,
            image_url: None,
            is_active: true,
            sort_order: 0,
            created_at: now,
            updated_at: now,
            deleted_at: None,
        }
    }

    pub fn update_name(&mut self, name: String) {
        self.name = name;
        self.updated_at = Utc::now();
    }

    pub fn update_description(&mut self, description: Option<String>) {
        self.description = description;
        self.updated_at = Utc::now();
    }

    pub fn set_parent(&mut self, parent_id: Option<CategoryId>) {
        self.parent_id = parent_id;
        self.updated_at = Utc::now();
    }

    pub fn set_image_url(&mut self, image_url: Option<String>) {
        self.image_url = image_url;
        self.updated_at = Utc::now();
    }

    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = Utc::now();
    }

    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = Utc::now();
    }

    pub fn set_sort_order(&mut self, sort_order: i32) {
        self.sort_order = sort_order;
        self.updated_at = Utc::now();
    }

    pub fn is_root_category(&self) -> bool {
        self.parent_id.is_none()
    }

    pub fn is_child_of(&self, parent_id: &CategoryId) -> bool {
        self.parent_id.as_ref() == Some(parent_id)
    }

    pub fn soft_delete(&mut self) {
        self.deleted_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    pub fn restore(&mut self) {
        self.deleted_at = None;
        self.updated_at = Utc::now();
    }

    pub fn is_deleted(&self) -> bool {
        self.deleted_at.is_some()
    }
}

impl ProductCategory {
    pub fn new(product_id: ProductId, category_id: CategoryId) -> Self {
        Self {
            product_id,
            category_id,
            created_at: Utc::now(),
        }
    }
}
