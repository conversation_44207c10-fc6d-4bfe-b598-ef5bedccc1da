use crate::domain::value_objects::*;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Order {
    pub id: OrderId,
    pub order_number: OrderNumber,
    pub customer_name: String,
    pub customer_email: Option<Email>,
    pub customer_phone: PhoneNumber,
    pub total_amount: Price,
    pub status: OrderStatus,
    pub notes: Option<String>,
    pub whatsapp_url: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderItem {
    pub id: OrderId,
    pub order_id: OrderId,
    pub product_id: ProductId,
    pub product_name: String,
    pub quantity: i32,
    pub unit_price: Price,
    pub total_price: Price,
    pub configuration: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum OrderStatus {
    Pending,
    Confirmed,
    Processing,
    Shipped,
    Delivered,
    Cancelled,
    Refunded,
}

impl Order {
    pub fn new(
        customer_name: String,
        customer_email: Option<Email>,
        customer_phone: PhoneNumber,
        total_amount: Price,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: OrderId::new(),
            order_number: OrderNumber::generate(),
            customer_name,
            customer_email,
            customer_phone,
            total_amount,
            status: OrderStatus::Pending,
            notes: None,
            whatsapp_url: None,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn confirm(&mut self) -> Result<(), String> {
        match self.status {
            OrderStatus::Pending => {
                self.status = OrderStatus::Confirmed;
                self.updated_at = Utc::now();
                Ok(())
            }
            _ => Err("Order can only be confirmed from pending status".to_string()),
        }
    }

    pub fn start_processing(&mut self) -> Result<(), String> {
        match self.status {
            OrderStatus::Confirmed => {
                self.status = OrderStatus::Processing;
                self.updated_at = Utc::now();
                Ok(())
            }
            _ => Err("Order can only be processed from confirmed status".to_string()),
        }
    }

    pub fn ship(&mut self) -> Result<(), String> {
        match self.status {
            OrderStatus::Processing => {
                self.status = OrderStatus::Shipped;
                self.updated_at = Utc::now();
                Ok(())
            }
            _ => Err("Order can only be shipped from processing status".to_string()),
        }
    }

    pub fn deliver(&mut self) -> Result<(), String> {
        match self.status {
            OrderStatus::Shipped => {
                self.status = OrderStatus::Delivered;
                self.updated_at = Utc::now();
                Ok(())
            }
            _ => Err("Order can only be delivered from shipped status".to_string()),
        }
    }

    pub fn cancel(&mut self) -> Result<(), String> {
        match self.status {
            OrderStatus::Delivered | OrderStatus::Cancelled | OrderStatus::Refunded => {
                Err("Cannot cancel order in current status".to_string())
            }
            _ => {
                self.status = OrderStatus::Cancelled;
                self.updated_at = Utc::now();
                Ok(())
            }
        }
    }

    pub fn refund(&mut self) -> Result<(), String> {
        match self.status {
            OrderStatus::Delivered | OrderStatus::Confirmed | OrderStatus::Processing => {
                self.status = OrderStatus::Refunded;
                self.updated_at = Utc::now();
                Ok(())
            }
            _ => Err("Cannot refund order in current status".to_string()),
        }
    }

    pub fn add_notes(&mut self, notes: String) {
        self.notes = Some(notes);
        self.updated_at = Utc::now();
    }

    pub fn set_whatsapp_url(&mut self, url: String) {
        self.whatsapp_url = Some(url);
        self.updated_at = Utc::now();
    }

    pub fn is_modifiable(&self) -> bool {
        matches!(self.status, OrderStatus::Pending | OrderStatus::Confirmed)
    }

    pub fn is_cancellable(&self) -> bool {
        !matches!(
            self.status,
            OrderStatus::Delivered | OrderStatus::Cancelled | OrderStatus::Refunded
        )
    }
}

impl OrderItem {
    pub fn new(
        order_id: OrderId,
        product_id: ProductId,
        product_name: String,
        quantity: i32,
        unit_price: Price,
        configuration: Option<serde_json::Value>,
    ) -> Result<Self, String> {
        if quantity <= 0 {
            return Err("Quantity must be positive".to_string());
        }

        let total_price = unit_price.multiply(rust_decimal::Decimal::from(quantity));

        Ok(Self {
            id: OrderId::new(),
            order_id,
            product_id,
            product_name,
            quantity,
            unit_price,
            total_price,
            configuration,
            created_at: Utc::now(),
        })
    }

    pub fn update_quantity(&mut self, new_quantity: i32) -> Result<(), String> {
        if new_quantity <= 0 {
            return Err("Quantity must be positive".to_string());
        }

        self.quantity = new_quantity;
        self.total_price = self.unit_price.multiply(rust_decimal::Decimal::from(new_quantity));
        Ok(())
    }
}

impl OrderStatus {
    pub fn as_str(&self) -> &'static str {
        match self {
            OrderStatus::Pending => "pending",
            OrderStatus::Confirmed => "confirmed",
            OrderStatus::Processing => "processing",
            OrderStatus::Shipped => "shipped",
            OrderStatus::Delivered => "delivered",
            OrderStatus::Cancelled => "cancelled",
            OrderStatus::Refunded => "refunded",
        }
    }

    pub fn from_str(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "pending" => Ok(OrderStatus::Pending),
            "confirmed" => Ok(OrderStatus::Confirmed),
            "processing" => Ok(OrderStatus::Processing),
            "shipped" => Ok(OrderStatus::Shipped),
            "delivered" => Ok(OrderStatus::Delivered),
            "cancelled" => Ok(OrderStatus::Cancelled),
            "refunded" => Ok(OrderStatus::Refunded),
            _ => Err(format!("Invalid order status: {}", s)),
        }
    }
}
