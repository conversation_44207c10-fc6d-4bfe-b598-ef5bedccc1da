use crate::domain::value_objects::*;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Product {
    pub id: ProductId,
    pub name: String,
    pub slug: Option<String>,
    pub description: Option<String>,
    pub base_price: Price,
    pub is_featured: bool,
    pub is_active: bool,
    pub image_url: Option<String>,
    pub specifications: Option<HashMap<String, serde_json::Value>>,
    pub dimensions: Option<HashMap<String, serde_json::Value>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductDimension {
    pub id: ProductId,
    pub name: String,
    pub price_modifier_type: PriceModifierType,
    pub price_modifier_value: Decimal,
    pub is_default: bool,
    pub sort_order: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductTheme {
    pub id: ProductId,
    pub name: String,
    pub price_modifier_type: PriceModifierType,
    pub price_modifier_value: Decimal,
    pub is_default: bool,
    pub sort_order: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductAccessory {
    pub id: AccessoryId,
    pub name: String,
    pub description: Option<String>,
    pub price: Price,
    pub is_required: bool,
    pub category: String,
    pub image_url: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PriceModifierType {
    Addition,
    Subtraction,
    Replacement,
    Percentage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductConfiguration {
    pub product_id: ProductId,
    pub dimension_name: Option<String>,
    pub theme_name: Option<String>,
    pub accessories: Vec<AccessorySelection>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessorySelection {
    pub accessory_id: AccessoryId,
    pub quantity: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceCalculation {
    pub base_price: Price,
    pub dimension_adjustment: Price,
    pub theme_adjustment: Price,
    pub accessories_total: Price,
    pub final_price: Price,
    pub breakdown: Vec<PriceItem>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceItem {
    pub name: String,
    pub price: Price,
    pub item_type: PriceItemType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PriceItemType {
    Base,
    Dimension,
    Theme,
    Accessory,
}

impl Product {
    pub fn new(
        name: String,
        slug: Option<String>,
        description: Option<String>,
        base_price: Price,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: ProductId::new(),
            name,
            slug,
            description,
            base_price,
            is_featured: false,
            is_active: true,
            image_url: None,
            specifications: None,
            dimensions: None,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update_price(&mut self, new_price: Price) {
        self.base_price = new_price;
        self.updated_at = Utc::now();
    }

    pub fn set_featured(&mut self, featured: bool) {
        self.is_featured = featured;
        self.updated_at = Utc::now();
    }

    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = Utc::now();
    }

    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = Utc::now();
    }

    pub fn update_specifications(&mut self, specs: HashMap<String, serde_json::Value>) {
        self.specifications = Some(specs);
        self.updated_at = Utc::now();
    }

    pub fn update_dimensions(&mut self, dims: HashMap<String, serde_json::Value>) {
        self.dimensions = Some(dims);
        self.updated_at = Utc::now();
    }
}

impl ProductAccessory {
    pub fn new(
        name: String,
        description: Option<String>,
        price: Price,
        is_required: bool,
        category: String,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: AccessoryId::new(),
            name,
            description,
            price,
            is_required,
            category,
            image_url: None,
            is_active: true,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update_price(&mut self, new_price: Price) {
        self.price = new_price;
        self.updated_at = Utc::now();
    }

    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = Utc::now();
    }

    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = Utc::now();
    }
}

impl PriceCalculation {
    pub fn new(base_price: Price) -> Self {
        Self {
            base_price: base_price.clone(),
            dimension_adjustment: Price::zero(),
            theme_adjustment: Price::zero(),
            accessories_total: Price::zero(),
            final_price: base_price.clone(),
            breakdown: vec![PriceItem {
                name: "Base Price".to_string(),
                price: base_price,
                item_type: PriceItemType::Base,
            }],
        }
    }

    pub fn add_dimension_adjustment(&mut self, name: String, adjustment: Price) -> Result<(), String> {
        self.dimension_adjustment = adjustment.clone();
        self.breakdown.push(PriceItem {
            name: format!("Dimension: {}", name),
            price: adjustment,
            item_type: PriceItemType::Dimension,
        });
        self.recalculate_final_price()
    }

    pub fn add_theme_adjustment(&mut self, name: String, adjustment: Price) -> Result<(), String> {
        self.theme_adjustment = adjustment.clone();
        self.breakdown.push(PriceItem {
            name: format!("Theme: {}", name),
            price: adjustment,
            item_type: PriceItemType::Theme,
        });
        self.recalculate_final_price()
    }

    pub fn add_accessory(&mut self, name: String, price: Price, quantity: i32) -> Result<(), String> {
        let total_price = price.multiply(Decimal::from(quantity));
        self.accessories_total = self.accessories_total.add(&total_price)?;
        self.breakdown.push(PriceItem {
            name: format!("{} (x{})", name, quantity),
            price: total_price,
            item_type: PriceItemType::Accessory,
        });
        self.recalculate_final_price()
    }

    fn recalculate_final_price(&mut self) -> Result<(), String> {
        let mut total = self.base_price.clone();
        total = total.add(&self.dimension_adjustment)?;
        total = total.add(&self.theme_adjustment)?;
        total = total.add(&self.accessories_total)?;
        self.final_price = total;
        Ok(())
    }
}
