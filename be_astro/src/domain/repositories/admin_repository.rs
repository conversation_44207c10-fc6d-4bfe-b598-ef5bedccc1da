use crate::domain::entities::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;

#[async_trait]
pub trait AdminRepository: Send + Sync {
    async fn find_by_id(&self, id: &AdminUserId) -> Result<Option<AdminUser>, String>;
    async fn find_by_username(&self, username: &str) -> Result<Option<AdminUser>, String>;
    async fn find_by_email(&self, email: &Email) -> Result<Option<AdminUser>, String>;
    async fn find_all(&self, page: u32, per_page: u32) -> Result<Vec<AdminUser>, String>;
    async fn count(&self) -> Result<u64, String>;
    async fn save(&self, admin_user: &AdminUser) -> Result<(), String>;
    async fn delete(&self, id: &AdminUserId) -> Result<(), String>;
    
    // Authentication
    async fn authenticate(&self, username: &str, password: &str) -> Result<Option<AdminUser>, String>;
    async fn update_last_login(&self, id: &AdminUserId) -> Result<(), String>;
}
