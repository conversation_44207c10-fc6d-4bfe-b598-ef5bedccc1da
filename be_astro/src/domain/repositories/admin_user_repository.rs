use async_trait::async_trait;
use crate::domain::entities::AdminUser;
use crate::domain::value_objects::AdminUserId;

#[async_trait]
pub trait AdminUserRepository: Send + Sync {
    /// Find all active admin users (not soft deleted)
    async fn find_all(&self) -> Result<Vec<AdminUser>, String>;
    
    /// Find admin user by ID (only active, not soft deleted)
    async fn find_by_id(&self, id: &AdminUserId) -> Result<Option<AdminUser>, String>;
    
    /// Find admin user by ID including soft deleted ones
    async fn find_by_id_including_deleted(&self, id: &AdminUserId) -> Result<Option<AdminUser>, String>;
    
    /// Find admin user by email (only active, not soft deleted)
    async fn find_by_email(&self, email: &str) -> Result<Option<AdminUser>, String>;
    
    /// Find admin user by email including soft deleted ones (for login validation)
    async fn find_by_email_including_deleted(&self, email: &str) -> Result<Option<AdminUser>, String>;
    
    /// Save admin user (create or update)
    async fn save(&self, admin_user: &AdminUser) -> Result<(), String>;
    
    /// Soft delete admin user by setting deleted_at timestamp
    async fn soft_delete(&self, id: &AdminUserId) -> Result<(), String>;
    
    /// Restore soft deleted admin user by clearing deleted_at timestamp
    async fn restore(&self, id: &AdminUserId) -> Result<(), String>;
    
    /// Check if email exists (excluding soft deleted users)
    async fn email_exists(&self, email: &str) -> Result<bool, String>;
    
    /// Count total active admin users
    async fn count_active(&self) -> Result<i64, String>;
}
