use crate::domain::entities::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;

#[async_trait]
pub trait BundleRepository: Send + Sync {
    async fn find_by_id(&self, id: &BundleId) -> Result<Option<Bundle>, String>;
    async fn find_all(&self, page: u32, per_page: u32) -> Result<Vec<Bundle>, String>;
    async fn find_active(&self) -> Result<Vec<Bundle>, String>;
    async fn count(&self) -> Result<u64, String>;
    async fn save(&self, bundle: &Bundle) -> Result<(), String>;
    async fn delete(&self, id: &BundleId) -> Result<(), String>;
    
    // Bundle items
    async fn find_items(&self, bundle_id: &BundleId) -> Result<Vec<BundleItem>, String>;
    async fn save_item(&self, item: &BundleItem) -> Result<(), String>;
    async fn delete_item(&self, bundle_id: &BundleId, product_id: &ProductId) -> Result<(), String>;
    async fn delete_all_items(&self, bundle_id: &BundleId) -> Result<(), String>;
    
    // Bundle pricing
    async fn calculate_bundle_pricing(&self, bundle_id: &BundleId) -> Result<BundlePricing, String>;
    async fn find_bundle_with_products(&self, bundle_id: &BundleId) -> Result<Option<BundleWithProducts>, String>;
}

#[derive(Debug, Clone)]
pub struct BundleWithProducts {
    pub bundle: Bundle,
    pub items: Vec<BundleItemWithProduct>,
}
