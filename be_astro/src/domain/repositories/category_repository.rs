use crate::domain::entities::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;

#[async_trait]
pub trait CategoryRepository: Send + Sync {
    async fn find_by_id(&self, id: &CategoryId) -> Result<Option<Category>, String>;
    async fn find_by_id_including_deleted(&self, id: &CategoryId) -> Result<Option<Category>, String>;
    async fn find_all(&self) -> Result<Vec<Category>, String>;
    async fn find_root_categories(&self) -> Result<Vec<Category>, String>;
    async fn find_children(&self, parent_id: &CategoryId) -> Result<Vec<Category>, String>;
    async fn find_by_parent(&self, parent_id: Option<&CategoryId>) -> Result<Vec<Category>, String>;
    async fn count(&self) -> Result<u64, String>;
    async fn save(&self, category: &Category) -> Result<(), String>;
    async fn delete(&self, id: &CategoryId) -> Result<(), String>;
    async fn soft_delete(&self, id: &CategoryId) -> Result<(), String>;
    async fn restore(&self, id: &CategoryId) -> Result<(), String>;
    
    // Category hierarchy operations
    async fn get_category_path(&self, id: &CategoryId) -> Result<Vec<Category>, String>;
    async fn has_children(&self, id: &CategoryId) -> Result<bool, String>;
    async fn get_category_tree(&self) -> Result<Vec<CategoryWithChildren>, String>;
}

#[derive(Debug, Clone)]
pub struct CategoryWithChildren {
    pub category: Category,
    pub children: Vec<CategoryWithChildren>,
}
