use crate::domain::entities::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;
use chrono::{DateTime, Utc};

#[async_trait]
pub trait OrderRepository: Send + Sync {
    async fn find_by_id(&self, id: &OrderId) -> Result<Option<Order>, String>;
    async fn find_by_order_number(&self, order_number: &OrderNumber) -> Result<Option<Order>, String>;
    async fn find_all(&self, page: u32, per_page: u32) -> Result<Vec<Order>, String>;
    async fn find_by_status(&self, status: &OrderStatus, page: u32, per_page: u32) -> Result<Vec<Order>, String>;
    async fn find_by_customer_phone(&self, phone: &PhoneNumber) -> Result<Vec<Order>, String>;
    async fn find_by_date_range(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
        page: u32,
        per_page: u32,
    ) -> Result<Vec<Order>, String>;
    async fn count(&self) -> Result<u64, String>;
    async fn count_by_status(&self, status: &OrderStatus) -> Result<u64, String>;
    async fn save(&self, order: &Order) -> Result<(), String>;
    async fn delete(&self, id: &OrderId) -> Result<(), String>;
    
    // Order items
    async fn find_items(&self, order_id: &OrderId) -> Result<Vec<OrderItem>, String>;
    async fn save_item(&self, item: &OrderItem) -> Result<(), String>;
    async fn delete_item(&self, item_id: &OrderId) -> Result<(), String>;
    async fn delete_all_items(&self, order_id: &OrderId) -> Result<(), String>;
    
    // Statistics
    async fn get_total_revenue(&self) -> Result<rust_decimal::Decimal, String>;
    async fn get_revenue_by_date_range(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<rust_decimal::Decimal, String>;
    async fn get_order_statistics(&self) -> Result<OrderStatistics, String>;
}

#[derive(Debug, Clone)]
pub struct OrderStatistics {
    pub total_orders: u64,
    pub pending_orders: u64,
    pub confirmed_orders: u64,
    pub processing_orders: u64,
    pub shipped_orders: u64,
    pub delivered_orders: u64,
    pub cancelled_orders: u64,
    pub refunded_orders: u64,
    pub total_revenue: rust_decimal::Decimal,
    pub average_order_value: rust_decimal::Decimal,
}
