use crate::domain::entities::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;


#[async_trait]
pub trait ProductRepository: Send + Sync {
    async fn find_by_id(&self, id: &ProductId) -> Result<Option<Product>, String>;
    async fn find_by_slug(&self, slug: &str) -> Result<Option<Product>, String>;
    async fn find_all(&self, page: u32, per_page: u32) -> Result<Vec<Product>, String>;
    async fn find_featured(&self, limit: Option<u32>) -> Result<Vec<Product>, String>;
    async fn find_by_category(&self, category_id: &CategoryId) -> Result<Vec<Product>, String>;
    async fn find_by_accessory_categories(&self) -> Result<Vec<Product>, String>;
    async fn search(&self, query: &str, page: u32, per_page: u32) -> Result<Vec<Product>, String>;
    async fn count(&self) -> Result<u64, String>;
    async fn save(&self, product: &Product) -> Result<(), String>;
    async fn delete(&self, id: &ProductId) -> Result<(), String>;
    
    // Product dimensions and themes
    async fn find_dimensions(&self, product_id: &ProductId) -> Result<Vec<ProductDimension>, String>;
    async fn find_themes(&self, product_id: &ProductId) -> Result<Vec<ProductTheme>, String>;
    async fn save_dimension(&self, dimension: &ProductDimension) -> Result<(), String>;
    async fn save_theme(&self, theme: &ProductTheme) -> Result<(), String>;
    
    // Product categories
    async fn add_to_category(&self, product_id: &ProductId, category_id: &CategoryId) -> Result<(), String>;
    async fn remove_from_category(&self, product_id: &ProductId, category_id: &CategoryId) -> Result<(), String>;
    async fn find_categories(&self, product_id: &ProductId) -> Result<Vec<Category>, String>;
}

#[async_trait]
pub trait ProductAccessoryRepository: Send + Sync {
    async fn find_by_id(&self, id: &AccessoryId) -> Result<Option<ProductAccessory>, String>;
    async fn find_all(&self, page: u32, per_page: u32) -> Result<Vec<ProductAccessory>, String>;
    async fn find_by_category(&self, category: &str) -> Result<Vec<ProductAccessory>, String>;
    async fn find_active(&self) -> Result<Vec<ProductAccessory>, String>;
    async fn count(&self) -> Result<u64, String>;
    async fn save(&self, accessory: &ProductAccessory) -> Result<(), String>;
    async fn delete(&self, id: &AccessoryId) -> Result<(), String>;
}

#[async_trait]
pub trait ProductPricingRepository: Send + Sync {
    async fn calculate_price(
        &self,
        product_id: &ProductId,
        configuration: &ProductConfiguration,
    ) -> Result<PriceCalculation, String>;
    
    async fn get_dimension_modifier(
        &self,
        product_id: &ProductId,
        dimension_name: &str,
    ) -> Result<Option<(PriceModifierType, rust_decimal::Decimal)>, String>;
    
    async fn get_theme_modifier(
        &self,
        product_id: &ProductId,
        theme_name: &str,
    ) -> Result<Option<(PriceModifierType, rust_decimal::Decimal)>, String>;
    
    async fn get_accessories_by_ids(
        &self,
        accessory_ids: &[AccessoryId],
    ) -> Result<Vec<ProductAccessory>, String>;
}
