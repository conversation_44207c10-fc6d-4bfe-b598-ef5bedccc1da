use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use std::sync::Arc;

pub struct OrderService {
    order_repo: Arc<dyn OrderRepository>,
}

impl OrderService {
    pub fn new(order_repo: Arc<dyn OrderRepository>) -> Self {
        Self { order_repo }
    }

    pub async fn create_order(
        &self,
        customer_name: String,
        customer_email: Option<String>,
        customer_phone: String,
        items: Vec<CreateOrderItem>,
    ) -> Result<Order, String> {
        // Validate customer data
        let email = if let Some(email_str) = customer_email {
            Some(Email::new(email_str)?)
        } else {
            None
        };
        let phone = PhoneNumber::new(customer_phone)?;

        // Calculate total amount
        let mut total_amount = Price::zero();
        for item in &items {
            let item_total = item.unit_price.multiply(rust_decimal::Decimal::from(item.quantity));
            total_amount = total_amount.add(&item_total)?;
        }

        // Create order
        let order = Order::new(customer_name, email, phone, total_amount);

        // Save order
        self.order_repo.save(&order).await?;

        // Create and save order items
        for item_data in items {
            let order_item = OrderItem::new(
                order.id.clone(),
                item_data.product_id,
                item_data.product_name,
                item_data.quantity,
                item_data.unit_price,
                item_data.configuration,
            )?;
            self.order_repo.save_item(&order_item).await?;
        }

        Ok(order)
    }

    pub async fn update_order_status(
        &self,
        order_id: &OrderId,
        new_status: OrderStatus,
    ) -> Result<(), String> {
        let mut order = self
            .order_repo
            .find_by_id(order_id)
            .await?
            .ok_or("Order not found")?;

        match new_status {
            OrderStatus::Confirmed => order.confirm()?,
            OrderStatus::Processing => order.start_processing()?,
            OrderStatus::Shipped => order.ship()?,
            OrderStatus::Delivered => order.deliver()?,
            OrderStatus::Cancelled => order.cancel()?,
            OrderStatus::Refunded => order.refund()?,
            OrderStatus::Pending => return Err("Cannot set order back to pending".to_string()),
        }

        self.order_repo.save(&order).await
    }

    pub async fn cancel_order(&self, order_id: &OrderId) -> Result<(), String> {
        let mut order = self
            .order_repo
            .find_by_id(order_id)
            .await?
            .ok_or("Order not found")?;

        if !order.is_cancellable() {
            return Err("Order cannot be cancelled in current status".to_string());
        }

        order.cancel()?;
        self.order_repo.save(&order).await
    }

    pub async fn add_order_notes(&self, order_id: &OrderId, notes: String) -> Result<(), String> {
        let mut order = self
            .order_repo
            .find_by_id(order_id)
            .await?
            .ok_or("Order not found")?;

        order.add_notes(notes);
        self.order_repo.save(&order).await
    }

    pub async fn generate_whatsapp_url(
        &self,
        order: &Order,
        whatsapp_base_url: &str,
        whatsapp_number: &str,
    ) -> Result<String, String> {
        let message = format!(
            "Halo, saya ingin konfirmasi pesanan:\n\n*Order #{}*\n\nNama: {}\nTotal: Rp {:.0}\n\nTerima kasih!",
            order.order_number.0,
            order.customer_name,
            order.total_amount.amount
        );

        let encoded_message = urlencoding::encode(&message);
        let url = format!(
            "{}{}?text={}",
            whatsapp_base_url,
            whatsapp_number.replace(['+', ' ', '-'], ""),
            encoded_message
        );

        Ok(url)
    }

    pub async fn get_order_with_items(&self, order_id: &OrderId) -> Result<OrderWithItems, String> {
        let order = self
            .order_repo
            .find_by_id(order_id)
            .await?
            .ok_or("Order not found")?;

        let items = self.order_repo.find_items(order_id).await?;

        Ok(OrderWithItems { order, items })
    }

    pub async fn validate_order_modification(&self, order_id: &OrderId) -> Result<(), String> {
        let order = self
            .order_repo
            .find_by_id(order_id)
            .await?
            .ok_or("Order not found")?;

        if !order.is_modifiable() {
            return Err("Order cannot be modified in current status".to_string());
        }

        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct CreateOrderItem {
    pub product_id: ProductId,
    pub product_name: String,
    pub quantity: i32,
    pub unit_price: Price,
    pub configuration: Option<serde_json::Value>,
}

#[derive(Debug, Clone)]
pub struct OrderWithItems {
    pub order: Order,
    pub items: Vec<OrderItem>,
}
