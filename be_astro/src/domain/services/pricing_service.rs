use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use rust_decimal::Decimal;
use std::sync::Arc;

pub struct PricingService {
    product_pricing_repo: Arc<dyn ProductPricingRepository>,
}

impl PricingService {
    pub fn new(product_pricing_repo: Arc<dyn ProductPricingRepository>) -> Self {
        Self {
            product_pricing_repo,
        }
    }

    pub async fn calculate_product_price(
        &self,
        product: &Product,
        configuration: &ProductConfiguration,
    ) -> Result<PriceCalculation, String> {
        let mut calculation = PriceCalculation::new(product.base_price.clone());

        // Apply dimension modifier
        if let Some(dimension_name) = &configuration.dimension_name {
            if let Some((modifier_type, modifier_value)) = self
                .product_pricing_repo
                .get_dimension_modifier(&product.id, dimension_name)
                .await?
            {
                let adjustment = self.apply_price_modifier(
                    &product.base_price,
                    &modifier_type,
                    modifier_value,
                )?;
                calculation.add_dimension_adjustment(dimension_name.clone(), adjustment)?;
            }
        }

        // Apply theme modifier
        if let Some(theme_name) = &configuration.theme_name {
            if let Some((modifier_type, modifier_value)) = self
                .product_pricing_repo
                .get_theme_modifier(&product.id, theme_name)
                .await?
            {
                let adjustment = self.apply_price_modifier(
                    &product.base_price,
                    &modifier_type,
                    modifier_value,
                )?;
                calculation.add_theme_adjustment(theme_name.clone(), adjustment)?;
            }
        }

        // Apply accessories
        if !configuration.accessories.is_empty() {
            let accessory_ids: Vec<AccessoryId> = configuration
                .accessories
                .iter()
                .map(|a| a.accessory_id.clone())
                .collect();

            let accessories = self
                .product_pricing_repo
                .get_accessories_by_ids(&accessory_ids)
                .await?;

            for selection in &configuration.accessories {
                if let Some(accessory) = accessories
                    .iter()
                    .find(|a| a.id == selection.accessory_id)
                {
                    calculation.add_accessory(
                        accessory.name.clone(),
                        accessory.price.clone(),
                        selection.quantity,
                    )?;
                }
            }
        }

        Ok(calculation)
    }

    fn apply_price_modifier(
        &self,
        base_price: &Price,
        modifier_type: &PriceModifierType,
        modifier_value: Decimal,
    ) -> Result<Price, String> {
        match modifier_type {
            PriceModifierType::Addition => {
                Ok(Price::new(modifier_value))
            }
            PriceModifierType::Subtraction => {
                Ok(Price::new(-modifier_value))
            }
            PriceModifierType::Replacement => {
                Ok(Price::new(modifier_value - base_price.amount))
            }
            PriceModifierType::Percentage => {
                let adjustment = base_price.amount * (modifier_value / Decimal::from(100));
                Ok(Price::new(adjustment))
            }
        }
    }

    pub async fn calculate_bundle_savings(
        &self,
        bundle: &Bundle,
        items: &[BundleItemWithProduct],
    ) -> Result<BundlePricing, String> {
        BundlePricing::calculate(bundle.clone(), items.to_vec())
    }
}
