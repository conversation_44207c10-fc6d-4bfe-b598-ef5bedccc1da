use serde::{Deserialize, Serialize};
use std::fmt;
use uuid::Uuid;
use rust_decimal::Decimal;

// ID Value Objects
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ProductId(pub Uuid);

#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct CategoryId(pub Uuid);

#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct BundleId(pub Uuid);

#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct OrderId(pub Uuid);

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct AdminUserId(pub Uuid);

#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct AccessoryId(pub Uuid);

// Price Value Object
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct Price {
    pub amount: Decimal,
    pub currency: Currency,
}

#[derive(Debug, <PERSON>lone, PartialEq, Eq, Serialize, Deserialize)]
pub enum Currency {
    IDR,
}

// Email Value Object
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct Email(pub String);

// Phone Number Value Object
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct PhoneNumber(pub String);

// Order Number Value Object
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct OrderNumber(pub String);

// Implementations for ID types
impl ProductId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
}

impl CategoryId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
}

impl BundleId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
}

impl OrderId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
}

impl AdminUserId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
}

impl AccessoryId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
}

// Price implementations
impl Price {
    pub fn new(amount: Decimal) -> Self {
        Self {
            amount,
            currency: Currency::IDR,
        }
    }
    
    pub fn zero() -> Self {
        Self::new(Decimal::ZERO)
    }
    
    pub fn add(&self, other: &Price) -> Result<Price, String> {
        if self.currency != other.currency {
            return Err("Cannot add prices with different currencies".to_string());
        }
        Ok(Price::new(self.amount + other.amount))
    }
    
    pub fn multiply(&self, factor: Decimal) -> Price {
        Price::new(self.amount * factor)
    }
}

// Email validation
impl Email {
    pub fn new(email: String) -> Result<Self, String> {
        if email.contains('@') && email.contains('.') {
            Ok(Self(email))
        } else {
            Err("Invalid email format".to_string())
        }
    }
}

// Phone number validation
impl PhoneNumber {
    pub fn new(phone: String) -> Result<Self, String> {
        let cleaned = phone.replace([' ', '-', '(', ')'], "");
        if cleaned.chars().all(|c| c.is_ascii_digit() || c == '+') && cleaned.len() >= 10 {
            Ok(Self(cleaned))
        } else {
            Err("Invalid phone number format".to_string())
        }
    }
}

// Order number generation
impl OrderNumber {
    pub fn generate() -> Self {
        let timestamp = chrono::Utc::now().format("%Y%m%d%H%M%S");
        let random: u32 = rand::random::<u32>() % 1000;
        Self(format!("ORD-{}-{:03}", timestamp, random))
    }
    
    pub fn from_string(order_number: String) -> Self {
        Self(order_number)
    }
}

// Display implementations
impl fmt::Display for ProductId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl fmt::Display for CategoryId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl fmt::Display for BundleId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl fmt::Display for OrderId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl fmt::Display for AdminUserId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl fmt::Display for AccessoryId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl fmt::Display for Price {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{} {:?}", self.amount, self.currency)
    }
}

impl fmt::Display for Email {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl fmt::Display for PhoneNumber {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl fmt::Display for OrderNumber {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}
