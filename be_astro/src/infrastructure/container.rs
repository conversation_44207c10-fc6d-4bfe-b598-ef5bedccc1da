use crate::application::services::*;
use crate::domain::services::*;
use crate::infrastructure::{repositories::*, storage::FileStorage};
use crate::config::Config;
use sqlx::PgPool;
use std::sync::Arc;
use std::time::Duration;

pub struct ServiceContainer {
    // Configuration
    pub config: Arc<Config>,

    // Repositories
    pub admin_repo: Arc<SqlxAdminRepository>,
    pub admin_user_repo: Arc<SqlxAdminUserRepository>,
    pub product_repo: Arc<SqlxProductRepository>,
    pub category_repo: Arc<SqlxCategoryRepository>,
    pub product_accessory_repo: Arc<SqlxProductAccessoryRepository>,
    pub product_pricing_repo: Arc<SqlxProductPricingRepository>,

    // Domain Services
    pub pricing_service: Arc<PricingService>,

    // Infrastructure Services
    pub file_storage: Arc<FileStorage>,

    // Application Services
    pub admin_service: Arc<AdminService>,
    pub auth_service: Arc<AuthService>,
    pub category_service: Arc<CategoryService>,
    pub product_service: Arc<ProductService>,
}

impl ServiceContainer {
    pub fn new(pool: PgPool, config: Config) -> Self {
        let config = Arc::new(config);

        // Create repositories
        let admin_repo = Arc::new(SqlxAdminRepository::new(pool.clone()));
        let admin_user_repo = Arc::new(SqlxAdminUserRepository::new(pool.clone()));
        let product_repo = Arc::new(SqlxProductRepository::new(pool.clone()));
        let category_repo = Arc::new(SqlxCategoryRepository::new(pool.clone()));
        let product_accessory_repo = Arc::new(SqlxProductAccessoryRepository::new(pool.clone()));
        let product_pricing_repo = Arc::new(SqlxProductPricingRepository::new(pool.clone()));

        // Create domain services
        let pricing_service = Arc::new(PricingService::new(product_pricing_repo.clone()));

        // Create infrastructure services
        let static_url = format!("{}/static", config.api_base_url);
        let file_storage = Arc::new(FileStorage::new("be_astro/static", &static_url));

        // Create application services
        let admin_service = Arc::new(AdminService::new(admin_repo.clone()));

        // Create auth service with JWT configuration
        let jwt_expires_in = Duration::from_secs(24 * 60 * 60); // 24 hours
        let auth_service = Arc::new(AuthService::new(
            admin_user_repo.clone(),
            config.jwt_secret.clone(),
            jwt_expires_in,
        ));

        let category_service = Arc::new(CategoryService::new(category_repo.clone()));
        let product_service = Arc::new(ProductService::new(
            product_repo.clone(),
            pricing_service.clone(),
        ));

        Self {
            config,
            admin_repo,
            admin_user_repo,
            product_repo,
            category_repo,
            product_accessory_repo,
            product_pricing_repo,
            pricing_service,
            file_storage,
            admin_service,
            auth_service,
            category_service,
            product_service,
        }
    }
}
