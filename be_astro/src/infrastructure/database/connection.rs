use sqlx::{postgres::PgPoolOptions, PgPool};
use std::time::Duration;

pub async fn create_connection_pool(database_url: &str) -> Result<PgPool, sqlx::Error> {
    PgPoolOptions::new()
        .max_connections(20)
        .min_connections(5)
        .acquire_timeout(Duration::from_secs(30))
        .idle_timeout(Duration::from_secs(600))
        .max_lifetime(Duration::from_secs(1800))
        .connect(database_url)
        .await
}

pub async fn run_migrations(_pool: &PgPool) -> Result<(), sqlx::migrate::MigrateError> {
    // Skip migrations for now
    // sqlx::migrate!("./migrations").run(pool).await
    Ok(())
}

pub async fn test_connection(pool: &PgPool) -> Result<(), sqlx::Error> {
    sqlx::query("SELECT 1").execute(pool).await?;
    Ok(())
}
