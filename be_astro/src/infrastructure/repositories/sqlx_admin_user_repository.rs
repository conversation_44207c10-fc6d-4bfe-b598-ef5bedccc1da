use async_trait::async_trait;
use sqlx::{PgPool, Row};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::domain::entities::AdminUser;
use crate::domain::repositories::AdminUserRepository;
use crate::domain::value_objects::AdminUserId;

pub struct SqlxAdminUserRepository {
    pool: PgPool,
}

impl SqlxAdminUserRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl AdminUserRepository for SqlxAdminUserRepository {
    async fn find_all(&self) -> Result<Vec<AdminUser>, String> {
        let rows = sqlx::query(
            "SELECT id, email, password_hash, name, is_active, last_login_at, created_at, updated_at, deleted_at FROM admin_users WHERE deleted_at IS NULL ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let admin_users = rows
            .into_iter()
            .map(|row| {
                AdminUser::from_db(
                    row.get::<Uuid, _>("id"),
                    row.get::<String, _>("email"),
                    row.get::<String, _>("password_hash"),
                    row.get::<String, _>("name"),
                    row.get::<bool, _>("is_active"),
                    row.get::<Option<DateTime<Utc>>, _>("last_login_at"),
                    row.get::<DateTime<Utc>, _>("created_at"),
                    row.get::<DateTime<Utc>, _>("updated_at"),
                    row.get::<Option<DateTime<Utc>>, _>("deleted_at"),
                )
            })
            .collect();

        Ok(admin_users)
    }

    async fn find_by_id(&self, id: &AdminUserId) -> Result<Option<AdminUser>, String> {
        let row = sqlx::query(
            "SELECT id, email, password_hash, name, is_active, last_login_at, created_at, updated_at, deleted_at FROM admin_users WHERE id = $1 AND deleted_at IS NULL"
        )
        .bind(id.0)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let admin_user = AdminUser::from_db(
                row.get::<Uuid, _>("id"),
                row.get::<String, _>("email"),
                row.get::<String, _>("password_hash"),
                row.get::<String, _>("name"),
                row.get::<bool, _>("is_active"),
                row.get::<Option<DateTime<Utc>>, _>("last_login_at"),
                row.get::<DateTime<Utc>, _>("created_at"),
                row.get::<DateTime<Utc>, _>("updated_at"),
                row.get::<Option<DateTime<Utc>>, _>("deleted_at"),
            );
            Ok(Some(admin_user))
        } else {
            Ok(None)
        }
    }

    async fn find_by_id_including_deleted(&self, id: &AdminUserId) -> Result<Option<AdminUser>, String> {
        let row = sqlx::query(
            "SELECT id, email, password_hash, name, is_active, last_login_at, created_at, updated_at, deleted_at FROM admin_users WHERE id = $1"
        )
        .bind(id.0)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let admin_user = AdminUser::from_db(
                row.get::<Uuid, _>("id"),
                row.get::<String, _>("email"),
                row.get::<String, _>("password_hash"),
                row.get::<String, _>("name"),
                row.get::<bool, _>("is_active"),
                row.get::<Option<DateTime<Utc>>, _>("last_login_at"),
                row.get::<DateTime<Utc>, _>("created_at"),
                row.get::<DateTime<Utc>, _>("updated_at"),
                row.get::<Option<DateTime<Utc>>, _>("deleted_at"),
            );
            Ok(Some(admin_user))
        } else {
            Ok(None)
        }
    }

    async fn find_by_email(&self, email: &str) -> Result<Option<AdminUser>, String> {
        let row = sqlx::query(
            "SELECT id, email, password_hash, name, is_active, last_login_at, created_at, updated_at, deleted_at FROM admin_users WHERE email = $1 AND deleted_at IS NULL"
        )
        .bind(email)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let admin_user = AdminUser::from_db(
                row.get::<Uuid, _>("id"),
                row.get::<String, _>("email"),
                row.get::<String, _>("password_hash"),
                row.get::<String, _>("name"),
                row.get::<bool, _>("is_active"),
                row.get::<Option<DateTime<Utc>>, _>("last_login_at"),
                row.get::<DateTime<Utc>, _>("created_at"),
                row.get::<DateTime<Utc>, _>("updated_at"),
                row.get::<Option<DateTime<Utc>>, _>("deleted_at"),
            );
            Ok(Some(admin_user))
        } else {
            Ok(None)
        }
    }

    async fn find_by_email_including_deleted(&self, email: &str) -> Result<Option<AdminUser>, String> {
        let row = sqlx::query(
            "SELECT id, email, password_hash, name, is_active, last_login_at, created_at, updated_at, deleted_at FROM admin_users WHERE email = $1"
        )
        .bind(email)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let admin_user = AdminUser::from_db(
                row.get::<Uuid, _>("id"),
                row.get::<String, _>("email"),
                row.get::<String, _>("password_hash"),
                row.get::<String, _>("name"),
                row.get::<bool, _>("is_active"),
                row.get::<Option<DateTime<Utc>>, _>("last_login_at"),
                row.get::<DateTime<Utc>, _>("created_at"),
                row.get::<DateTime<Utc>, _>("updated_at"),
                row.get::<Option<DateTime<Utc>>, _>("deleted_at"),
            );
            Ok(Some(admin_user))
        } else {
            Ok(None)
        }
    }

    async fn save(&self, admin_user: &AdminUser) -> Result<(), String> {
        sqlx::query(
            r#"
            INSERT INTO admin_users (id, email, password_hash, name, is_active, last_login_at, created_at, updated_at, deleted_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            ON CONFLICT (id) DO UPDATE SET
                email = EXCLUDED.email,
                password_hash = EXCLUDED.password_hash,
                name = EXCLUDED.name,
                is_active = EXCLUDED.is_active,
                last_login_at = EXCLUDED.last_login_at,
                updated_at = EXCLUDED.updated_at,
                deleted_at = EXCLUDED.deleted_at
            "#
        )
        .bind(admin_user.id.0)
        .bind(&admin_user.email)
        .bind(&admin_user.password_hash)
        .bind(&admin_user.name)
        .bind(admin_user.is_active)
        .bind(admin_user.last_login_at)
        .bind(admin_user.created_at)
        .bind(admin_user.updated_at)
        .bind(admin_user.deleted_at)
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    async fn soft_delete(&self, id: &AdminUserId) -> Result<(), String> {
        sqlx::query(
            "UPDATE admin_users SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND deleted_at IS NULL"
        )
        .bind(id.0)
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    async fn restore(&self, id: &AdminUserId) -> Result<(), String> {
        sqlx::query(
            "UPDATE admin_users SET deleted_at = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = $1"
        )
        .bind(id.0)
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    async fn email_exists(&self, email: &str) -> Result<bool, String> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM admin_users WHERE email = $1 AND deleted_at IS NULL"
        )
        .bind(email)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(count.0 > 0)
    }

    async fn count_active(&self) -> Result<i64, String> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM admin_users WHERE is_active = true AND deleted_at IS NULL"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(count.0)
    }
}
