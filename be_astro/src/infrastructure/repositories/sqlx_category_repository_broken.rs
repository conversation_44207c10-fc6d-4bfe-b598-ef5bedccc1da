use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;
use sqlx::PgPool;

pub struct SqlxCategoryRepository {
    pool: PgPool,
}

impl SqlxCategoryRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl CategoryRepository for SqlxCategoryRepository {
    async fn find_by_id(&self, id: &CategoryId) -> Result<Option<Category>, String> {
        let row = sqlx::query(
            r#"
            SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at, deleted_at
            FROM categories
            WHERE id = $1 AND is_active = true AND deleted_at IS NULL
            "#,
            id.0
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let category = Category {
                id: CategoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                parent_id: row.parent_id.map(CategoryId::from_uuid),
                image_url: row.image_url,
                is_active: row.is_active,
                sort_order: row.sort_order,
                created_at: row.created_at,
                updated_at: row.updated_at,
                deleted_at: row.deleted_at,
            };
            Ok(Some(category))
        } else {
            Ok(None)
        }
    }

    async fn find_by_id_including_deleted(&self, id: &CategoryId) -> Result<Option<Category>, String> {
        let row = sqlx::query(
            r#"
            SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at, deleted_at
            FROM categories
            WHERE id = $1
            "#,
            id.0
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let category = Category {
                id: CategoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                parent_id: row.parent_id.map(CategoryId::from_uuid),
                image_url: row.image_url,
                is_active: row.is_active,
                sort_order: row.sort_order,
                created_at: row.created_at,
                updated_at: row.updated_at,
                deleted_at: row.deleted_at,
            };
            Ok(Some(category))
        } else {
            Ok(None)
        }
    }

    async fn find_all(&self) -> Result<Vec<Category>, String> {
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at, deleted_at
            FROM categories
            WHERE is_active = true AND deleted_at IS NULL
            ORDER BY sort_order, name
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let categories = rows
            .into_iter()
            .map(|row| Category {
                id: CategoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                parent_id: row.parent_id.map(CategoryId::from_uuid),
                image_url: row.image_url,
                is_active: row.is_active,
                sort_order: row.sort_order,
                created_at: row.created_at,
                updated_at: row.updated_at,
                deleted_at: row.deleted_at,
            })
            .collect();

        Ok(categories)
    }

    async fn find_root_categories(&self) -> Result<Vec<Category>, String> {
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at, deleted_at
            FROM categories
            WHERE parent_id IS NULL AND is_active = true AND deleted_at IS NULL
            ORDER BY sort_order, name
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let categories = rows
            .into_iter()
            .map(|row| Category {
                id: CategoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                parent_id: row.parent_id.map(CategoryId::from_uuid),
                image_url: row.image_url,
                is_active: row.is_active,
                sort_order: row.sort_order,
                created_at: row.created_at,
                updated_at: row.updated_at,
                deleted_at: row.deleted_at,
            })
            .collect();

        Ok(categories)
    }

    async fn find_children(&self, _parent_id: &CategoryId) -> Result<Vec<Category>, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(vec![])
    }

    async fn find_by_parent(&self, _parent_id: Option<&CategoryId>) -> Result<Vec<Category>, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(vec![])
    }

    async fn count(&self) -> Result<u64, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(0)
    }

    async fn save(&self, category: &Category) -> Result<(), String> {
        // Check if category exists (for update vs insert)
        let existing = sqlx::query(
            "SELECT id FROM categories WHERE id = $1",
            category.id.0
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error checking category existence: {}", e))?;

        if existing.is_some() {
            // Update existing category
            sqlx::query(
                r#"
                UPDATE categories
                SET name = $2, description = $3, parent_id = $4, image_url = $5,
                    is_active = $6, sort_order = $7, updated_at = $8, deleted_at = $9
                WHERE id = $1
                "#,
                category.id.0,
                category.name,
                category.description,
                category.parent_id.as_ref().map(|id| id.0),
                category.image_url,
                category.is_active,
                category.sort_order,
                category.updated_at,
                category.deleted_at
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error updating category: {}", e))?;
        } else {
            // Insert new category
            sqlx::query(
                r#"
                INSERT INTO categories (id, name, description, parent_id, image_url,
                                      is_active, sort_order, created_at, updated_at, deleted_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                "#,
                category.id.0,
                category.name,
                category.description,
                category.parent_id.as_ref().map(|id| id.0),
                category.image_url,
                category.is_active,
                category.sort_order,
                category.created_at,
                category.updated_at,
                category.deleted_at
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error inserting category: {}", e))?;
        }

        Ok(())
    }

    async fn delete(&self, id: &CategoryId) -> Result<(), String> {
        sqlx::query(
            r#"
            UPDATE categories
            SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
            WHERE id = $1 AND deleted_at IS NULL
            "#,
            id.0
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error soft deleting category: {}", e))?;

        Ok(())
    }

    async fn soft_delete(&self, id: &CategoryId) -> Result<(), String> {
        sqlx::query(
            r#"
            UPDATE categories
            SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
            WHERE id = $1 AND deleted_at IS NULL
            "#,
            id.0
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error soft deleting category: {}", e))?;

        Ok(())
    }

    async fn restore(&self, id: &CategoryId) -> Result<(), String> {
        sqlx::query(
            r#"
            UPDATE categories
            SET deleted_at = NULL, updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
            "#,
            id.0
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error restoring category: {}", e))?;

        Ok(())
    }

    async fn get_category_path(&self, _id: &CategoryId) -> Result<Vec<Category>, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(vec![])
    }

    async fn has_children(&self, _id: &CategoryId) -> Result<bool, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(false)
    }

    async fn get_category_tree(&self) -> Result<Vec<CategoryWithChildren>, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(vec![])
    }
}
