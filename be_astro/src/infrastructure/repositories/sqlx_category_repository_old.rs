use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;
use sqlx::PgPool;

pub struct SqlxCategoryRepository {
    pool: PgPool,
}

impl SqlxCategoryRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl CategoryRepository for SqlxCategoryRepository {
    async fn find_by_id(&self, id: &CategoryId) -> Result<Option<Category>, String> {
        let row = sqlx::query(
            r#"
            SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at
            FROM categories 
            WHERE id = $1
            "#,
            id.0
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            Ok(Some(Category {
                id: CategoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                parent_id: row.parent_id.map(CategoryId::from_uuid),
                image_url: row.image_url,
                is_active: row.is_active,
                sort_order: row.sort_order,
                created_at: row.created_at,
                updated_at: row.updated_at,
            }))
        } else {
            Ok(None)
        }
    }

    async fn find_all(&self) -> Result<Vec<Category>, String> {
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at
            FROM categories 
            WHERE is_active = true
            ORDER BY sort_order, name
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let categories = rows
            .into_iter()
            .map(|row| Category {
                id: CategoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                parent_id: row.parent_id.map(CategoryId::from_uuid),
                image_url: row.image_url,
                is_active: row.is_active,
                sort_order: row.sort_order,
                created_at: row.created_at,
                updated_at: row.updated_at,
            })
            .collect();

        Ok(categories)
    }

    async fn find_root_categories(&self) -> Result<Vec<Category>, String> {
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at
            FROM categories 
            WHERE parent_id IS NULL AND is_active = true
            ORDER BY sort_order, name
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let categories = rows
            .into_iter()
            .map(|row| Category {
                id: CategoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                parent_id: row.parent_id.map(CategoryId::from_uuid),
                image_url: row.image_url,
                is_active: row.is_active,
                sort_order: row.sort_order,
                created_at: row.created_at,
                updated_at: row.updated_at,
            })
            .collect();

        Ok(categories)
    }

    async fn find_children(&self, parent_id: &CategoryId) -> Result<Vec<Category>, String> {
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at
            FROM categories 
            WHERE parent_id = $1 AND is_active = true
            ORDER BY sort_order, name
            "#,
            parent_id.0
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let categories = rows
            .into_iter()
            .map(|row| Category {
                id: CategoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                parent_id: row.parent_id.map(CategoryId::from_uuid),
                image_url: row.image_url,
                is_active: row.is_active,
                sort_order: row.sort_order,
                created_at: row.created_at,
                updated_at: row.updated_at,
            })
            .collect();

        Ok(categories)
    }

    async fn find_by_parent(&self, parent_id: Option<&CategoryId>) -> Result<Vec<Category>, String> {
        match parent_id {
            Some(id) => self.find_children(id).await,
            None => self.find_root_categories().await,
        }
    }

    async fn count(&self) -> Result<u64, String> {
        let row = sqlx::query(
            "SELECT COUNT(*) as count FROM categories WHERE is_active = true"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(row.count.unwrap_or(0) as u64)
    }

    async fn save(&self, category: &Category) -> Result<(), String> {
        sqlx::query(
            r#"
            INSERT INTO categories (id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                description = EXCLUDED.description,
                parent_id = EXCLUDED.parent_id,
                image_url = EXCLUDED.image_url,
                is_active = EXCLUDED.is_active,
                sort_order = EXCLUDED.sort_order,
                updated_at = EXCLUDED.updated_at
            "#,
            category.id.0,
            category.name,
            category.description,
            category.parent_id.as_ref().map(|id| id.0),
            category.image_url,
            category.is_active,
            category.sort_order,
            category.created_at,
            category.updated_at
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    async fn delete(&self, id: &CategoryId) -> Result<(), String> {
        sqlx::query("DELETE FROM categories WHERE id = $1", id.0)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    async fn get_category_path(&self, id: &CategoryId) -> Result<Vec<Category>, String> {
        // This would require a recursive query - simplified implementation
        let mut path = Vec::new();
        let mut current_id = Some(id.clone());

        while let Some(id) = current_id {
            if let Some(category) = self.find_by_id(&id).await? {
                current_id = category.parent_id.clone();
                path.insert(0, category);
            } else {
                break;
            }
        }

        Ok(path)
    }

    async fn has_children(&self, id: &CategoryId) -> Result<bool, String> {
        let row = sqlx::query(
            "SELECT COUNT(*) as count FROM categories WHERE parent_id = $1 AND is_active = true",
            id.0
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(row.count.unwrap_or(0) > 0)
    }

    async fn get_category_tree(&self) -> Result<Vec<CategoryWithChildren>, String> {
        // Simplified implementation - would need recursive query for full tree
        let root_categories = self.find_root_categories().await?;
        let mut tree = Vec::new();

        for category in root_categories {
            let children = self.find_children(&category.id).await?;
            let children_with_children = children
                .into_iter()
                .map(|child| CategoryWithChildren {
                    category: child,
                    children: vec![], // Simplified - only one level deep
                })
                .collect();

            tree.push(CategoryWithChildren {
                category,
                children: children_with_children,
            });
        }

        Ok(tree)
    }
}
