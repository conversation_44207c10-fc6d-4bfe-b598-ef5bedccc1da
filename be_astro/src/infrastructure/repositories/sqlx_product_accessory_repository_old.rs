use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;
use sqlx::PgPool;

pub struct SqlxProductAccessoryRepository {
    pool: PgPool,
}

impl SqlxProductAccessoryRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ProductAccessoryRepository for SqlxProductAccessoryRepository {
    async fn find_by_id(&self, id: &AccessoryId) -> Result<Option<ProductAccessory>, String> {
        let row = sqlx::query(
            r#"
            SELECT id, name, description, price, is_required, category, image_url, is_active, created_at, updated_at
            FROM product_accessories 
            WHERE id = $1
            "#,
            id.0
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            Ok(Some(ProductAccessory {
                id: AccessoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                price: Price::new(row.price),
                is_required: row.is_required,
                category: row.category,
                image_url: row.image_url,
                is_active: row.is_active,
                created_at: row.created_at,
                updated_at: row.updated_at,
            }))
        } else {
            Ok(None)
        }
    }

    async fn find_all(&self, page: u32, per_page: u32) -> Result<Vec<ProductAccessory>, String> {
        let offset = (page.saturating_sub(1)) * per_page;
        
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, price, is_required, category, image_url, is_active, created_at, updated_at
            FROM product_accessories 
            ORDER BY category, name
            LIMIT $1 OFFSET $2
            "#,
            per_page as i64,
            offset as i64
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let accessories = rows
            .into_iter()
            .map(|row| ProductAccessory {
                id: AccessoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                price: Price::new(row.price),
                is_required: row.is_required,
                category: row.category,
                image_url: row.image_url,
                is_active: row.is_active,
                created_at: row.created_at,
                updated_at: row.updated_at,
            })
            .collect();

        Ok(accessories)
    }

    async fn find_by_category(&self, category: &str) -> Result<Vec<ProductAccessory>, String> {
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, price, is_required, category, image_url, is_active, created_at, updated_at
            FROM product_accessories 
            WHERE category = $1 AND is_active = true
            ORDER BY name
            "#,
            category
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let accessories = rows
            .into_iter()
            .map(|row| ProductAccessory {
                id: AccessoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                price: Price::new(row.price),
                is_required: row.is_required,
                category: row.category,
                image_url: row.image_url,
                is_active: row.is_active,
                created_at: row.created_at,
                updated_at: row.updated_at,
            })
            .collect();

        Ok(accessories)
    }

    async fn find_active(&self) -> Result<Vec<ProductAccessory>, String> {
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, price, is_required, category, image_url, is_active, created_at, updated_at
            FROM product_accessories 
            WHERE is_active = true
            ORDER BY category, name
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let accessories = rows
            .into_iter()
            .map(|row| ProductAccessory {
                id: AccessoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                price: Price::new(row.price),
                is_required: row.is_required,
                category: row.category,
                image_url: row.image_url,
                is_active: row.is_active,
                created_at: row.created_at,
                updated_at: row.updated_at,
            })
            .collect();

        Ok(accessories)
    }

    async fn count(&self) -> Result<u64, String> {
        let row = sqlx::query(
            "SELECT COUNT(*) as count FROM product_accessories WHERE is_active = true"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(row.count.unwrap_or(0) as u64)
    }

    async fn save(&self, accessory: &ProductAccessory) -> Result<(), String> {
        sqlx::query(
            r#"
            INSERT INTO product_accessories (id, name, description, price, is_required, category, image_url, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                description = EXCLUDED.description,
                price = EXCLUDED.price,
                is_required = EXCLUDED.is_required,
                category = EXCLUDED.category,
                image_url = EXCLUDED.image_url,
                is_active = EXCLUDED.is_active,
                updated_at = EXCLUDED.updated_at
            "#,
            accessory.id.0,
            accessory.name,
            accessory.description,
            accessory.price.amount,
            accessory.is_required,
            accessory.category,
            accessory.image_url,
            accessory.is_active,
            accessory.created_at,
            accessory.updated_at
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    async fn delete(&self, id: &AccessoryId) -> Result<(), String> {
        sqlx::query("DELETE FROM product_accessories WHERE id = $1", id.0)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }
}
