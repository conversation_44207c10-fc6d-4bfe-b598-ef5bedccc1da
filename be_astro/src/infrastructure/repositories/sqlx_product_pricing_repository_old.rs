use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;
use sqlx::PgPool;
use rust_decimal::Decimal;

pub struct SqlxProductPricingRepository {
    pool: PgPool,
}

impl SqlxProductPricingRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ProductPricingRepository for SqlxProductPricingRepository {
    async fn calculate_price(
        &self,
        product_id: &ProductId,
        configuration: &ProductConfiguration,
    ) -> Result<PriceCalculation, String> {
        // Get the base product
        let product_row = sqlx::query(
            "SELECT base_price FROM products WHERE id = $1",
            product_id.0
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .ok_or("Product not found")?;

        let mut calculation = PriceCalculation::new(Price::new(product_row.base_price));

        // Apply dimension modifier if specified
        if let Some(dimension_name) = &configuration.dimension_name {
            if let Some((modifier_type, modifier_value)) = self
                .get_dimension_modifier(product_id, dimension_name)
                .await?
            {
                let adjustment = self.apply_price_modifier(
                    &Price::new(product_row.base_price),
                    &modifier_type,
                    modifier_value,
                )?;
                calculation.add_dimension_adjustment(dimension_name.clone(), adjustment)?;
            }
        }

        // Apply theme modifier if specified
        if let Some(theme_name) = &configuration.theme_name {
            if let Some((modifier_type, modifier_value)) = self
                .get_theme_modifier(product_id, theme_name)
                .await?
            {
                let adjustment = self.apply_price_modifier(
                    &Price::new(product_row.base_price),
                    &modifier_type,
                    modifier_value,
                )?;
                calculation.add_theme_adjustment(theme_name.clone(), adjustment)?;
            }
        }

        // Apply accessories
        if !configuration.accessories.is_empty() {
            let accessory_ids: Vec<uuid::Uuid> = configuration
                .accessories
                .iter()
                .map(|a| a.accessory_id.0)
                .collect();

            let accessories = self.get_accessories_by_ids(&configuration.accessories.iter().map(|a| a.accessory_id.clone()).collect::<Vec<_>>()).await?;

            for selection in &configuration.accessories {
                if let Some(accessory) = accessories
                    .iter()
                    .find(|a| a.id == selection.accessory_id)
                {
                    calculation.add_accessory(
                        accessory.name.clone(),
                        accessory.price.clone(),
                        selection.quantity,
                    )?;
                }
            }
        }

        Ok(calculation)
    }

    async fn get_dimension_modifier(
        &self,
        product_id: &ProductId,
        dimension_name: &str,
    ) -> Result<Option<(PriceModifierType, Decimal)>, String> {
        let row = sqlx::query(
            r#"
            SELECT price_modifier_type as "modifier_type: _", price_modifier_value
            FROM product_dimensions 
            WHERE product_id = $1 AND name = $2
            "#,
            product_id.0,
            dimension_name
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let modifier_type = match row.modifier_type.as_str() {
                "addition" => PriceModifierType::Addition,
                "subtraction" => PriceModifierType::Subtraction,
                "replacement" => PriceModifierType::Replacement,
                "percentage" => PriceModifierType::Percentage,
                _ => PriceModifierType::Addition,
            };
            Ok(Some((modifier_type, row.price_modifier_value)))
        } else {
            Ok(None)
        }
    }

    async fn get_theme_modifier(
        &self,
        product_id: &ProductId,
        theme_name: &str,
    ) -> Result<Option<(PriceModifierType, Decimal)>, String> {
        let row = sqlx::query(
            r#"
            SELECT price_modifier_type as "modifier_type: _", price_modifier_value
            FROM product_themes 
            WHERE product_id = $1 AND name = $2
            "#,
            product_id.0,
            theme_name
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let modifier_type = match row.modifier_type.as_str() {
                "addition" => PriceModifierType::Addition,
                "subtraction" => PriceModifierType::Subtraction,
                "replacement" => PriceModifierType::Replacement,
                "percentage" => PriceModifierType::Percentage,
                _ => PriceModifierType::Addition,
            };
            Ok(Some((modifier_type, row.price_modifier_value)))
        } else {
            Ok(None)
        }
    }

    async fn get_accessories_by_ids(
        &self,
        accessory_ids: &[AccessoryId],
    ) -> Result<Vec<ProductAccessory>, String> {
        if accessory_ids.is_empty() {
            return Ok(vec![]);
        }

        let ids: Vec<uuid::Uuid> = accessory_ids.iter().map(|id| id.0).collect();
        
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, price, is_required, category, image_url, is_active, created_at, updated_at
            FROM product_accessories 
            WHERE id = ANY($1) AND is_active = true
            "#,
            &ids
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let accessories = rows
            .into_iter()
            .map(|row| ProductAccessory {
                id: AccessoryId::from_uuid(row.id),
                name: row.name,
                description: row.description,
                price: Price::new(row.price),
                is_required: row.is_required,
                category: row.category,
                image_url: row.image_url,
                is_active: row.is_active,
                created_at: row.created_at,
                updated_at: row.updated_at,
            })
            .collect();

        Ok(accessories)
    }
}

impl SqlxProductPricingRepository {
    fn apply_price_modifier(
        &self,
        base_price: &Price,
        modifier_type: &PriceModifierType,
        modifier_value: Decimal,
    ) -> Result<Price, String> {
        match modifier_type {
            PriceModifierType::Addition => {
                Ok(Price::new(modifier_value))
            }
            PriceModifierType::Subtraction => {
                Ok(Price::new(-modifier_value))
            }
            PriceModifierType::Replacement => {
                Ok(Price::new(modifier_value - base_price.amount))
            }
            PriceModifierType::Percentage => {
                let adjustment = base_price.amount * (modifier_value / Decimal::from(100));
                Ok(Price::new(adjustment))
            }
        }
    }
}
