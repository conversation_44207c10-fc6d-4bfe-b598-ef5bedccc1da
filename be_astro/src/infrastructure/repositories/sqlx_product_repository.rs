use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;
use sqlx::{PgPool, Row};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;

pub struct SqlxProductRepository {
    pool: PgPool,
}

impl SqlxProductRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ProductRepository for SqlxProductRepository {
    async fn find_by_id(&self, id: &ProductId) -> Result<Option<Product>, String> {
        let row = sqlx::query(
            "SELECT id, name, slug, description, base_price, is_featured, is_active, image_url, specifications, dimensions, created_at, updated_at FROM products WHERE id = $1 AND is_active = true"
        )
        .bind(id.0)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let product = Product {
                id: ProductId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                slug: Some(row.get::<String, _>("slug")),
                description: Some(row.get::<String, _>("description")),
                base_price: Price::new(row.get::<Decimal, _>("base_price")),
                is_featured: row.get::<bool, _>("is_featured"),
                is_active: row.get::<bool, _>("is_active"),
                image_url: row.get::<Option<String>, _>("image_url"),
                specifications: row.get::<Option<serde_json::Value>, _>("specifications").map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.get::<Option<serde_json::Value>, _>("dimensions").map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
            };
            Ok(Some(product))
        } else {
            Ok(None)
        }
    }

    async fn find_by_slug(&self, slug: &str) -> Result<Option<Product>, String> {
        let row = sqlx::query(
            "SELECT id, name, slug, description, base_price, is_featured, is_active, image_url, specifications, dimensions, created_at, updated_at FROM products WHERE slug = $1"
        )
        .bind(slug)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let product = Product {
                id: ProductId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                slug: Some(row.get::<String, _>("slug")),
                description: Some(row.get::<String, _>("description")),
                base_price: Price::new(row.get::<Decimal, _>("base_price")),
                is_featured: row.get::<bool, _>("is_featured"),
                is_active: row.get::<bool, _>("is_active"),
                image_url: row.get::<Option<String>, _>("image_url"),
                specifications: row.get::<Option<serde_json::Value>, _>("specifications").map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.get::<Option<serde_json::Value>, _>("dimensions").map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
            };
            Ok(Some(product))
        } else {
            Ok(None)
        }
    }

    async fn find_all(&self, page: u32, per_page: u32) -> Result<Vec<Product>, String> {
        let offset = (page.saturating_sub(1)) * per_page;
        let rows = sqlx::query(
            "SELECT id, name, slug, description, base_price, is_featured, is_active, image_url, specifications, dimensions, created_at, updated_at FROM products WHERE is_active = true ORDER BY created_at DESC LIMIT $1 OFFSET $2"
        )
        .bind(per_page as i64)
        .bind(offset as i64)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut products = Vec::new();
        for row in rows {
            let product = Product {
                id: ProductId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                slug: Some(row.get::<String, _>("slug")),
                description: Some(row.get::<String, _>("description")),
                base_price: Price::new(row.get::<Decimal, _>("base_price")),
                is_featured: row.get::<bool, _>("is_featured"),
                is_active: row.get::<bool, _>("is_active"),
                image_url: row.get::<Option<String>, _>("image_url"),
                specifications: row.get::<Option<serde_json::Value>, _>("specifications").map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.get::<Option<serde_json::Value>, _>("dimensions").map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
            };
            products.push(product);
        }
        Ok(products)
    }

    async fn find_featured(&self, limit: Option<u32>) -> Result<Vec<Product>, String> {
        let limit = limit.unwrap_or(10) as i64;
        let rows = sqlx::query(
            "SELECT id, name, slug, description, base_price, is_featured, is_active, image_url, specifications, dimensions, created_at, updated_at FROM products WHERE is_active = true AND is_featured = true ORDER BY created_at DESC LIMIT $1"
        )
        .bind(limit)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut products = Vec::new();
        for row in rows {
            let product = Product {
                id: ProductId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                slug: Some(row.get::<String, _>("slug")),
                description: Some(row.get::<String, _>("description")),
                base_price: Price::new(row.get::<Decimal, _>("base_price")),
                is_featured: row.get::<bool, _>("is_featured"),
                is_active: row.get::<bool, _>("is_active"),
                image_url: row.get::<Option<String>, _>("image_url"),
                specifications: row.get::<Option<serde_json::Value>, _>("specifications").map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.get::<Option<serde_json::Value>, _>("dimensions").map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
            };
            products.push(product);
        }
        Ok(products)
    }

    async fn find_by_category(&self, _category_id: &CategoryId) -> Result<Vec<Product>, String> {
        Ok(vec![])
    }

    async fn find_by_accessory_categories(&self) -> Result<Vec<Product>, String> {
        let rows = sqlx::query(
            "SELECT id, name, slug, description, base_price, is_featured, is_active, image_url, specifications, dimensions, created_at, updated_at FROM products WHERE is_active = true AND (name ILIKE '%led%' OR name ILIKE '%tandem%' OR name ILIKE '%cabinet lighting%' OR name ILIKE '%hinge%' OR name ILIKE '%basket%' OR name ILIKE '%glass door%') ORDER BY name"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut products = Vec::new();
        for row in rows {
            let product = Product {
                id: ProductId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                slug: Some(row.get::<String, _>("slug")),
                description: Some(row.get::<String, _>("description")),
                base_price: Price::new(row.get::<Decimal, _>("base_price")),
                is_featured: row.get::<bool, _>("is_featured"),
                is_active: row.get::<bool, _>("is_active"),
                image_url: row.get::<Option<String>, _>("image_url"),
                specifications: row.get::<Option<serde_json::Value>, _>("specifications").map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.get::<Option<serde_json::Value>, _>("dimensions").map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
            };
            products.push(product);
        }
        Ok(products)
    }

    async fn search(&self, _query: &str, _page: u32, _per_page: u32) -> Result<Vec<Product>, String> {
        Ok(vec![])
    }

    async fn count(&self) -> Result<u64, String> {
        let count: (i64,) = sqlx::query_as("SELECT COUNT(*) FROM products WHERE is_active = true")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        Ok(count.0 as u64)
    }

    async fn save(&self, product: &Product) -> Result<(), String> {
        sqlx::query(
            r#"
            INSERT INTO products (id, name, slug, description, base_price, is_featured, is_active, image_url, specifications, dimensions, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                slug = EXCLUDED.slug,
                description = EXCLUDED.description,
                base_price = EXCLUDED.base_price,
                is_featured = EXCLUDED.is_featured,
                is_active = EXCLUDED.is_active,
                image_url = EXCLUDED.image_url,
                specifications = EXCLUDED.specifications,
                dimensions = EXCLUDED.dimensions,
                updated_at = EXCLUDED.updated_at
            "#
        )
        .bind(product.id.0)
        .bind(&product.name)
        .bind(&product.slug)
        .bind(&product.description)
        .bind(product.base_price.amount)
        .bind(product.is_featured)
        .bind(product.is_active)
        .bind(&product.image_url)
        .bind(product.specifications.as_ref().map(|s| serde_json::to_value(s).unwrap_or(serde_json::Value::Null)))
        .bind(product.dimensions.as_ref().map(|d| serde_json::to_value(d).unwrap_or(serde_json::Value::Null)))
        .bind(product.created_at)
        .bind(product.updated_at)
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    async fn delete(&self, id: &ProductId) -> Result<(), String> {
        sqlx::query("UPDATE products SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1")
            .bind(id.0)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        Ok(())
    }

    async fn find_dimensions(&self, product_id: &ProductId) -> Result<Vec<ProductDimension>, String> {
        let rows = sqlx::query(
            "SELECT id, product_id, name, price_modifier_type, price_modifier_value, is_default, sort_order FROM product_dimensions WHERE product_id = $1 ORDER BY sort_order, name"
        )
        .bind(product_id.0)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut dimensions = Vec::new();
        for row in rows {
            let modifier_type_str = row.get::<String, _>("price_modifier_type");
            let price_modifier_type = match modifier_type_str.as_str() {
                "addition" => PriceModifierType::Addition,
                "subtraction" => PriceModifierType::Subtraction,
                "replacement" => PriceModifierType::Replacement,
                "percentage" => PriceModifierType::Percentage,
                _ => PriceModifierType::Addition, // Default fallback
            };

            let dimension = ProductDimension {
                id: ProductId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                price_modifier_type,
                price_modifier_value: row.get::<Decimal, _>("price_modifier_value"),
                is_default: row.get::<bool, _>("is_default"),
                sort_order: row.get::<i32, _>("sort_order"),
            };
            dimensions.push(dimension);
        }
        Ok(dimensions)
    }

    async fn find_themes(&self, product_id: &ProductId) -> Result<Vec<ProductTheme>, String> {
        let rows = sqlx::query(
            "SELECT id, product_id, name, price_modifier_type, price_modifier_value, is_default, sort_order FROM product_themes WHERE product_id = $1 ORDER BY sort_order, name"
        )
        .bind(product_id.0)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut themes = Vec::new();
        for row in rows {
            let modifier_type_str = row.get::<String, _>("price_modifier_type");
            let price_modifier_type = match modifier_type_str.as_str() {
                "addition" => PriceModifierType::Addition,
                "subtraction" => PriceModifierType::Subtraction,
                "replacement" => PriceModifierType::Replacement,
                "percentage" => PriceModifierType::Percentage,
                _ => PriceModifierType::Addition, // Default fallback
            };

            let theme = ProductTheme {
                id: ProductId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                price_modifier_type,
                price_modifier_value: row.get::<Decimal, _>("price_modifier_value"),
                is_default: row.get::<bool, _>("is_default"),
                sort_order: row.get::<i32, _>("sort_order"),
            };
            themes.push(theme);
        }
        Ok(themes)
    }

    async fn save_dimension(&self, _dimension: &ProductDimension) -> Result<(), String> {
        Ok(())
    }

    async fn save_theme(&self, _theme: &ProductTheme) -> Result<(), String> {
        Ok(())
    }

    async fn add_to_category(&self, product_id: &ProductId, category_id: &CategoryId) -> Result<(), String> {
        sqlx::query("INSERT INTO product_categories (product_id, category_id, created_at) VALUES ($1, $2, $3) ON CONFLICT DO NOTHING")
            .bind(product_id.0)
            .bind(category_id.0)
            .bind(chrono::Utc::now())
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        Ok(())
    }

    async fn remove_from_category(&self, _product_id: &ProductId, _category_id: &CategoryId) -> Result<(), String> {
        Ok(())
    }

    async fn find_categories(&self, _product_id: &ProductId) -> Result<Vec<Category>, String> {
        Ok(vec![])
    }
}
