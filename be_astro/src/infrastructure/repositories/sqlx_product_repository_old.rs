use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;
use sqlx::{PgPool, Row};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;

pub struct SqlxProductRepository {
    pool: PgPool,
}

impl SqlxProductRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ProductRepository for SqlxProductRepository {
    async fn find_by_id(&self, id: &ProductId) -> Result<Option<Product>, String> {
        let row = sqlx::query(
            r#"
            SELECT id, name, slug, description, base_price, is_featured, is_active,
                   image_url, specifications, dimensions, created_at, updated_at
            FROM products
            WHERE id = $1 AND is_active = true AND deleted_at IS NULL
            "#
        )
        .bind(id.0)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let product = Product {
                id: ProductId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                slug: row.get::<String, _>("slug"),
                description: row.get::<String, _>("description"),
                base_price: Price::new(row.get::<Decimal, _>("base_price")),
                is_featured: row.get::<bool, _>("is_featured"),
                is_active: row.get::<bool, _>("is_active"),
                image_url: row.get::<Option<String>, _>("image_url"),
                specifications: row.get::<Option<serde_json::Value>, _>("specifications").map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.get::<Option<serde_json::Value>, _>("dimensions").map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
            };
            Ok(Some(product))
        } else {
            Ok(None)
        }
    }

    async fn find_by_slug(&self, slug: &str) -> Result<Option<Product>, String> {
        let row = sqlx::query(
            r#"
            SELECT id, name, slug, description, base_price, is_featured, is_active,
                   image_url, specifications, dimensions, created_at, updated_at
            FROM products
            WHERE slug = $1
            "#,
            slug
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let product = Product {
                id: ProductId::from_uuid(row.id),
                name: row.name,
                slug: row.slug,
                description: row.description,
                base_price: Price::new(row.base_price),
                is_featured: row.is_featured,
                is_active: row.is_active,
                image_url: row.image_url,
                specifications: row.specifications.map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.dimensions.map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.created_at,
                updated_at: row.updated_at,
            };
            Ok(Some(product))
        } else {
            Ok(None)
        }
    }

    async fn find_all(&self, page: u32, per_page: u32) -> Result<Vec<Product>, String> {
        let offset = (page.saturating_sub(1)) * per_page;

        let rows = sqlx::query(
            r#"
            SELECT p.id, p.name, p.slug, p.description, p.base_price, p.is_featured, p.is_active,
                   p.image_url, p.specifications, p.dimensions, p.created_at, p.updated_at
            FROM products p
            WHERE p.is_active = true AND p.deleted_at IS NULL
            ORDER BY p.created_at DESC
            LIMIT $1 OFFSET $2
            "#,
            per_page as i64,
            offset as i64
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut products = Vec::new();
        for row in rows {
            let product = Product {
                id: ProductId::from_uuid(row.id),
                name: row.name,
                slug: row.slug,
                description: row.description,
                base_price: Price::new(row.base_price),
                is_featured: row.is_featured,
                is_active: row.is_active,
                image_url: row.image_url,
                specifications: row.specifications.map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.dimensions.map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.created_at,
                updated_at: row.updated_at,
            };
            products.push(product);
        }

        Ok(products)
    }

    async fn find_featured(&self, limit: Option<u32>) -> Result<Vec<Product>, String> {
        let limit = limit.unwrap_or(10) as i64;

        let rows = sqlx::query(
            r#"
            SELECT id, name, slug, description, base_price, is_featured, is_active,
                   image_url, specifications, dimensions, created_at, updated_at
            FROM products
            WHERE is_active = true AND is_featured = true AND deleted_at IS NULL
            ORDER BY created_at DESC
            LIMIT $1
            "#,
            limit
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut products = Vec::new();
        for row in rows {
            let product = Product {
                id: ProductId::from_uuid(row.id),
                name: row.name,
                slug: row.slug,
                description: row.description,
                base_price: Price::new(row.base_price),
                is_featured: row.is_featured,
                is_active: row.is_active,
                image_url: row.image_url,
                specifications: row.specifications.map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.dimensions.map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.created_at,
                updated_at: row.updated_at,
            };
            products.push(product);
        }

        Ok(products)
    }

    async fn find_by_category(&self, _category_id: &CategoryId) -> Result<Vec<Product>, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(vec![])
    }

    async fn find_by_accessory_categories(&self) -> Result<Vec<Product>, String> {
        // First try to get products linked to accessory categories
        let rows = sqlx::query(
            r#"
            SELECT DISTINCT p.id, p.name, p.slug, p.description, p.base_price, p.is_featured, p.is_active,
                   p.image_url, p.specifications, p.dimensions, p.created_at, p.updated_at
            FROM products p
            LEFT JOIN product_categories pc ON p.id = pc.product_id
            LEFT JOIN categories c ON pc.category_id = c.id
            WHERE p.is_active = true AND p.deleted_at IS NULL
            AND (
                (c.is_active = true AND (
                    c.name = 'Accessories'
                    OR c.parent_id = (SELECT id FROM categories WHERE name = 'Accessories' AND is_active = true)
                ))
                OR (
                    -- Fallback: include products with accessory-like names if no category relationships exist
                    c.id IS NULL AND (
                        p.name ILIKE '%led%'
                        OR p.name ILIKE '%tandem%'
                        OR (p.name ILIKE '%box%' AND p.name NOT ILIKE '%tv%' AND p.name NOT ILIKE '%kitchen%')
                        OR p.name ILIKE '%cabinet lighting%'
                        OR p.name ILIKE '%hinge%'
                        OR p.name ILIKE '%basket%'
                        OR p.name ILIKE '%glass door%'
                        OR p.name ILIKE '%stone%'
                        OR p.name ILIKE '%pull%'
                        OR p.name ILIKE '%soft close%'
                    )
                    AND p.name NOT ILIKE '%tv cabinet%'
                    AND p.name NOT ILIKE '%kitchen%'
                    AND p.name NOT ILIKE '%pantry%'
                    AND p.name NOT ILIKE '%fantasy%'
                )
            )
            ORDER BY p.name
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut products = Vec::new();
        for row in rows {
            let product = Product {
                id: ProductId::from_uuid(row.id),
                name: row.name,
                slug: row.slug,
                description: row.description,
                base_price: Price::new(row.base_price),
                is_featured: row.is_featured,
                is_active: row.is_active,
                image_url: row.image_url,
                specifications: row.specifications.map(|v| serde_json::from_value(v).unwrap_or_default()),
                dimensions: row.dimensions.map(|v| serde_json::from_value(v).unwrap_or_default()),
                created_at: row.created_at,
                updated_at: row.updated_at,
            };
            products.push(product);
        }

        Ok(products)
    }

    async fn search(&self, _query: &str, _page: u32, _per_page: u32) -> Result<Vec<Product>, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(vec![])
    }

    async fn count(&self) -> Result<u64, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(0)
    }

    async fn save(&self, product: &Product) -> Result<(), String> {
        // Check if product exists (for update vs insert)
        let existing = sqlx::query(
            "SELECT id FROM products WHERE id = $1",
            product.id.0
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error checking product existence: {}", e))?;

        if existing.is_some() {
            // Update existing product
            sqlx::query(
                r#"
                UPDATE products
                SET name = $2, slug = $3, description = $4, base_price = $5,
                    is_featured = $6, is_active = $7, image_url = $8,
                    specifications = $9, dimensions = $10, updated_at = $11
                WHERE id = $1
                "#,
                product.id.0,
                product.name,
                product.slug,
                product.description,
                product.base_price.amount,
                product.is_featured,
                product.is_active,
                product.image_url,
                product.specifications.as_ref().map(|s| serde_json::to_value(s).unwrap_or(serde_json::Value::Null)),
                product.dimensions.as_ref().map(|d| serde_json::to_value(d).unwrap_or(serde_json::Value::Null)),
                product.updated_at
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error updating product: {}", e))?;
        } else {
            // Insert new product
            sqlx::query(
                r#"
                INSERT INTO products (id, name, slug, description, base_price, is_featured, is_active,
                                    image_url, specifications, dimensions, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                "#,
                product.id.0,
                product.name,
                product.slug,
                product.description,
                product.base_price.amount,
                product.is_featured,
                product.is_active,
                product.image_url,
                product.specifications.as_ref().map(|s| serde_json::to_value(s).unwrap_or(serde_json::Value::Null)),
                product.dimensions.as_ref().map(|d| serde_json::to_value(d).unwrap_or(serde_json::Value::Null)),
                product.created_at,
                product.updated_at
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error inserting product: {}", e))?;
        }

        Ok(())
    }

    async fn delete(&self, id: &ProductId) -> Result<(), String> {
        // Use soft delete by setting is_active to false and deleted_at timestamp
        sqlx::query(
            r#"
            UPDATE products
            SET is_active = false,
                deleted_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
            "#,
            id.0
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error deleting product: {}", e))?;

        Ok(())
    }

    async fn find_dimensions(&self, _product_id: &ProductId) -> Result<Vec<ProductDimension>, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(vec![])
    }

    async fn find_themes(&self, _product_id: &ProductId) -> Result<Vec<ProductTheme>, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(vec![])
    }

    async fn save_dimension(&self, _dimension: &ProductDimension) -> Result<(), String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(())
    }

    async fn save_theme(&self, _theme: &ProductTheme) -> Result<(), String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(())
    }

    async fn add_to_category(&self, product_id: &ProductId, category_id: &CategoryId) -> Result<(), String> {
        // Check if relationship already exists
        let existing = sqlx::query(
            "SELECT product_id FROM product_categories WHERE product_id = $1 AND category_id = $2",
            product_id.0,
            category_id.0
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error checking product category relationship: {}", e))?;

        if existing.is_none() {
            // Insert new relationship
            sqlx::query(
                "INSERT INTO product_categories (product_id, category_id, created_at) VALUES ($1, $2, $3)",
                product_id.0,
                category_id.0,
                chrono::Utc::now()
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error adding product to category: {}", e))?;
        }

        Ok(())
    }

    async fn remove_from_category(&self, _product_id: &ProductId, _category_id: &CategoryId) -> Result<(), String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(())
    }

    async fn find_categories(&self, _product_id: &ProductId) -> Result<Vec<Category>, String> {
        // TODO: Implement with actual database queries after migrations are run
        Ok(vec![])
    }
}
