use std::path::{Path, PathBuf};
use tokio::fs;
use uuid::Uuid;
use image::ImageFormat;
use bytes::Bytes;
use mime_guess::MimeGuess;

#[derive(Debug, <PERSON><PERSON>)]
pub struct FileStorage {
    upload_dir: PathBuf,
    base_url: String,
}

#[derive(Debug, thiserror::Error)]
pub enum FileStorageError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Image processing error: {0}")]
    Image(#[from] image::ImageError),
    #[error("Invalid file type: {0}")]
    InvalidFileType(String),
    #[error("File too large: {0} bytes (max: {1} bytes)")]
    FileTooLarge(usize, usize),
    #[error("Invalid file name")]
    InvalidFileName,
}

#[derive(Debug, <PERSON>lone)]
pub struct UploadedFile {
    pub filename: String,
    pub original_filename: String,
    pub file_path: <PERSON>Buf,
    pub url: String,
    pub mime_type: String,
    pub size: usize,
}

impl FileStorage {
    pub fn new(upload_dir: impl Into<PathBuf>, base_url: impl Into<String>) -> Self {
        Self {
            upload_dir: upload_dir.into(),
            base_url: base_url.into(),
        }
    }

    pub async fn init(&self) -> Result<(), FileStorageError> {
        fs::create_dir_all(&self.upload_dir).await?;
        fs::create_dir_all(self.upload_dir.join("products")).await?;
        fs::create_dir_all(self.upload_dir.join("categories")).await?;
        fs::create_dir_all(self.upload_dir.join("thumbnails")).await?;
        Ok(())
    }

    pub async fn save_image(
        &self,
        data: Bytes,
        original_filename: &str,
        subfolder: &str,
    ) -> Result<UploadedFile, FileStorageError> {
        // Validate file size (max 5MB)
        const MAX_SIZE: usize = 5 * 1024 * 1024;
        if data.len() > MAX_SIZE {
            return Err(FileStorageError::FileTooLarge(data.len(), MAX_SIZE));
        }

        // Detect MIME type
        let mime_type = MimeGuess::from_path(original_filename)
            .first_or_octet_stream()
            .to_string();

        // Validate image format
        let format = match mime_type.as_str() {
            "image/jpeg" => ImageFormat::Jpeg,
            "image/png" => ImageFormat::Png,
            "image/webp" => ImageFormat::WebP,
            _ => return Err(FileStorageError::InvalidFileType(mime_type)),
        };

        // Generate unique filename
        let extension = Path::new(original_filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("jpg");
        let filename = format!("{}.{}", Uuid::new_v4(), extension);

        // Create subfolder path
        let subfolder_path = self.upload_dir.join(subfolder);
        fs::create_dir_all(&subfolder_path).await?;

        let file_path = subfolder_path.join(&filename);

        // Process and save image
        let img = image::load_from_memory(&data)?;
        
        // Resize if too large (max 1920x1920)
        let img = if img.width() > 1920 || img.height() > 1920 {
            img.resize(1920, 1920, image::imageops::FilterType::Lanczos3)
        } else {
            img
        };

        // Save the processed image
        img.save_with_format(&file_path, format)?;

        // Generate thumbnail
        let thumbnail_path = self.upload_dir.join("thumbnails").join(&filename);
        let thumbnail = img.resize(300, 300, image::imageops::FilterType::Lanczos3);
        thumbnail.save_with_format(&thumbnail_path, format)?;

        // Generate URL
        let url = format!("{}/uploads/{}/{}", self.base_url, subfolder, filename);

        Ok(UploadedFile {
            filename: filename.clone(),
            original_filename: original_filename.to_string(),
            file_path,
            url,
            mime_type,
            size: data.len(),
        })
    }

    pub async fn delete_file(&self, filename: &str, subfolder: &str) -> Result<(), FileStorageError> {
        let file_path = self.upload_dir.join(subfolder).join(filename);
        let thumbnail_path = self.upload_dir.join("thumbnails").join(filename);

        // Delete main file
        if file_path.exists() {
            fs::remove_file(file_path).await?;
        }

        // Delete thumbnail
        if thumbnail_path.exists() {
            fs::remove_file(thumbnail_path).await?;
        }

        Ok(())
    }

    pub fn get_url(&self, filename: &str, subfolder: &str) -> String {
        format!("{}/uploads/{}/{}", self.base_url, subfolder, filename)
    }

    pub fn get_thumbnail_url(&self, filename: &str) -> String {
        format!("{}/uploads/thumbnails/{}", self.base_url, filename)
    }

    pub async fn file_exists(&self, filename: &str, subfolder: &str) -> bool {
        let file_path = self.upload_dir.join(subfolder).join(filename);
        file_path.exists()
    }
}

// Helper function to validate image file
pub fn is_valid_image_type(mime_type: &str) -> bool {
    matches!(mime_type, "image/jpeg" | "image/png" | "image/webp")
}

// Helper function to get file extension from MIME type
pub fn get_extension_from_mime(mime_type: &str) -> &'static str {
    match mime_type {
        "image/jpeg" => "jpg",
        "image/png" => "png",
        "image/webp" => "webp",
        _ => "jpg",
    }
}
