// Library exports for be_astro crate

pub mod config;
pub mod domain;
pub mod application;
pub mod infrastructure;
pub mod presentation;
pub mod utils;
pub mod handlers;
pub mod services;

// Re-export commonly used items
pub use config::Config;
pub use infrastructure::{create_connection_pool, run_migrations, ServiceContainer};

// Re-export the AppState for external use
#[derive(Clone)]
pub struct AppState {
    pub db: sqlx::PgPool,
    pub redis: redis::Client,
    pub config: Config,
    pub services: std::sync::Arc<infrastructure::ServiceContainer>,
}
