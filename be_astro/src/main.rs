use axum::{
    routing::{get, post, delete},
    middleware,
    Router,
    http::{Method, HeaderValue},
};
use tower::ServiceBuilder;
use tower_http::{
    services::ServeDir,
    cors::{Cors<PERSON>ayer, Any},
};
use tracing_subscriber;
use std::sync::Arc;
use sqlx::PgPool;

// Import everything from the library
use be_astro::{
    AppState, Config, create_connection_pool, run_migrations, ServiceContainer,
    domain::entities::AdminUser,
    presentation::{handlers, middleware::auth_middleware},
};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize environment variables
    dotenvy::dotenv().ok();

    // Initialize tracing with environment-based level
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .init();

    tracing::info!("Starting Astro Works backend server...");

    // Load configuration
    tracing::info!("Loading configuration...");
    let config = Config::from_env()?;
    tracing::info!("Configuration loaded successfully");

    // Initialize database
    let db_pool = create_connection_pool(&config.database_url).await?;

    // Run migrations
    run_migrations(&db_pool).await?;

    // Initialize default admin user from environment variables
    initialize_default_admin(&db_pool, &config).await?;

    // Create service container
    let services = Arc::new(ServiceContainer::new(db_pool.clone(), config.clone()));

    // Initialize Redis (with error handling)
    let redis_client = match redis::Client::open(config.redis_url.clone()) {
        Ok(client) => {
            tracing::info!("Redis client initialized successfully");
            client
        }
        Err(e) => {
            tracing::warn!("Failed to initialize Redis client: {}. Continuing without Redis.", e);
            // Create a dummy client using the same URL (will fail on connection attempts)
            redis::Client::open(config.redis_url.clone()).unwrap()
        }
    };

    // CORS is now handled by Nginx reverse proxy

    // Store config values before moving
    let host = config.host.clone();
    let port = config.port;
    let environment = config.environment.clone();

    // Configure CORS based on environment
    let cors = if environment == "development" {
        // Development: Allow all origins for local testing (no credentials for wildcard origin)
        tracing::info!("🔧 CORS: Development mode - allowing all origins");
        CorsLayer::new()
            .allow_origin(Any)
            .allow_methods([Method::GET, Method::POST, Method::PATCH, Method::DELETE, Method::OPTIONS])
            .allow_headers([
                axum::http::header::CONTENT_TYPE,
                axum::http::header::AUTHORIZATION,
                axum::http::header::ACCEPT,
                axum::http::header::ORIGIN,
                axum::http::header::ACCESS_CONTROL_REQUEST_METHOD,
                axum::http::header::ACCESS_CONTROL_REQUEST_HEADERS,
            ])
    } else {
        // Production: Restrict to specific origins
        tracing::info!("🔒 CORS: Production mode - restricting origins");
        CorsLayer::new()
            .allow_origin([
                "https://astrokabinet.id".parse::<HeaderValue>().unwrap(),
                "https://www.astrokabinet.id".parse::<HeaderValue>().unwrap(),
            ])
            .allow_methods([Method::GET, Method::POST, Method::PATCH, Method::DELETE, Method::OPTIONS])
            .allow_headers([
                axum::http::header::CONTENT_TYPE,
                axum::http::header::AUTHORIZATION,
                axum::http::header::ACCEPT,
            ])
            .allow_credentials(true)
    };

    // Build application state
    let app_state = AppState {
        db: db_pool,
        redis: redis_client,
        config,
        services,
    };

    // Build application routes - API only
    let app = Router::new()
        .route("/", get(handlers::health::health_check))
        .route("/health", get(handlers::health::health_check))
        .nest("/api/v1", api_routes())
        // Serve static files (uploaded images) with CORS
        .nest_service("/static", ServeDir::new("static").precompressed_gzip())
        .layer(ServiceBuilder::new()
            .layer(cors)
            .layer(middleware::from_fn_with_state(app_state.clone(), auth_middleware))
        )
        .with_state(app_state);

    // Start server
    let listener = tokio::net::TcpListener::bind(format!("{}:{}", host, port)).await?;

    tracing::info!("🚀 Server starting on {}:{}", host, port);
    tracing::info!("📊 Health check available at http://{}:{}/health", host, port);
    tracing::info!("🔗 API documentation at http://{}:{}/api/v1", host, port);

    axum::serve(listener, app).await?;

    Ok(())
}

fn api_routes() -> Router<AppState> {
    Router::new()
        .nest("/auth", handlers::auth::routes())
        .nest("/admin", handlers::admin::routes())
        .nest("/categories", handlers::categories::routes())
        .nest("/products", handlers::products::routes())
        .nest("/bundles", handlers::bundles::routes())
        .nest("/orders", handlers::orders::routes())
        // Config routes
        .route("/config", get(handlers::config::get_public_config))
        // Upload routes (legacy)
        .route("/uploads/:aspect/:product_slug", post(handlers::upload::upload_product_image))
        .route("/uploads/products/:product_slug", get(handlers::upload::get_product_images))
        .route("/uploads/delete/:filename", delete(handlers::upload::delete_product_image))
        // New optimized upload routes
        .route("/uploads/products", post(handlers::uploads::upload_product_images))
        .route("/uploads/cleanup", post(handlers::uploads::cleanup_legacy_images))
        .route("/uploads/stats", get(handlers::uploads::get_upload_stats))
}

// Admin UI routes removed - backend is now API-only
// All admin functionality moved to Svelte frontend at /manajemen

/// Initialize default admin user from environment variables
async fn initialize_default_admin(pool: &PgPool, config: &Config) -> anyhow::Result<()> {
    tracing::info!("Initializing default admin user...");

    let admin_email = &config.admin_default_email;
    let admin_password = &config.admin_default_password;

    // Check if admin user already exists
    let existing_admin = sqlx::query_as::<_, (uuid::Uuid, String)>(
        "SELECT id, password_hash FROM admin_users WHERE email = $1 AND deleted_at IS NULL"
    )
    .bind(admin_email)
    .fetch_optional(pool)
    .await?;

    match existing_admin {
        Some((_admin_id, password_hash)) => {
            // Admin exists, check if password needs updating
            if !AdminUser::verify_password_static(admin_password, &password_hash).unwrap_or(false) {
                tracing::info!("Updating admin password from environment variables...");

                // Hash the new password
                let new_password_hash = AdminUser::hash_password(admin_password)
                    .map_err(|e| anyhow::anyhow!("Failed to hash password: {}", e))?;

                // Update password in database
                sqlx::query(
                    "UPDATE admin_users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2"
                )
                .bind(new_password_hash)
                .bind(admin_email)
                .execute(pool)
                .await?;

                tracing::info!("Admin password updated successfully");
            } else {
                tracing::info!("Admin user exists with correct password");
            }
        }
        None => {
            // Admin doesn't exist, create new one
            tracing::info!("Creating new admin user from environment variables...");

            // Hash the password
            let password_hash = AdminUser::hash_password(admin_password)
                .map_err(|e| anyhow::anyhow!("Failed to hash password: {}", e))?;

            // Create admin user
            sqlx::query(
                "INSERT INTO admin_users (email, password_hash, name, is_active, created_at, updated_at) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)"
            )
            .bind(admin_email)
            .bind(password_hash)
            .bind("System Administrator")
            .bind(true)
            .execute(pool)
            .await?;

            tracing::info!("Admin user created successfully");
        }
    }

    tracing::info!("Default admin user initialization completed");
    Ok(())
}


