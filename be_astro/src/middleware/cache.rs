use axum::{
    extract::{Request, State},
    http::{HeaderMap, HeaderValue, StatusCode},
    middleware::Next,
    response::Response,
};
use redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::time::Duration;

use crate::AppState;

#[derive(Debug, Serialize, Deserialize)]
pub struct CacheEntry {
    pub data: String,
    pub content_type: String,
    pub expires_at: i64,
}

pub async fn cache_middleware(
    State(state): State<AppState>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let method = request.method().clone();
    let uri = request.uri().clone();
    
    // Only cache GET requests
    if method != axum::http::Method::GET {
        return Ok(next.run(request).await);
    }

    let cache_key = format!("cache:{}", uri.path_and_query().map(|pq| pq.as_str()).unwrap_or("/"));
    
    // Try to get from cache first
    if let Ok(mut redis_conn) = state.redis.get_async_connection().await {
        if let Ok(cached_data) = redis_conn.get::<_, String>(&cache_key).await {
            if let Ok(cache_entry) = serde_json::from_str::<CacheEntry>(&cached_data) {
                let now = chrono::Utc::now().timestamp();
                if cache_entry.expires_at > now {
                    let mut response = Response::new(cache_entry.data.into());
                    response.headers_mut().insert(
                        axum::http::header::CONTENT_TYPE,
                        HeaderValue::from_str(&cache_entry.content_type).unwrap_or_else(|_| {
                            HeaderValue::from_static("application/json")
                        }),
                    );
                    response.headers_mut().insert(
                        "X-Cache",
                        HeaderValue::from_static("HIT"),
                    );
                    return Ok(response);
                }
            }
        }
    }

    // Not in cache or expired, proceed with request
    let response = next.run(request).await;
    
    // Cache successful responses for certain endpoints
    if response.status().is_success() && should_cache_endpoint(uri.path()) {
        if let Ok(mut redis_conn) = state.redis.get_async_connection().await {
            let content_type = response
                .headers()
                .get(axum::http::header::CONTENT_TYPE)
                .and_then(|v| v.to_str().ok())
                .unwrap_or("application/json")
                .to_string();

            // For now, skip caching responses with bodies to avoid complexity
            // In a production environment, you'd want to implement proper body streaming
            let mut new_response = response;
            new_response.headers_mut().insert(
                "X-Cache",
                HeaderValue::from_static("MISS"),
            );
            return Ok(new_response);
        }
    }

    Ok(response)
}

fn should_cache_endpoint(path: &str) -> bool {
    // Cache product listings, categories, and static content
    path.starts_with("/api/v1/products") && !path.contains("/calculate-price") ||
    path.starts_with("/api/v1/categories") ||
    path.starts_with("/api/v1/bundles") ||
    path == "/health"
}

fn get_cache_duration(path: &str) -> i64 {
    match path {
        p if p.starts_with("/api/v1/products") => 300, // 5 minutes
        p if p.starts_with("/api/v1/categories") => 600, // 10 minutes
        p if p.starts_with("/api/v1/bundles") => 300, // 5 minutes
        "/health" => 60, // 1 minute
        _ => 300, // Default 5 minutes
    }
}
