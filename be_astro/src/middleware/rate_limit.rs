use axum::{
    extract::{Request, State},
    http::StatusCode,
    middleware::Next,
    response::Response,
};
use redis::AsyncCommands;
use std::net::SocketAddr;

use crate::AppState;

pub async fn rate_limit_middleware(
    State(state): State<AppState>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Extract client IP
    let client_ip = request
        .headers()
        .get("x-forwarded-for")
        .and_then(|v| v.to_str().ok())
        .map(|s| s.to_string())
        .or_else(|| {
            request
                .extensions()
                .get::<SocketAddr>()
                .map(|addr| addr.ip().to_string())
        })
        .unwrap_or_else(|| "unknown".to_string());

    let rate_limit_key = format!("rate_limit:{}", client_ip);
    
    if let Ok(mut redis_conn) = state.redis.get_async_connection().await {
        // Get current request count
        let current_count: i32 = redis_conn
            .get(&rate_limit_key)
            .await
            .unwrap_or(0);

        // Rate limit: 100 requests per minute
        if current_count >= 100 {
            return Err(StatusCode::TOO_MANY_REQUESTS);
        }

        // Increment counter
        let _: () = redis_conn.incr(&rate_limit_key, 1).await.unwrap_or(());
        
        // Set expiration if this is the first request
        if current_count == 0 {
            let _: () = redis_conn.expire(&rate_limit_key, 60).await.unwrap_or(());
        }
    }

    Ok(next.run(request).await)
}
