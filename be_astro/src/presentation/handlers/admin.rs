use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::J<PERSON>,
    routing::{get, post, patch},
    Router,
};
use uuid::Uuid;

use crate::{
    application::AdminUserResponse,
    AppState,
};

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/users", get(list_admin_users).post(create_admin_user))
        .route("/users/:id", get(get_admin_user).patch(update_admin_user))
}

pub async fn list_admin_users(
    State(_state): State<AppState>,
) -> Result<Json<Vec<AdminUserResponse>>, StatusCode> {
    // TODO: Implement admin user listing
    Ok(Json(vec![]))
}

pub async fn get_admin_user(
    State(_state): State<AppState>,
    Path(_id): Path<Uuid>,
) -> Result<Json<AdminUserResponse>, StatusCode> {
    // TODO: Implement admin user retrieval
    Err(StatusCode::NOT_FOUND)
}

pub async fn create_admin_user(
    State(_state): State<AppState>,
    Json(_payload): Json<serde_json::Value>,
) -> Result<(StatusCode, Json<serde_json::Value>), StatusCode> {
    // TODO: Implement admin user creation
    Ok((StatusCode::CREATED, Json(serde_json::json!({"message": "Admin user created"}))))
}

pub async fn update_admin_user(
    State(_state): State<AppState>,
    Path(_id): Path<Uuid>,
    Json(_payload): Json<serde_json::Value>,
) -> Result<(StatusCode, Json<serde_json::Value>), StatusCode> {
    // TODO: Implement admin user update
    Ok((StatusCode::OK, Json(serde_json::json!({"message": "Admin user updated"}))))
}
