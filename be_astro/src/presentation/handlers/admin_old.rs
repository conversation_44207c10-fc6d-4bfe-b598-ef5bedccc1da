use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::J<PERSON>,
    routing::{get, post, patch},
    Router,
};
use uuid::Uuid;

use crate::{
    application::AdminUserResponse,
    AppState,
};

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/users", get(list_admin_users).post(create_admin_user))
        .route("/users/:id", get(get_admin_user).patch(update_admin_user))
}

pub async fn list_admin_users(
    State(state): State<AppState>,
) -> Result<Json<Vec<AdminUserResponse>>, StatusCode> {
    let admin_service = AdminService::new(&state.db);
    
    match admin_service.list_users().await {
        Ok(users) => Ok(Json(users)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_admin_user(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<AdminUserResponse>, StatusCode> {
    let admin_service = AdminService::new(&state.db);
    
    match admin_service.get_user(id).await {
        Ok(Some(user)) => Ok(Json(user)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn create_admin_user(
    State(state): State<AppState>,
    Json(payload): Json<CreateAdminUserRequest>,
) -> Result<Json<AdminUserResponse>, StatusCode> {
    let admin_service = AdminService::new(&state.db);
    
    match admin_service.create_user(payload).await {
        Ok(user) => Ok(Json(user)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn update_admin_user(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateAdminUserRequest>,
) -> Result<Json<AdminUserResponse>, StatusCode> {
    let admin_service = AdminService::new(&state.db);
    
    match admin_service.update_user(id, payload).await {
        Ok(Some(user)) => Ok(Json(user)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}
