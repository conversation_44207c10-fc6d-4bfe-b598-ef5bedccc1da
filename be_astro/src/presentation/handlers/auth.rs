use axum::{
    extract::{ConnectInfo, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::net::SocketAddr;

use crate::application::services::auth_service::{LoginResponse, AdminUserInfo};
use crate::AppState;

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Deserialize)]
pub struct RefreshTokenRequest {
    pub token: String,
}

#[derive(Debug, Serialize)]
pub struct RefreshTokenResponse {
    pub token: String,
}

#[derive(Debug, Serialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
    pub timestamp: String,
}

impl ErrorResponse {
    fn new(error: &str, message: &str) -> Self {
        Self {
            error: error.to_string(),
            message: message.to_string(),
            timestamp: chrono::Utc::now().to_rfc3339(),
        }
    }
}

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/login", post(login))
        .route("/refresh", post(refresh_token))
        .route("/me", get(get_current_admin))
}

/// Admin login endpoint
pub async fn login(
    State(state): State<AppState>,
    connect_info: Option<ConnectInfo<SocketAddr>>,
    Json(request): Json<LoginRequest>,
) -> Result<Json<LoginResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate input
    if request.email.trim().is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new("validation_error", "Email is required")),
        ));
    }

    if request.password.trim().is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new("validation_error", "Password is required")),
        ));
    }

    // Extract IP address for rate limiting
    let ip_address = connect_info
        .map(|ConnectInfo(addr)| addr.ip().to_string())
        .unwrap_or_else(|| "unknown".to_string());

    // Attempt login
    match state.services.auth_service.login(
        request.email.trim().to_string(),
        request.password,
        ip_address,
    ).await {
        Ok(response) => Ok(Json(response)),
        Err(e) => {
            // Return generic error message for security
            if e.contains("Too many login attempts") {
                Err((
                    StatusCode::TOO_MANY_REQUESTS,
                    Json(ErrorResponse::new("rate_limited", &e)),
                ))
            } else {
                Err((
                    StatusCode::UNAUTHORIZED,
                    Json(ErrorResponse::new("unauthorized", "Invalid credentials")),
                ))
            }
        }
    }
}

/// Refresh JWT token endpoint
pub async fn refresh_token(
    State(state): State<AppState>,
    Json(request): Json<RefreshTokenRequest>,
) -> Result<Json<RefreshTokenResponse>, (StatusCode, Json<ErrorResponse>)> {
    if request.token.trim().is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new("validation_error", "Token is required")),
        ));
    }

    match state.services.auth_service.refresh_token(request.token).await {
        Ok(new_token) => Ok(Json(RefreshTokenResponse { token: new_token })),
        Err(_) => Err((
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse::new("unauthorized", "Invalid or expired token")),
        )),
    }
}

/// Get current authenticated admin user
pub async fn get_current_admin(
    State(_state): State<AppState>,
    request: axum::extract::Request,
) -> Result<Json<AdminUserInfo>, (StatusCode, Json<ErrorResponse>)> {
    // Extract admin user from request extensions (set by auth middleware)
    if let Some(admin_user) = request.extensions().get::<crate::domain::entities::AdminUser>() {
        let admin_info = AdminUserInfo {
            id: admin_user.id.0.to_string(),
            email: admin_user.email.clone(),
            name: admin_user.name.clone(),
            is_active: admin_user.is_active,
            last_login_at: admin_user.last_login_at,
        };

        Ok(Json(admin_info))
    } else {
        Err((
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse::new("unauthorized", "Authentication required")),
        ))
    }
}
