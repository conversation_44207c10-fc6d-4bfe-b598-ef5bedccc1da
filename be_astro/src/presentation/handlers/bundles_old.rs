use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, patch, delete},
    Router,
};
use serde::Deserialize;
use uuid::Uuid;

use crate::{
    models::product_bundle::{ProductBundleResponse, CreateProductBundleRequest, UpdateProductBundleRequest},
    services::bundle::BundleService,
    AppState,
};

#[derive(Debug, Deserialize)]
pub struct BundleQuery {
    pub main_product_id: Option<Uuid>,
    pub is_active: Option<bool>,
    pub include_items: Option<bool>,
    pub include_main_product: Option<bool>,
}

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/", get(list_bundles).post(create_bundle))
        .route("/:id", get(get_bundle).patch(update_bundle).delete(delete_bundle))
        .route("/:id/items", get(get_bundle_items))
        .route("/product/:product_id", get(get_bundles_for_product))
}

pub async fn list_bundles(
    State(state): State<AppState>,
    Query(query): Query<BundleQuery>,
) -> Result<Json<Vec<ProductBundleResponse>>, StatusCode> {
    let bundle_service = BundleService::new(&state.db);
    
    match bundle_service.list_bundles(query).await {
        Ok(bundles) => Ok(Json(bundles)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_bundle(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Query(query): Query<BundleQuery>,
) -> Result<Json<ProductBundleResponse>, StatusCode> {
    let bundle_service = BundleService::new(&state.db);
    
    match bundle_service.get_bundle(id, query).await {
        Ok(Some(bundle)) => Ok(Json(bundle)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn create_bundle(
    State(state): State<AppState>,
    Json(payload): Json<CreateProductBundleRequest>,
) -> Result<Json<ProductBundleResponse>, StatusCode> {
    let bundle_service = BundleService::new(&state.db);
    
    match bundle_service.create_bundle(payload).await {
        Ok(bundle) => Ok(Json(bundle)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn update_bundle(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateProductBundleRequest>,
) -> Result<Json<ProductBundleResponse>, StatusCode> {
    let bundle_service = BundleService::new(&state.db);
    
    match bundle_service.update_bundle(id, payload).await {
        Ok(Some(bundle)) => Ok(Json(bundle)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn delete_bundle(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<StatusCode, StatusCode> {
    let bundle_service = BundleService::new(&state.db);
    
    match bundle_service.delete_bundle(id).await {
        Ok(true) => Ok(StatusCode::NO_CONTENT),
        Ok(false) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_bundle_items(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<Vec<crate::models::bundle_item::BundleItemResponse>>, StatusCode> {
    let bundle_service = BundleService::new(&state.db);
    
    match bundle_service.get_bundle_items(id).await {
        Ok(items) => Ok(Json(items)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_bundles_for_product(
    State(state): State<AppState>,
    Path(product_id): Path<Uuid>,
    Query(query): Query<BundleQuery>,
) -> Result<Json<Vec<ProductBundleResponse>>, StatusCode> {
    let bundle_service = BundleService::new(&state.db);
    
    match bundle_service.get_bundles_for_product(product_id, query).await {
        Ok(bundles) => Ok(Json(bundles)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}
