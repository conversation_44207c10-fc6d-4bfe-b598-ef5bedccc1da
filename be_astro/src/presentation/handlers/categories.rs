use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, patch, delete},
    Router,
};
use uuid::Uuid;

use crate::{
    application::{CategoryResponse, CreateCategoryRequest},
    domain::value_objects::CategoryId,
    AppState,
};

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/", get(list_categories).post(create_category))
        .route("/:id", get(get_category).patch(update_category).delete(delete_category))
        .route("/:id/children", get(get_category_children))
}

pub async fn list_categories(
    State(state): State<AppState>,
) -> Result<Json<Vec<CategoryResponse>>, StatusCode> {
    match state.services.category_service.get_all_categories().await {
        Ok(categories) => {
            let responses: Vec<CategoryResponse> = categories.into_iter().map(|c| c.into()).collect();
            Ok(Json(responses))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_category(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<CategoryResponse>, StatusCode> {
    let category_id = CategoryId::from_uuid(id);

    match state.services.category_service.get_category(&category_id).await {
        Ok(Some(category)) => Ok(Json(category.into())),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn create_category(
    State(state): State<AppState>,
    Json(payload): Json<CreateCategoryRequest>,
) -> Result<Json<CategoryResponse>, StatusCode> {
    let parent_id = payload.parent_id.map(CategoryId::from_uuid);
    let is_active = payload.is_active.unwrap_or(true);

    match state.services.category_service.create_category_with_status(
        payload.name,
        payload.description,
        parent_id,
        is_active,
    ).await {
        Ok(category) => {
            Ok(Json(category.into()))
        },
        Err(e) => {
            eprintln!("Error creating category: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        },
    }
}

pub async fn update_category(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Json(payload): Json<CreateCategoryRequest>,
) -> Result<Json<CategoryResponse>, StatusCode> {
    let category_id = CategoryId::from_uuid(id);
    let parent_id = payload.parent_id.map(CategoryId::from_uuid);

    match state.services.category_service.update_category(
        &category_id,
        Some(payload.name),
        payload.description,
        parent_id,
        payload.is_active,
    ).await {
        Ok(_) => {
            // Fetch the updated category to return
            match state.services.category_service.get_category(&category_id).await {
                Ok(Some(category)) => Ok(Json(category.into())),
                Ok(None) => Err(StatusCode::NOT_FOUND),
                Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
            }
        },
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn delete_category(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<StatusCode, StatusCode> {
    eprintln!("DELETE request received for category ID: {}", id);
    let category_id = CategoryId::from_uuid(id);

    // First, get the category from the database (including deleted ones)
    match state.services.category_service.get_category_by_id_including_deleted(&category_id).await {
        Ok(Some(mut category)) => {
            eprintln!("Found category: {}, calling soft_delete()", category.name);

            // Call the entity's soft_delete method
            category.soft_delete();
            eprintln!("Category soft_delete() called, deleted_at: {:?}", category.deleted_at);

            // Save the updated category back to the database
            match state.services.category_service.save_category(&category).await {
                Ok(_) => {
                    eprintln!("Category {} soft deleted successfully", id);
                    Ok(StatusCode::NO_CONTENT)
                },
                Err(e) => {
                    eprintln!("Error saving soft deleted category: {}", e);
                    Err(StatusCode::INTERNAL_SERVER_ERROR)
                },
            }
        },
        Ok(None) => {
            eprintln!("Category {} not found", id);
            Err(StatusCode::NOT_FOUND)
        },
        Err(e) => {
            eprintln!("Error finding category: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        },
    }
}

pub async fn get_category_children(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<Vec<CategoryResponse>>, StatusCode> {
    let category_id = CategoryId::from_uuid(id);

    match state.services.category_service.get_category_children(&category_id).await {
        Ok(children) => {
            let responses: Vec<CategoryResponse> = children.into_iter().map(|c| c.into()).collect();
            Ok(Json(responses))
        },
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}
