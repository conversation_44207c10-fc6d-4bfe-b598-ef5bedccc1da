use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, patch, delete},
    Router,
};
use serde::Deserialize;
use uuid::Uuid;

use crate::{
    models::category::{CategoryResponse, CreateCategoryRequest, UpdateCategoryRequest},
    services::category::CategoryService,
    AppState,
};

#[derive(Debug, Deserialize)]
pub struct CategoryQuery {
    pub include_children: Option<bool>,
    pub include_parent: Option<bool>,
    pub is_accessory: Option<bool>,
    pub is_active: Option<bool>,
}

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/", get(list_categories).post(create_category))
        .route("/:id", get(get_category).patch(update_category).delete(delete_category))
        .route("/tree", get(get_category_tree))
        .route("/accessories", get(get_accessory_categories))
}

pub async fn list_categories(
    State(state): State<AppState>,
    Query(query): Query<CategoryQuery>,
) -> Result<Json<Vec<CategoryResponse>>, StatusCode> {
    let category_service = CategoryService::new(&state.db);
    
    match category_service.list_categories(query).await {
        Ok(categories) => Ok(Json(categories)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_category(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Query(query): Query<CategoryQuery>,
) -> Result<Json<CategoryResponse>, StatusCode> {
    let category_service = CategoryService::new(&state.db);
    
    match category_service.get_category(id, query).await {
        Ok(Some(category)) => Ok(Json(category)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn create_category(
    State(state): State<AppState>,
    Json(payload): Json<CreateCategoryRequest>,
) -> Result<Json<CategoryResponse>, StatusCode> {
    let category_service = CategoryService::new(&state.db);
    
    match category_service.create_category(payload).await {
        Ok(category) => Ok(Json(category)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn update_category(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateCategoryRequest>,
) -> Result<Json<CategoryResponse>, StatusCode> {
    let category_service = CategoryService::new(&state.db);
    
    match category_service.update_category(id, payload).await {
        Ok(Some(category)) => Ok(Json(category)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn delete_category(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<StatusCode, StatusCode> {
    let category_service = CategoryService::new(&state.db);
    
    match category_service.delete_category(id).await {
        Ok(true) => Ok(StatusCode::NO_CONTENT),
        Ok(false) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_category_tree(
    State(state): State<AppState>,
) -> Result<Json<Vec<CategoryResponse>>, StatusCode> {
    let category_service = CategoryService::new(&state.db);
    
    match category_service.get_category_tree().await {
        Ok(tree) => Ok(Json(tree)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_accessory_categories(
    State(state): State<AppState>,
) -> Result<Json<Vec<CategoryResponse>>, StatusCode> {
    let category_service = CategoryService::new(&state.db);
    
    match category_service.get_accessory_categories().await {
        Ok(categories) => Ok(Json(categories)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}
