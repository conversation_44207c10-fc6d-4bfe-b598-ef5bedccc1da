use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
};
use serde_json::json;

use crate::AppState;

/// Get public configuration for frontend
pub async fn get_public_config(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(json!({
        "whatsapp": {
            "phone_number": state.config.whatsapp_phone_number,
            "base_url": state.config.whatsapp_base_url
        },
        "bank": {
            "name": state.config.bank_name,
            "account_number": state.config.bank_account_number,
            "account_name": state.config.bank_account_name
        }
    })))
}
