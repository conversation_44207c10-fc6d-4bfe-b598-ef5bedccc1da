use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, patch},
    Router,
};
use serde::Deserialize;
use uuid::Uuid;

use crate::AppState;

#[derive(Deserialize)]
pub struct OrderQuery {
    pub status: Option<String>,
    pub page: Option<i64>,
    pub per_page: Option<i64>,
}

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/", get(list_orders).post(create_order))
        .route("/:id", get(get_order).patch(update_order))
        .route("/:id/items", get(get_order_items))
        .route("/:id/whatsapp", get(generate_whatsapp_link))
}

pub async fn list_orders(
    State(_state): State<AppState>,
    Query(_query): Query<OrderQuery>,
) -> Result<Json<Vec<serde_json::Value>>, StatusCode> {
    // TODO: Implement order listing
    Ok(Json(vec![]))
}

pub async fn get_order(
    State(_state): State<AppState>,
    Path(_id): Path<Uuid>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    // TODO: Implement order retrieval
    Err(StatusCode::NOT_FOUND)
}

pub async fn create_order(
    State(_state): State<AppState>,
    Json(_payload): Json<serde_json::Value>,
) -> Result<(StatusCode, Json<serde_json::Value>), StatusCode> {
    // TODO: Implement order creation
    Ok((StatusCode::CREATED, Json(serde_json::json!({"message": "Order created"}))))
}

pub async fn update_order(
    State(_state): State<AppState>,
    Path(_id): Path<Uuid>,
    Json(_payload): Json<serde_json::Value>,
) -> Result<(StatusCode, Json<serde_json::Value>), StatusCode> {
    // TODO: Implement order update
    Ok((StatusCode::OK, Json(serde_json::json!({"message": "Order updated"}))))
}

pub async fn get_order_items(
    State(_state): State<AppState>,
    Path(_id): Path<Uuid>,
) -> Result<Json<Vec<serde_json::Value>>, StatusCode> {
    // TODO: Implement order items retrieval
    Ok(Json(vec![]))
}

pub async fn generate_whatsapp_link(
    State(state): State<AppState>,
    Path(_id): Path<Uuid>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    // Generate WhatsApp link using config
    let whatsapp_url = format!(
        "{}{}?text=Hello%20from%20{}",
        state.config.whatsapp_base_url,
        state.config.whatsapp_phone_number,
        urlencoding::encode(&state.config.company_name)
    );

    Ok(Json(serde_json::json!({
        "whatsapp_url": whatsapp_url
    })))
}