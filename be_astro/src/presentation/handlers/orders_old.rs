use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, patch},
    Router,
};
use serde::Deserialize;
use uuid::Uuid;

use crate::{
    models::order::{OrderResponse, CreateOrderRequest, UpdateOrderRequest, OrderStatus},
    services::order::OrderService,
    AppState,
};

#[derive(Debug, Deserialize)]
pub struct OrderQuery {
    pub status: Option<OrderStatus>,
    pub customer_phone: Option<String>,
    pub include_items: Option<bool>,
    pub page: Option<u64>,
    pub per_page: Option<u64>,
    pub date_from: Option<String>,
    pub date_to: Option<String>,
}

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/", get(list_orders).post(create_order))
        .route("/:id", get(get_order).patch(update_order))
        .route("/:id/items", get(get_order_items))
        .route("/:id/whatsapp", get(generate_whatsapp_link))
        .route("/number/:order_number", get(get_order_by_number))
}

pub async fn list_orders(
    State(state): State<AppState>,
    Query(query): Query<OrderQuery>,
) -> Result<Json<Vec<OrderResponse>>, StatusCode> {
    let order_service = OrderService::new(&state.db, &state.config);
    
    match order_service.list_orders(query).await {
        Ok(orders) => Ok(Json(orders)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_order(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Query(query): Query<OrderQuery>,
) -> Result<Json<OrderResponse>, StatusCode> {
    let order_service = OrderService::new(&state.db, &state.config);
    
    match order_service.get_order(id, query).await {
        Ok(Some(order)) => Ok(Json(order)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_order_by_number(
    State(state): State<AppState>,
    Path(order_number): Path<String>,
    Query(query): Query<OrderQuery>,
) -> Result<Json<OrderResponse>, StatusCode> {
    let order_service = OrderService::new(&state.db, &state.config);
    
    match order_service.get_order_by_number(order_number, query).await {
        Ok(Some(order)) => Ok(Json(order)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn create_order(
    State(state): State<AppState>,
    Json(payload): Json<CreateOrderRequest>,
) -> Result<Json<OrderResponse>, StatusCode> {
    let order_service = OrderService::new(&state.db, &state.config);
    
    match order_service.create_order(payload).await {
        Ok(order) => Ok(Json(order)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn update_order(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateOrderRequest>,
) -> Result<Json<OrderResponse>, StatusCode> {
    let order_service = OrderService::new(&state.db, &state.config);
    
    match order_service.update_order(id, payload).await {
        Ok(Some(order)) => Ok(Json(order)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_order_items(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<Vec<crate::models::order_item::OrderItemResponse>>, StatusCode> {
    let order_service = OrderService::new(&state.db, &state.config);
    
    match order_service.get_order_items(id).await {
        Ok(items) => Ok(Json(items)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn generate_whatsapp_link(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let order_service = OrderService::new(&state.db, &state.config);
    
    match order_service.generate_whatsapp_link(id).await {
        Ok(Some(url)) => Ok(Json(serde_json::json!({ "whatsapp_url": url }))),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}
