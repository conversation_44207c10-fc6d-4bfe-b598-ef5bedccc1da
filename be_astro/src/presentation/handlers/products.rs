use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
    routing::{get, post, delete},
    Router,
};
use serde::Deserialize;
use uuid::Uuid;

use crate::{
    application::{
        ProductResponse, CreateProductRequest, UpdateProductRequest,
        ProductDimensionResponse, ProductThemeResponse,
        CalculatePriceRequest, PriceCalculationResponse,
    },
    domain::{
        value_objects::*,
        entities::ProductConfiguration,
    },
    AppState,
};

#[derive(Debug, Deserialize)]
pub struct ProductQuery {
    pub category_id: Option<Uuid>,
    pub is_featured: Option<bool>,
    pub is_active: Option<bool>,
    pub include_images: Option<bool>,
    pub include_categories: Option<bool>,
    pub search: Option<String>,
    pub page: Option<u64>,
    pub per_page: Option<u64>,
}

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/", get(list_products).post(create_product))
        .route("/slug/:slug", get(get_product_by_slug))
        .route("/slug/:slug/dimensions", get(get_product_dimensions_by_slug))
        .route("/slug/:slug/themes", get(get_product_themes_by_slug))
        .route("/:id", get(get_product).patch(update_product).delete(delete_product))
        .route("/:id/pricing", get(get_product_with_pricing))
        .route("/:id/calculate-price", post(calculate_dynamic_price))
        .route("/:id/dimensions", get(get_product_dimensions).post(create_product_dimension))
        .route("/:id/themes", get(get_product_themes).post(create_product_theme))
        .route("/accessories", get(get_available_accessories))
        .route("/featured", get(get_featured_products))
        .route("/search", get(search_products))
        .route("/:id/images", get(get_product_images).post(add_product_image))
        .route("/:id/images/:image_id", delete(remove_product_image))
}

pub async fn list_products(
    State(state): State<AppState>,
    Query(query): Query<ProductQuery>,
) -> Result<Json<Vec<ProductResponse>>, StatusCode> {
    let page = query.page.unwrap_or(1) as u32;
    let per_page = query.per_page.unwrap_or(20) as u32;

    match state.services.product_service.get_products(page, per_page).await {
        Ok(products) => {
            let responses: Vec<ProductResponse> = products.into_iter().map(|p| p.into()).collect();
            Ok(Json(responses))
        }
        Err(e) => {
            eprintln!("Error getting products: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        },
    }
}

pub async fn get_product(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Query(_query): Query<ProductQuery>,
) -> Result<Json<ProductResponse>, StatusCode> {
    let product_id = ProductId::from_uuid(id);

    match state.services.product_service.get_product(&product_id).await {
        Ok(Some(product)) => Ok(Json(product.into())),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_product_by_slug(
    State(state): State<AppState>,
    Path(slug): Path<String>,
) -> Result<Json<ProductResponse>, StatusCode> {
    match state.services.product_service.get_product_by_slug(&slug).await {
        Ok(Some(product)) => Ok(Json(product.into())),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}





pub async fn create_product(
    State(state): State<AppState>,
    Json(payload): Json<CreateProductRequest>,
) -> Result<Json<ProductResponse>, StatusCode> {
    match state.services.product_service.create_product(
        payload.name,
        payload.slug,
        payload.description,
        payload.base_price,
        payload.is_featured,
        payload.is_active,
    ).await {
        Ok(product) => {
            // If category_id is provided, add product to category
            if let Some(category_id) = payload.category_id {
                let category_id = crate::domain::value_objects::CategoryId::from_uuid(category_id);
                let _ = state.services.product_service.add_product_to_category(&product.id, &category_id).await;
            }
            Ok(Json(product.into()))
        },
        Err(e) => {
            eprintln!("Error creating product: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        },
    }
}

pub async fn update_product(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateProductRequest>,
) -> Result<Json<ProductResponse>, StatusCode> {
    let product_id = ProductId::from_uuid(id);

    match state.services.product_service.update_product(
        &product_id,
        payload.name,
        payload.description,
        payload.base_price,
        payload.is_featured,
        payload.is_active,
    ).await {
        Ok(()) => {
            // Get the updated product to return
            match state.services.product_service.get_product(&product_id).await {
                Ok(Some(product)) => Ok(Json(product.into())),
                Ok(None) => Err(StatusCode::NOT_FOUND),
                Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
            }
        },
        Err(e) => {
            eprintln!("Error updating product: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

pub async fn delete_product(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<StatusCode, StatusCode> {
    let product_id = ProductId::from_uuid(id);

    match state.services.product_service.delete_product(&product_id).await {
        Ok(()) => Ok(StatusCode::NO_CONTENT),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_featured_products(
    State(state): State<AppState>,
    Query(_query): Query<ProductQuery>,
) -> Result<Json<Vec<ProductResponse>>, StatusCode> {
    match state.services.product_service.get_featured_products(None).await {
        Ok(products) => {
            let responses: Vec<ProductResponse> = products.into_iter().map(|p| p.into()).collect();
            Ok(Json(responses))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn search_products(
    State(state): State<AppState>,
    Query(query): Query<ProductQuery>,
) -> Result<Json<Vec<ProductResponse>>, StatusCode> {
    let search_term = query.search.unwrap_or_default();
    let page = query.page.unwrap_or(1) as u32;
    let per_page = query.per_page.unwrap_or(20) as u32;

    match state.services.product_service.search_products(&search_term, page, per_page).await {
        Ok(products) => {
            let responses: Vec<ProductResponse> = products.into_iter().map(|p| p.into()).collect();
            Ok(Json(responses))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

// Placeholder implementations for image management
pub async fn get_product_images(
    State(_state): State<AppState>,
    Path(_id): Path<Uuid>,
) -> Result<Json<Vec<String>>, StatusCode> {
    // TODO: Implement product image listing
    Ok(Json(vec![]))
}

pub async fn add_product_image(
    State(_state): State<AppState>,
    Path(_id): Path<Uuid>,
) -> Result<StatusCode, StatusCode> {
    // TODO: Implement product image upload
    Ok(StatusCode::CREATED)
}

pub async fn remove_product_image(
    State(_state): State<AppState>,
    Path((_id, _image_id)): Path<(Uuid, Uuid)>,
) -> Result<StatusCode, StatusCode> {
    // TODO: Implement product image removal
    Ok(StatusCode::NO_CONTENT)
}

// New dynamic pricing endpoints
pub async fn get_product_with_pricing(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<ProductResponse>, StatusCode> {
    let product_id = ProductId::from_uuid(id);

    match state.services.product_service.get_product(&product_id).await {
        Ok(Some(product)) => Ok(Json(product.into())),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn calculate_dynamic_price(
    State(state): State<AppState>,
    Json(payload): Json<CalculatePriceRequest>,
) -> Result<Json<PriceCalculationResponse>, StatusCode> {
    let product_id = ProductId::from_uuid(payload.product_id);
    let configuration: ProductConfiguration = payload.into();

    match state.services.product_service.calculate_product_price(&product_id, &configuration).await {
        Ok(calculation) => Ok(Json(calculation.into())),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_product_dimensions(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<Vec<ProductDimensionResponse>>, StatusCode> {
    let product_id = ProductId::from_uuid(id);

    match state.services.product_service.get_product_dimensions(&product_id).await {
        Ok(dimensions) => {
            let responses: Vec<ProductDimensionResponse> = dimensions.into_iter().map(|d| d.into()).collect();
            Ok(Json(responses))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn create_product_dimension(
    State(_state): State<AppState>,
    Json(_payload): Json<serde_json::Value>,
) -> Result<StatusCode, StatusCode> {
    // TODO: Implement product dimension creation
    Ok(StatusCode::CREATED)
}

pub async fn get_product_themes(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<Vec<ProductThemeResponse>>, StatusCode> {
    let product_id = ProductId::from_uuid(id);

    match state.services.product_service.get_product_themes(&product_id).await {
        Ok(themes) => {
            let responses: Vec<ProductThemeResponse> = themes.into_iter().map(|t| t.into()).collect();
            Ok(Json(responses))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn create_product_theme(
    State(_state): State<AppState>,
    Json(_payload): Json<serde_json::Value>,
) -> Result<StatusCode, StatusCode> {
    // TODO: Implement product theme creation
    Ok(StatusCode::CREATED)
}

pub async fn get_product_dimensions_by_slug(
    State(state): State<AppState>,
    Path(slug): Path<String>,
) -> Result<Json<Vec<ProductDimensionResponse>>, StatusCode> {
    // First get the product by slug to get its ID
    match state.services.product_service.get_product_by_slug(&slug).await {
        Ok(Some(product)) => {
            match state.services.product_service.get_product_dimensions(&product.id).await {
                Ok(dimensions) => {
                    let responses: Vec<ProductDimensionResponse> = dimensions.into_iter().map(|d| d.into()).collect();
                    Ok(Json(responses))
                }
                Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
            }
        }
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_product_themes_by_slug(
    State(state): State<AppState>,
    Path(slug): Path<String>,
) -> Result<Json<Vec<ProductThemeResponse>>, StatusCode> {
    // First get the product by slug to get its ID
    match state.services.product_service.get_product_by_slug(&slug).await {
        Ok(Some(product)) => {
            match state.services.product_service.get_product_themes(&product.id).await {
                Ok(themes) => {
                    let responses: Vec<ProductThemeResponse> = themes.into_iter().map(|t| t.into()).collect();
                    Ok(Json(responses))
                }
                Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
            }
        }
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

pub async fn get_available_accessories(
    State(state): State<AppState>,
) -> Result<Json<Vec<ProductResponse>>, StatusCode> {
    match state.services.product_service.get_accessory_products().await {
        Ok(products) => {
            let responses: Vec<ProductResponse> = products.into_iter().map(|p| p.into()).collect();
            Ok(Json(responses))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}


