use axum::{
    extract::{Multipart, Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tokio::fs;
use uuid::Uuid;

use crate::AppState;

#[derive(Debug, Serialize)]
pub struct UploadResponse {
    pub success: bool,
    pub filename: String,
    pub url: String,
    pub message: String,
}

#[derive(Debug, Deserialize)]
pub struct UploadParams {
    pub aspect: String,
    pub product_slug: String,
}

/// Upload image with aspect-specific naming
/// POST /api/v1/uploads/:aspect/:product_slug
pub async fn upload_product_image(
    Path(params): Path<UploadParams>,
    State(state): State<AppState>,
    mut multipart: Multipart,
) -> Result<Json<UploadResponse>, StatusCode> {
    // Validate aspect ratio
    if !matches!(params.aspect.as_str(), "4x5" | "16x9") {
        return Ok(Json(UploadResponse {
            success: false,
            filename: String::new(),
            url: String::new(),
            message: "Invalid aspect ratio. Must be '4x5' or '16x9'".to_string(),
        }));
    }

    // Validate product slug
    if params.product_slug.is_empty() || params.product_slug.len() > 100 {
        return Ok(Json(UploadResponse {
            success: false,
            filename: String::new(),
            url: String::new(),
            message: "Invalid product slug".to_string(),
        }));
    }

    // Process the uploaded file
    while let Some(field) = multipart.next_field().await.map_err(|_| StatusCode::BAD_REQUEST)? {
        let name = field.name().unwrap_or("").to_string();
        
        if name != "image" {
            continue;
        }

        let filename = field.file_name().unwrap_or("").to_string();
        if filename.is_empty() {
            continue;
        }

        // Validate file type
        let content_type = field.content_type().unwrap_or("").to_string();
        if !content_type.starts_with("image/") {
            return Ok(Json(UploadResponse {
                success: false,
                filename: String::new(),
                url: String::new(),
                message: "File must be an image".to_string(),
            }));
        }

        // Get file extension
        let extension = PathBuf::from(&filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("jpg")
            .to_lowercase();

        // Validate extension
        if !matches!(extension.as_str(), "jpg" | "jpeg" | "png" | "webp") {
            return Ok(Json(UploadResponse {
                success: false,
                filename: String::new(),
                url: String::new(),
                message: "Invalid file type. Allowed: jpg, jpeg, png, webp".to_string(),
            }));
        }

        // Read file data
        let data = field.bytes().await.map_err(|_| StatusCode::BAD_REQUEST)?;
        
        // Validate file size (5MB max)
        if data.len() > 5 * 1024 * 1024 {
            return Ok(Json(UploadResponse {
                success: false,
                filename: String::new(),
                url: String::new(),
                message: "File size must be less than 5MB".to_string(),
            }));
        }

        // Generate filename with aspect-specific naming
        let new_filename = generate_image_filename(&params.aspect, &params.product_slug, &extension).await?;
        
        // Ensure upload directory exists
        let upload_dir = PathBuf::from("static/uploads/products");
        fs::create_dir_all(&upload_dir).await.map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

        // Save file
        let file_path = upload_dir.join(&new_filename);
        fs::write(&file_path, &data).await.map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

        // Generate URL
        let url = format!("{}/static/uploads/products/{}", state.config.api_base_url, new_filename);

        tracing::info!("Uploaded image: {} -> {}", filename, new_filename);

        return Ok(Json(UploadResponse {
            success: true,
            filename: new_filename,
            url,
            message: "Image uploaded successfully".to_string(),
        }));
    }

    Ok(Json(UploadResponse {
        success: false,
        filename: String::new(),
        url: String::new(),
        message: "No image file found in request".to_string(),
    }))
}

/// Generate filename with aspect-specific naming convention
/// Format: {aspect}_{product_slug}_{index}.{extension}
async fn generate_image_filename(aspect: &str, product_slug: &str, extension: &str) -> Result<String, StatusCode> {
    let upload_dir = PathBuf::from("static/uploads/products");
    
    // Find the next available index for this aspect and product
    let mut index = 1;
    loop {
        let uuid_str = Uuid::new_v4().to_string();
        let filename = format!("{}_{}_{}_{}.{}", aspect, product_slug, index, &uuid_str[..8], extension);
        let file_path = upload_dir.join(&filename);
        
        if !file_path.exists() {
            return Ok(filename);
        }
        
        index += 1;
        if index > 100 { // Safety limit
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    }
}

/// Get all images for a product
/// GET /api/v1/uploads/products/:product_slug
pub async fn get_product_images(
    Path(product_slug): Path<String>,
    State(state): State<AppState>,
) -> Result<Json<ProductImagesResponse>, StatusCode> {
    let upload_dir = PathBuf::from("static/uploads/products");
    
    let mut mobile_images = Vec::new();
    let mut desktop_images = Vec::new();

    // Read directory and filter images for this product
    if let Ok(mut entries) = fs::read_dir(&upload_dir).await {
        while let Some(entry) = entries.next_entry().await.map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)? {
            if let Some(filename) = entry.file_name().to_str() {
                if filename.contains(&product_slug) {
                    let url = format!("{}/static/uploads/products/{}", state.config.api_base_url, filename);
                    
                    if filename.starts_with("4x5_") {
                        mobile_images.push(ProductImage {
                            filename: filename.to_string(),
                            url,
                            aspect: "4x5".to_string(),
                        });
                    } else if filename.starts_with("16x9_") {
                        desktop_images.push(ProductImage {
                            filename: filename.to_string(),
                            url,
                            aspect: "16x9".to_string(),
                        });
                    }
                }
            }
        }
    }

    // Sort by filename to maintain order
    mobile_images.sort_by(|a, b| a.filename.cmp(&b.filename));
    desktop_images.sort_by(|a, b| a.filename.cmp(&b.filename));

    Ok(Json(ProductImagesResponse {
        product_slug,
        mobile_images,
        desktop_images,
    }))
}

/// Delete a product image
/// DELETE /api/v1/uploads/products/:filename
pub async fn delete_product_image(
    Path(filename): Path<String>,
) -> Result<Json<UploadResponse>, StatusCode> {
    let upload_dir = PathBuf::from("static/uploads/products");
    let file_path = upload_dir.join(&filename);

    if file_path.exists() {
        fs::remove_file(&file_path).await.map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        
        Ok(Json(UploadResponse {
            success: true,
            filename,
            url: String::new(),
            message: "Image deleted successfully".to_string(),
        }))
    } else {
        Ok(Json(UploadResponse {
            success: false,
            filename,
            url: String::new(),
            message: "Image not found".to_string(),
        }))
    }
}

#[derive(Debug, Serialize)]
pub struct ProductImage {
    pub filename: String,
    pub url: String,
    pub aspect: String,
}

#[derive(Debug, Serialize)]
pub struct ProductImagesResponse {
    pub product_slug: String,
    pub mobile_images: Vec<ProductImage>,
    pub desktop_images: Vec<ProductImage>,
}
