use axum::{
    extract::{Multipart, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, warn, error};

use crate::{
    AppState,
    services::image_processor::ImageProcessor,
};

/// Request for uploading product images
#[derive(Debug, Deserialize)]
pub struct UploadProductImagesRequest {
    pub product_slug: String,
    pub start_index: Option<u32>,
}

/// Response for image upload operation
#[derive(Debug, Serialize)]
pub struct UploadResponse {
    pub success: bool,
    pub message: String,
    pub results: Vec<ImageUploadResult>,
    pub total_processed: u32,
    pub total_failed: u32,
}

/// Individual image upload result
#[derive(Debug, Serialize)]
pub struct ImageUploadResult {
    pub success: bool,
    pub filename: Option<String>,
    pub original_size: Option<u64>,
    pub optimized_size: Option<u64>,
    pub compression_ratio: Option<f32>,
    pub original_format: Option<String>,
    pub optimized_format: Option<String>,
    pub space_saved_bytes: Option<u64>,
    pub space_saved_kb: Option<f64>,
    pub aspect_ratios: Vec<AspectRatioResult>,
    pub error: Option<String>,
}

/// Result for each aspect ratio generated
#[derive(Debug, Serialize)]
pub struct AspectRatioResult {
    pub aspect_ratio: String,
    pub filename: String,
    pub dimensions: (u32, u32),
    pub file_size: u64,
    pub file_size_kb: f64,
    pub compression_ratio: f64,
    pub file_path: String,
}

/// Request for cleanup operation
#[derive(Debug, Deserialize)]
pub struct CleanupRequest {
    pub dry_run: bool,
}

/// Response for cleanup operation
#[derive(Debug, Serialize)]
pub struct CleanupResponse {
    pub success: bool,
    pub message: String,
    pub files_removed: Vec<String>,
    pub total_removed: u32,
    pub dry_run: bool,
}

/// Upload multiple product images
pub async fn upload_product_images(
    State(_state): State<AppState>,
    mut multipart: Multipart,
) -> Result<Json<UploadResponse>, StatusCode> {
    info!("Starting product image upload");

    let mut product_slug = String::new();
    let mut start_index = 1u32;
    let mut image_files = Vec::new();

    // Parse multipart form data
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        error!("Failed to read image data: {}", e);
        StatusCode::BAD_REQUEST
    })? {
        let name = field.name().unwrap_or("").to_string();
        info!("Processing multipart field: {}", name);

        match name.as_str() {
            "product_slug" => {
                product_slug = field.text().await.map_err(|e| {
                    error!("Failed to read product_slug: {}", e);
                    StatusCode::BAD_REQUEST
                })?;
                info!("Product slug: {}", product_slug);
            }
            "start_index" => {
                let index_str = field.text().await.map_err(|e| {
                    error!("Failed to read start_index: {}", e);
                    StatusCode::BAD_REQUEST
                })?;
                start_index = index_str.parse().unwrap_or(1);
                info!("Start index: {}", start_index);
            }
            "images" | "image" | "files" | "file" => {
                let filename = field.file_name().unwrap_or("unknown").to_string();
                let content_type = field.content_type().unwrap_or("").to_string();

                info!("Processing file: {} (type: {})", filename, content_type);

                // Validate content type
                if !content_type.starts_with("image/") && !content_type.is_empty() {
                    warn!("Invalid content type for file {}: {}", filename, content_type);
                    continue;
                }

                let data = field.bytes().await.map_err(|e| {
                    error!("Failed to read image data for {}: {}", filename, e);
                    StatusCode::BAD_REQUEST
                })?;

                // Validate file size (10MB max)
                if data.len() > 10 * 1024 * 1024 {
                    warn!("File {} too large: {} bytes", filename, data.len());
                    continue;
                }

                // Basic image validation
                if data.len() < 100 {
                    warn!("File {} too small: {} bytes", filename, data.len());
                    continue;
                }

                info!("Added file: {} ({} bytes)", filename, data.len());
                image_files.push((filename, data.to_vec()));
            }
            _ => {
                info!("Ignoring unknown field: {}", name);
                // Skip unknown fields instead of erroring
                let _ = field.bytes().await;
            }
        }
    }

    // Validate required fields
    if product_slug.is_empty() {
        return Err(StatusCode::BAD_REQUEST);
    }

    if image_files.is_empty() {
        return Err(StatusCode::BAD_REQUEST);
    }

    info!("Processing {} images for product: {}", image_files.len(), product_slug);

    // Initialize image processor
    let upload_dir = std::path::PathBuf::from("static/uploads");
    let processor = ImageProcessor::new(upload_dir);

    let mut results = Vec::new();
    let mut total_processed = 0u32;
    let mut total_failed = 0u32;

    // Process each image
    for (index, (filename, data)) in image_files.into_iter().enumerate() {
        let image_index = start_index + index as u32;
        
        info!("Processing image {}: {} (index: {})", index + 1, filename, image_index);

        match process_single_image(&processor, &data, &product_slug, image_index, &filename).await {
            Ok(result) => {
                total_processed += 1;
                results.push(result);
            }
            Err(e) => {
                total_failed += 1;
                error!("Failed to process image {}: {}", filename, e);

                // Create more user-friendly error messages
                let user_error = create_user_friendly_error(&e, &filename);

                results.push(ImageUploadResult {
                    success: false,
                    filename: Some(filename),
                    original_size: Some(data.len() as u64),
                    optimized_size: None,
                    compression_ratio: None,
                    original_format: None,
                    optimized_format: None,
                    space_saved_bytes: None,
                    space_saved_kb: None,
                    aspect_ratios: Vec::new(),
                    error: Some(user_error),
                });
            }
        }
    }

    let response = UploadResponse {
        success: total_failed == 0,
        message: if total_failed == 0 {
            format!("Successfully processed {} images", total_processed)
        } else {
            format!("Processed {} images, {} failed", total_processed, total_failed)
        },
        results,
        total_processed,
        total_failed,
    };

    info!("Upload completed: {} processed, {} failed", total_processed, total_failed);
    Ok(Json(response))
}

/// Process a single image file
async fn process_single_image(
    processor: &ImageProcessor,
    data: &[u8],
    product_slug: &str,
    index: u32,
    filename: &str,
) -> Result<ImageUploadResult, anyhow::Error> {
    // Validate image format
    let original_format = ImageProcessor::validate_image_format(data)?;

    // Process the image
    let processing_results = processor.process_product_image(data, product_slug, index).await?;

    let original_size = data.len() as u64;
    let total_optimized_size: u64 = processing_results.iter().map(|r| r.optimized_size).sum();
    let compression_ratio = if original_size > 0 {
        Some((original_size - total_optimized_size) as f32 / original_size as f32 * 100.0)
    } else {
        None
    };

    // Calculate space saved
    let space_saved_bytes = if original_size > total_optimized_size {
        Some(original_size - total_optimized_size)
    } else {
        None
    };
    let space_saved_kb = space_saved_bytes.map(|bytes| bytes as f64 / 1024.0);

    let aspect_ratios = processing_results
        .into_iter()
        .map(|result| {
            let filename = result.file_path
                .file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("unknown")
                .to_string();

            AspectRatioResult {
                aspect_ratio: result.aspect_ratio,
                filename: filename.clone(),
                dimensions: result.dimensions,
                file_size: result.optimized_size,
                file_size_kb: result.optimized_size as f64 / 1024.0,
                compression_ratio: result.compression_ratio,
                file_path: result.file_path.to_string_lossy().to_string(),
            }
        })
        .collect();

    Ok(ImageUploadResult {
        success: true,
        filename: Some(filename.to_string()),
        original_size: Some(original_size),
        optimized_size: Some(total_optimized_size),
        compression_ratio,
        original_format: Some(format!("{:?}", original_format)),
        optimized_format: Some("WebP".to_string()),
        space_saved_bytes,
        space_saved_kb,
        aspect_ratios,
        error: None,
    })
}

/// Clean up legacy non-WebP images
pub async fn cleanup_legacy_images(
    State(_state): State<AppState>,
    Json(request): Json<CleanupRequest>,
) -> Result<Json<CleanupResponse>, StatusCode> {
    info!("Starting legacy image cleanup (dry_run: {})", request.dry_run);

    let upload_dir = std::path::PathBuf::from("static/uploads");
    let processor = ImageProcessor::new(upload_dir);

    match processor.cleanup_legacy_images(request.dry_run).await {
        Ok(removed_files) => {
            let files_removed: Vec<String> = removed_files
                .iter()
                .map(|p| p.to_string_lossy().to_string())
                .collect();

            let total_removed = files_removed.len() as u32;

            let response = CleanupResponse {
                success: true,
                message: if request.dry_run {
                    format!("Dry run completed. {} files would be removed", total_removed)
                } else {
                    format!("Cleanup completed. {} files removed", total_removed)
                },
                files_removed,
                total_removed,
                dry_run: request.dry_run,
            };

            info!("Cleanup completed successfully");
            Ok(Json(response))
        }
        Err(e) => {
            error!("Cleanup failed: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Get upload status and statistics
pub async fn get_upload_stats(
    State(_state): State<AppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let upload_dir = std::path::PathBuf::from("static/uploads/products");
    
    let stats = match calculate_directory_stats(&upload_dir).await {
        Ok(stats) => stats,
        Err(e) => {
            error!("Failed to calculate upload stats: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    Ok(Json(serde_json::json!({
        "success": true,
        "stats": stats
    })))
}

/// Calculate statistics for the upload directory
async fn calculate_directory_stats(dir: &std::path::Path) -> Result<serde_json::Value, anyhow::Error> {
    use tokio::fs;

    if !dir.exists() {
        return Ok(serde_json::json!({
            "total_files": 0,
            "total_size": 0,
            "webp_files": 0,
            "legacy_files": 0,
            "file_types": {}
        }));
    }

    let mut total_files = 0u32;
    let mut total_size = 0u64;
    let mut webp_files = 0u32;
    let mut legacy_files = 0u32;
    let mut file_types: HashMap<String, u32> = HashMap::new();

    let mut entries = fs::read_dir(dir).await?;
    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        
        if path.is_file() {
            total_files += 1;
            
            if let Ok(metadata) = entry.metadata().await {
                total_size += metadata.len();
            }

            if let Some(extension) = path.extension() {
                let ext = extension.to_string_lossy().to_lowercase();
                *file_types.entry(ext.clone()).or_insert(0) += 1;

                match ext.as_str() {
                    "webp" => webp_files += 1,
                    "jpg" | "jpeg" | "png" | "gif" | "bmp" => legacy_files += 1,
                    _ => {}
                }
            }
        }
    }

    Ok(serde_json::json!({
        "total_files": total_files,
        "total_size": total_size,
        "webp_files": webp_files,
        "legacy_files": legacy_files,
        "file_types": file_types
    }))
}

/// Create user-friendly error messages from technical errors
fn create_user_friendly_error(error: &anyhow::Error, filename: &str) -> String {
    let error_str = error.to_string().to_lowercase();

    if error_str.contains("failed to process 16x9 aspect ratio") || error_str.contains("failed to process 4x5 aspect ratio") {
        format!("Image '{}' could not be processed for the required aspect ratios. This may be due to the image being too small or having unusual dimensions. Please ensure the image is at least 400x400 pixels.", filename)
    } else if error_str.contains("image dimensions too small") {
        format!("Image '{}' is too small. Minimum required size is 400x400 pixels.", filename)
    } else if error_str.contains("image file too large") {
        format!("Image '{}' is too large. Maximum file size is 10MB.", filename)
    } else if error_str.contains("unsupported image format") || error_str.contains("unable to determine image format") {
        format!("Image '{}' has an unsupported format. Please use JPG, PNG, GIF, BMP, or WebP.", filename)
    } else if error_str.contains("failed to decode image") || error_str.contains("failed to load") {
        format!("Image '{}' appears to be corrupted or invalid. Please try a different image file.", filename)
    } else if error_str.contains("invalid crop parameters") || error_str.contains("invalid image dimensions") {
        format!("Image '{}' has invalid dimensions that prevent proper processing. Please use a different image.", filename)
    } else if error_str.contains("failed to optimize") {
        format!("Image '{}' could not be optimized. The file may be corrupted or in an unusual format.", filename)
    } else if error_str.contains("failed to save") || error_str.contains("failed to write") {
        format!("Image '{}' was processed but could not be saved. Please try again.", filename)
    } else {
        // Fallback for unknown errors
        format!("Image '{}' could not be processed due to an unexpected error. Please try a different image or contact support if the problem persists.", filename)
    }
}
