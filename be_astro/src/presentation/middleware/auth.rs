use axum::{
    extract::{Request, State},
    http::{header::AUTHORIZATION, StatusCode},
    middleware::Next,
    response::{Json, Response},
};
use serde::Serialize;

use crate::domain::entities::AdminUser;
use crate::AppState;

#[derive(Debug, Serialize)]
pub struct AuthErrorResponse {
    pub error: String,
    pub message: String,
    pub timestamp: String,
}

impl AuthErrorResponse {
    fn new(error: &str, message: &str) -> Self {
        Self {
            error: error.to_string(),
            message: message.to_string(),
            timestamp: chrono::Utc::now().to_rfc3339(),
        }
    }
}

/// Authentication middleware for protecting admin routes
pub async fn auth_middleware(
    State(state): State<AppState>,
    mut request: Request,
    next: Next,
) -> Result<Response, (StatusCode, Json<AuthErrorResponse>)> {
    // Get the request path and method
    let path = request.uri().path().to_string();
    let method = request.method().clone();

    // Always allow OPTIONS requests (CORS preflight)
    if method == axum::http::Method::OPTIONS {
        return Ok(next.run(request).await);
    }

    // Check if this is a protected route (admin management routes)
    if !is_protected_route(&path) {
        // Allow public routes to pass through without authentication
        return Ok(next.run(request).await);
    }

    // Extract Authorization header
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    let token = match auth_header {
        Some(header) if header.starts_with("Bearer ") => {
            header.strip_prefix("Bearer ").unwrap_or("")
        }
        _ => {
            return Err((
                StatusCode::UNAUTHORIZED,
                Json(AuthErrorResponse::new(
                    "unauthorized",
                    "Missing or invalid Authorization header",
                )),
            ));
        }
    };

    // Verify token and get admin user
    let admin_user = match state.services.auth_service.get_admin_from_token(token).await {
        Ok(user) => user,
        Err(e) => {
            eprintln!("[AUTH] Token verification failed for path {}: {}", path, e);
            return Err((
                StatusCode::UNAUTHORIZED,
                Json(AuthErrorResponse::new("unauthorized", "Invalid or expired token")),
            ));
        }
    };

    // Check if admin user can still login (active and not deleted)
    if !admin_user.can_login() {
        eprintln!("[AUTH] Access denied for inactive/deleted admin: {}", admin_user.email);
        return Err((
            StatusCode::UNAUTHORIZED,
            Json(AuthErrorResponse::new("unauthorized", "Account is inactive")),
        ));
    }

    // Log successful authentication (before moving admin_user)
    eprintln!("[AUTH] Authenticated admin access to {}: {}", path, admin_user.email);

    // Add admin user to request extensions for use in handlers
    request.extensions_mut().insert(admin_user);

    Ok(next.run(request).await)
}

/// Check if a route requires authentication
fn is_protected_route(path: &str) -> bool {
    // Protect admin API routes (except auth login/refresh endpoints)
    if path.starts_with("/api/v1/admin/") {
        return true;
    }

    // Protect the /me endpoint for getting current admin user
    if path == "/api/v1/auth/me" {
        return true;
    }

    // Allow all other routes (public API, auth login/refresh, frontend routes)
    // Frontend authentication will be handled by Svelte
    false
}

/// Extract authenticated admin user from request extensions
pub fn extract_admin_user(request: &Request) -> Option<&AdminUser> {
    request.extensions().get::<AdminUser>()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_protected_route() {
        // Protected routes
        assert!(is_protected_route("/api/v1/admin/users"));
        assert!(is_protected_route("/api/v1/admin/categories"));
        assert!(is_protected_route("/api/v1/auth/me"));

        // Public routes
        assert!(!is_protected_route("/"));
        assert!(!is_protected_route("/api/v1/categories"));
        assert!(!is_protected_route("/api/v1/products"));
        assert!(!is_protected_route("/api/v1/auth/login"));
        assert!(!is_protected_route("/api/v1/auth/refresh"));
        assert!(!is_protected_route("/static/images/test.jpg"));
        assert!(!is_protected_route("/manajemen")); // Frontend routes are public
        assert!(!is_protected_route("/akses-manajerial")); // Login page is public
    }

    #[test]
    fn test_auth_error_response() {
        let error = AuthErrorResponse::new("test_error", "Test message");
        assert_eq!(error.error, "test_error");
        assert_eq!(error.message, "Test message");
        assert!(!error.timestamp.is_empty());
    }
}
