use axum::{
    extract::{Request, State},
    http::{StatusC<PERSON>, Uri},
    middleware::Next,
    response::{Redirect, Response},
};
use tower_sessions::Session;

use crate::AppState;

/// Session-based authentication middleware for admin UI routes
pub async fn session_auth_middleware(
    State(_state): State<AppState>,
    session: Session,
    request: Request,
    next: Next,
) -> Result<Response, Response> {
    let uri = request.uri().clone();
    let path = uri.path();

    // Check if this is a protected admin route
    if !is_protected_admin_route(path) {
        // Allow non-protected routes to pass through
        return Ok(next.run(request).await);
    }

    // Check if user is authenticated via session
    match session.get::<String>("admin_id").await {
        Ok(Some(_admin_id)) => {
            // User is authenticated, proceed with request
            Ok(next.run(request).await)
        }
        Ok(None) => {
            // User is not authenticated, redirect to login
            Ok(Redirect::to("/akses-manajerial").into_response())
        }
        Err(_) => {
            // Session error, redirect to login
            Ok(Redirect::to("/akses-manajerial").into_response())
        }
    }
}

/// Check if a route requires session authentication
fn is_protected_admin_route(path: &str) -> bool {
    // Protect all /manajemen/* routes except logout (which handles its own auth)
    if path.starts_with("/manajemen") && path != "/manajemen/logout" {
        return true;
    }
    
    // Allow login page and other routes
    false
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_protected_admin_route() {
        // Protected routes
        assert!(is_protected_admin_route("/manajemen"));
        assert!(is_protected_admin_route("/manajemen/"));
        assert!(is_protected_admin_route("/manajemen/kategori"));
        assert!(is_protected_admin_route("/manajemen/produk"));
        assert!(is_protected_admin_route("/manajemen/pesanan"));
        
        // Non-protected routes
        assert!(!is_protected_admin_route("/akses-manajerial"));
        assert!(!is_protected_admin_route("/manajemen/logout"));
        assert!(!is_protected_admin_route("/api/v1/auth/login"));
        assert!(!is_protected_admin_route("/api/v1/categories"));
        assert!(!is_protected_admin_route("/"));
        assert!(!is_protected_admin_route("/static/images/test.jpg"));
    }
}
