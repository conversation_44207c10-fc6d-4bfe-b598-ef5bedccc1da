use image::{ImageFormat, DynamicImage, ImageError};
use std::io::Cursor;
use std::path::Path;
use anyhow::{Context, Result};
use tracing::{info, warn};

/// Advanced image optimizer with metadata removal and quality control
pub struct ImageOptimizer {
    pub quality: f32, // For WebP, 0-100
}

impl ImageOptimizer {
    pub fn new(quality: f32) -> Self {
        Self {
            quality: quality.clamp(0.0, 100.0),
        }
    }

    /// Optimize image based on input format with comprehensive metadata removal
    pub fn optimize_image(&self, input_data: &[u8], input_format: ImageFormat) -> Result<Vec<u8>> {
        match input_format {
            ImageFormat::Png | ImageFormat::Jpeg => {
                // For PNG/JPG: remove metadata and convert to WebP
                self.convert_to_webp(input_data)
            }
            ImageFormat::WebP => {
                // For WebP: optimize again if beneficial
                self.optimize_webp(input_data)
            }
            _ => anyhow::bail!("Unsupported format: {:?}", input_format),
        }
    }

    /// Convert PNG/JPG to WebP with metadata removal
    fn convert_to_webp(&self, input_data: &[u8]) -> Result<Vec<u8>> {
        // Load image without metadata (image crate automatically strips metadata)
        let img = image::load_from_memory(input_data)
            .context("Failed to load image for WebP conversion")?;
        
        // Convert to WebP
        let webp_data = self.encode_webp(&img)?;
        
        Ok(webp_data)
    }

    /// Optimize existing WebP images
    fn optimize_webp(&self, input_data: &[u8]) -> Result<Vec<u8>> {
        // Decode WebP
        let img = image::load_from_memory(input_data)
            .context("Failed to decode WebP image")?;
        
        // Encode again with target quality
        let optimized_data = self.encode_webp(&img)?;
        
        // Only return optimized version if it's smaller
        if optimized_data.len() < input_data.len() {
            info!("WebP re-optimization successful: {} -> {} bytes", 
                  input_data.len(), optimized_data.len());
            Ok(optimized_data)
        } else {
            info!("WebP re-optimization not beneficial, keeping original");
            Ok(input_data.to_vec())
        }
    }

    /// Encode image to WebP format with quality control
    fn encode_webp(&self, img: &DynamicImage) -> Result<Vec<u8>> {
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);
        
        // Encode to WebP with specified quality
        // Note: The image crate's WebP encoder automatically uses the quality setting
        img.write_to(&mut cursor, ImageFormat::WebP)
            .context("Failed to encode image as WebP")?;
        
        Ok(buffer)
    }

    /// Detect image format from raw data
    pub fn detect_format(data: &[u8]) -> Result<ImageFormat, ImageError> {
        image::guess_format(data)
    }

    /// Process file directly from filesystem
    pub fn process_file<P: AsRef<Path>>(&self, input_path: P, output_path: P) -> Result<()> {
        let input_data = std::fs::read(&input_path)
            .context("Failed to read input file")?;
        let format = Self::detect_format(&input_data)
            .context("Failed to detect image format")?;
        
        let optimized_data = self.optimize_image(&input_data, format)?;
        
        std::fs::write(output_path, optimized_data)
            .context("Failed to write optimized file")?;
        Ok(())
    }

    /// Optimize image with detailed statistics
    pub fn optimize_with_info(&self, input_data: &[u8]) -> Result<OptimizationResult> {
        let original_size = input_data.len();
        let format = Self::detect_format(input_data)
            .context("Failed to detect image format")?;
        
        let optimized_data = self.optimize_image(input_data, format)?;
        let optimized_size = optimized_data.len();
        
        let compression_ratio = if original_size > 0 {
            ((original_size - optimized_size) as f64 / original_size as f64) * 100.0
        } else {
            0.0
        };

        Ok(OptimizationResult {
            data: optimized_data,
            original_size,
            optimized_size,
            compression_ratio,
            original_format: format,
            output_format: ImageFormat::WebP,
        })
    }

    /// Optimize with target file size (iterative quality adjustment)
    pub fn optimize_to_target_size(&self, input_data: &[u8], target_size_kb: u32) -> Result<OptimizationResult> {
        let target_size_bytes = (target_size_kb as usize) * 1024;
        let format = Self::detect_format(input_data)
            .context("Failed to detect image format")?;
        
        // Load image once
        let img = image::load_from_memory(input_data)
            .context("Failed to load image")?;
        
        // Try different quality levels to hit target size
        let mut best_quality = self.quality;
        let mut best_data = self.encode_webp(&img)?;
        
        // If initial encoding is already under target, return it
        if best_data.len() <= target_size_bytes {
            return Ok(OptimizationResult {
                data: best_data.clone(),
                original_size: input_data.len(),
                optimized_size: best_data.len(),
                compression_ratio: ((input_data.len() - best_data.len()) as f64 / input_data.len() as f64) * 100.0,
                original_format: format,
                output_format: ImageFormat::WebP,
            });
        }
        
        // Binary search for optimal quality
        let mut low_quality = 10.0;
        let mut high_quality = self.quality;
        
        for _ in 0..8 { // Max 8 iterations
            let test_quality = (low_quality + high_quality) / 2.0;
            let test_optimizer = ImageOptimizer::new(test_quality);
            let test_data = test_optimizer.encode_webp(&img)?;
            
            if test_data.len() <= target_size_bytes {
                // This quality works, try higher
                best_quality = test_quality;
                best_data = test_data;
                low_quality = test_quality;
            } else {
                // Too large, try lower quality
                high_quality = test_quality;
            }
            
            // If we're close enough, break
            if (high_quality - low_quality) < 1.0 {
                break;
            }
        }
        
        info!("Optimized to target size {}KB with quality {:.1}", 
              target_size_kb, best_quality);
        
        Ok(OptimizationResult {
            data: best_data.clone(),
            original_size: input_data.len(),
            optimized_size: best_data.len(),
            compression_ratio: ((input_data.len() - best_data.len()) as f64 / input_data.len() as f64) * 100.0,
            original_format: format,
            output_format: ImageFormat::WebP,
        })
    }
}

/// Detailed result of image optimization
#[derive(Debug)]
pub struct OptimizationResult {
    pub data: Vec<u8>,
    pub original_size: usize,
    pub optimized_size: usize,
    pub compression_ratio: f64,
    pub original_format: ImageFormat,
    pub output_format: ImageFormat,
}

impl OptimizationResult {
    pub fn print_stats(&self) {
        info!("Image Optimization Results:");
        info!("  Original format: {:?}", self.original_format);
        info!("  Output format: {:?}", self.output_format);
        info!("  Original size: {} bytes ({:.2} KB)", self.original_size, self.original_size as f64 / 1024.0);
        info!("  Optimized size: {} bytes ({:.2} KB)", self.optimized_size, self.optimized_size as f64 / 1024.0);
        info!("  Compression ratio: {:.2}%", self.compression_ratio);
        
        if self.compression_ratio > 0.0 {
            let saved_bytes = self.original_size - self.optimized_size;
            info!("  Space saved: {} bytes ({:.2} KB)", saved_bytes, saved_bytes as f64 / 1024.0);
        } else {
            info!("  No size reduction achieved");
        }
    }
    
    pub fn is_under_target_kb(&self, target_kb: u32) -> bool {
        self.optimized_size <= (target_kb as usize * 1024)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_quality_clamping() {
        let optimizer_high = ImageOptimizer::new(150.0);
        let optimizer_low = ImageOptimizer::new(-10.0);
        let optimizer_normal = ImageOptimizer::new(85.0);
        
        assert_eq!(optimizer_high.quality, 100.0);
        assert_eq!(optimizer_low.quality, 0.0);
        assert_eq!(optimizer_normal.quality, 85.0);
    }

    #[test]
    fn test_format_detection() {
        // Test with minimal valid image headers
        let jpeg_header = [0xFF, 0xD8, 0xFF]; // JPEG magic bytes
        let png_header = [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]; // PNG magic bytes
        
        // Note: These are just headers, not complete images
        // In real usage, complete image data would be provided
        assert!(ImageOptimizer::detect_format(&jpeg_header).is_err()); // Incomplete image
        assert!(ImageOptimizer::detect_format(&png_header).is_err()); // Incomplete image
    }
}
