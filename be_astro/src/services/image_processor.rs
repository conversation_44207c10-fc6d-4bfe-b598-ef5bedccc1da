use anyhow::{Context, Result};
use image::{ImageFormat, DynamicImage, GenericImageView};
use std::io::Cursor;
use std::path::PathBuf;
use tokio::fs;
use tracing::{info, warn};

use crate::services::image_optimizer::ImageOptimizer;

/// Configuration for image processing
#[derive(Debug, Clone)]
pub struct ImageProcessingConfig {
    pub webp_quality: f32,
    pub max_dimension: u32,
    pub target_file_size: u64, // in bytes
}

impl Default for ImageProcessingConfig {
    fn default() -> Self {
        Self {
            webp_quality: 85.0,
            max_dimension: 2000,
            target_file_size: 500 * 1024, // 500KB
        }
    }
}

/// Represents the two aspect ratios we need to generate
#[derive(Debug, Clone)]
pub enum AspectRatio {
    Mobile,  // 4:5
    Desktop, // 16:9
}

impl AspectRatio {
    pub fn as_str(&self) -> &'static str {
        match self {
            AspectRatio::Mobile => "4x5",
            AspectRatio::Desktop => "16x9",
        }
    }

    pub fn ratio(&self) -> f32 {
        match self {
            AspectRatio::Mobile => 4.0 / 5.0,
            AspectRatio::Desktop => 16.0 / 9.0,
        }
    }
}

/// Result of image processing operation
#[derive(Debug)]
pub struct ProcessingResult {
    pub original_size: u64,
    pub optimized_size: u64,
    pub original_format: String,
    pub optimized_format: String,
    pub dimensions: (u32, u32),
    pub file_path: PathBuf,
    pub compression_ratio: f64,
    pub aspect_ratio: String,
}

/// Main image processor service
pub struct ImageProcessor {
    config: ImageProcessingConfig,
    upload_dir: PathBuf,
    optimizer: ImageOptimizer,
}

impl ImageProcessor {
    pub fn new(upload_dir: impl Into<PathBuf>) -> Self {
        let config = ImageProcessingConfig::default();
        Self {
            optimizer: ImageOptimizer::new(config.webp_quality),
            config,
            upload_dir: upload_dir.into(),
        }
    }

    pub fn with_config(upload_dir: impl Into<PathBuf>, config: ImageProcessingConfig) -> Self {
        Self {
            optimizer: ImageOptimizer::new(config.webp_quality),
            config,
            upload_dir: upload_dir.into(),
        }
    }

    /// Process a single image and generate both aspect ratios
    pub async fn process_product_image(
        &self,
        image_data: &[u8],
        product_slug: &str,
        index: u32,
    ) -> Result<Vec<ProcessingResult>> {
        info!("Processing product image for slug: {}, index: {}", product_slug, index);

        // Load and validate the image
        let (img, _original_format) = self.load_and_validate_image(image_data)
            .context("Failed to load and validate image")?;

        let mut results = Vec::new();

        // Generate both aspect ratios
        for aspect_ratio in [AspectRatio::Desktop, AspectRatio::Mobile] {
            let result = self.process_aspect_ratio(&img, product_slug, index, &aspect_ratio)
                .await
                .with_context(|| format!("Failed to process {} aspect ratio", aspect_ratio.as_str()))?;

            results.push(result);
        }

        info!("Successfully processed {} aspect ratios for {}", results.len(), product_slug);
        Ok(results)
    }

    /// Load image from bytes and perform initial validation
    fn load_and_validate_image(&self, image_data: &[u8]) -> Result<(DynamicImage, ImageFormat)> {
        // Validate file size
        if image_data.len() > 10 * 1024 * 1024 {
            anyhow::bail!("Image file too large. Maximum size is 10MB");
        }

        // Detect format first
        let format = ImageOptimizer::detect_format(image_data)
            .context("Failed to detect image format")?;

        // Load the image
        let img = image::load_from_memory(image_data)
            .context("Failed to decode image. Please ensure the file is a valid image format")?;

        // Validate dimensions
        let (width, height) = img.dimensions();
        if width < 400 || height < 400 {
            anyhow::bail!("Image dimensions too small. Minimum size is 400x400 pixels");
        }

        info!("Loaded image: {}x{}, format: {:?}", width, height, format);
        Ok((img, format))
    }

    /// Process image for a specific aspect ratio
    async fn process_aspect_ratio(
        &self,
        img: &DynamicImage,
        product_slug: &str,
        index: u32,
        aspect_ratio: &AspectRatio,
    ) -> Result<ProcessingResult> {
        // Resize image to target aspect ratio
        let resized_img = self.resize_to_aspect_ratio(img, aspect_ratio)?;

        // Resize if dimensions exceed maximum
        let final_img = self.resize_if_needed(&resized_img)?;

        // Convert to bytes for optimization
        let mut temp_buffer = Vec::new();
        let mut cursor = Cursor::new(&mut temp_buffer);
        final_img.write_to(&mut cursor, ImageFormat::WebP)
            .context("Failed to encode image for optimization")?;

        // Use advanced optimizer with target size optimization
        let optimization_result = if temp_buffer.len() > self.config.target_file_size as usize {
            // If image is larger than target, optimize to target size
            let target_kb = (self.config.target_file_size / 1024) as u32;
            self.optimizer.optimize_to_target_size(&temp_buffer, target_kb)
                .context("Failed to optimize image to target size")?
        } else {
            // Otherwise, use standard optimization
            self.optimizer.optimize_with_info(&temp_buffer)
                .context("Failed to optimize image")?
        };

        // Log optimization results
        optimization_result.print_stats();

        // Generate filename and save
        let filename = format!("{}_{}_{}.webp",
            aspect_ratio.as_str(),
            product_slug,
            index
        );

        let file_path = self.upload_dir.join("products").join(&filename);

        // Ensure directory exists
        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent).await
                .context("Failed to create upload directory")?;
        }

        // Save the optimized image
        fs::write(&file_path, &optimization_result.data).await
            .context("Failed to save optimized image")?;

        let (width, height) = final_img.dimensions();

        Ok(ProcessingResult {
            original_size: optimization_result.original_size as u64,
            optimized_size: optimization_result.optimized_size as u64,
            original_format: format!("{:?}", optimization_result.original_format),
            optimized_format: format!("{:?}", optimization_result.output_format),
            dimensions: (width, height),
            file_path,
            compression_ratio: optimization_result.compression_ratio,
            aspect_ratio: aspect_ratio.as_str().to_string(),
        })
    }

    /// Resize image to match target aspect ratio
    fn resize_to_aspect_ratio(&self, img: &DynamicImage, aspect_ratio: &AspectRatio) -> Result<DynamicImage> {
        let (width, height) = img.dimensions();
        let current_ratio = width as f32 / height as f32;
        let target_ratio = aspect_ratio.ratio();

        info!("Resizing to {} aspect ratio: {}x{} (ratio: {:.3}) -> target ratio: {:.3}",
              aspect_ratio.as_str(), width, height, current_ratio, target_ratio);

        // Validate dimensions
        if width == 0 || height == 0 {
            anyhow::bail!("Invalid image dimensions: {}x{}", width, height);
        }

        let resized = if (current_ratio - target_ratio).abs() < 0.01 {
            // Already close to target ratio
            info!("Image already has target aspect ratio");
            img.clone()
        } else if current_ratio > target_ratio {
            // Image is wider, crop width
            let new_width = (height as f32 * target_ratio) as u32;
            let x_offset = (width - new_width) / 2;

            info!("Cropping width: {}x{} -> {}x{} (offset: {})",
                  width, height, new_width, height, x_offset);

            // Validate crop parameters
            if new_width == 0 || new_width > width || x_offset > width {
                anyhow::bail!("Invalid crop parameters: new_width={}, x_offset={}, original_width={}",
                             new_width, x_offset, width);
            }

            img.crop_imm(x_offset, 0, new_width, height)
        } else {
            // Image is taller, crop height
            let new_height = (width as f32 / target_ratio) as u32;
            let y_offset = (height - new_height) / 2;

            info!("Cropping height: {}x{} -> {}x{} (offset: {})",
                  width, height, width, new_height, y_offset);

            // Validate crop parameters
            if new_height == 0 || new_height > height || y_offset > height {
                anyhow::bail!("Invalid crop parameters: new_height={}, y_offset={}, original_height={}",
                             new_height, y_offset, height);
            }

            img.crop_imm(0, y_offset, width, new_height)
        };

        let (final_width, final_height) = resized.dimensions();
        info!("Aspect ratio conversion completed: {}x{}", final_width, final_height);

        Ok(resized)
    }

    /// Resize image if it exceeds maximum dimensions
    fn resize_if_needed(&self, img: &DynamicImage) -> Result<DynamicImage> {
        let (width, height) = img.dimensions();
        
        if width <= self.config.max_dimension && height <= self.config.max_dimension {
            return Ok(img.clone());
        }

        let scale = (self.config.max_dimension as f32 / width.max(height) as f32).min(1.0);
        let new_width = (width as f32 * scale) as u32;
        let new_height = (height as f32 * scale) as u32;

        info!("Resizing image from {}x{} to {}x{}", width, height, new_width, new_height);
        
        Ok(img.resize(new_width, new_height, image::imageops::FilterType::Lanczos3))
    }



    /// Clean up legacy non-WebP images from products directory
    pub async fn cleanup_legacy_images(&self, dry_run: bool) -> Result<Vec<PathBuf>> {
        let products_dir = self.upload_dir.join("products");
        let mut removed_files = Vec::new();

        if !products_dir.exists() {
            warn!("Products directory does not exist: {:?}", products_dir);
            return Ok(removed_files);
        }

        let mut entries = fs::read_dir(&products_dir).await
            .context("Failed to read products directory")?;

        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            
            if let Some(extension) = path.extension() {
                let ext = extension.to_string_lossy().to_lowercase();
                
                // Remove non-WebP image files
                if matches!(ext.as_str(), "jpg" | "jpeg" | "png" | "gif" | "bmp") {
                    if dry_run {
                        info!("Would remove: {:?}", path);
                    } else {
                        fs::remove_file(&path).await
                            .with_context(|| format!("Failed to remove file: {:?}", path))?;
                        info!("Removed legacy image: {:?}", path);
                    }
                    removed_files.push(path);
                }
            }
        }

        if dry_run {
            info!("Dry run completed. {} files would be removed", removed_files.len());
        } else {
            info!("Cleanup completed. {} legacy images removed", removed_files.len());
        }

        Ok(removed_files)
    }

    /// Validate that a file is a supported image format
    pub fn validate_image_format(data: &[u8]) -> Result<ImageFormat> {
        // Use the optimizer's format detection
        let format = ImageOptimizer::detect_format(data)
            .context("Unable to determine image format")?;

        // Check if format is supported
        match format {
            ImageFormat::Jpeg | ImageFormat::Png | ImageFormat::WebP |
            ImageFormat::Gif | ImageFormat::Bmp => Ok(format),
            _ => anyhow::bail!("Unsupported image format: {:?}", format),
        }
    }
}
