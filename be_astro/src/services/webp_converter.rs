use image::{<PERSON>R<PERSON><PERSON>, ImageFormat, GenericImageView};
use std::fs::{File, remove_file};
use std::io::{Write, Cursor};
use std::path::Path;
use walkdir::WalkDir;
use tracing::{info, warn, error};

/// WebP conversion service for product images
pub struct WebPConverter;

impl WebPConverter {
    /// Convert a single image to WebP format with specified quality
    /// Returns the path to the converted WebP image
    pub async fn convert_image_to_webp(
        file_path: &str,
        _quality: f32,
        target_size_kb: Option<u32>,
    ) -> Result<String, String> {
        // Open and decode the image
        let image = ImageReader::open(file_path)
            .map_err(|e| format!("Failed to open image {}: {}", file_path, e))?
            .with_guessed_format()
            .map_err(|e| format!("Failed to guess format for {}: {}", file_path, e))?
            .decode()
            .map_err(|e| format!("Failed to decode image {}: {}", file_path, e))?;

        // Convert to WebP using the image crate's built-in WebP support
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);

        image.write_to(&mut cursor, ImageFormat::WebP)
            .map_err(|e| format!("Failed to encode WebP for {}: {}", file_path, e))?;

        // Check file size if target is specified
        if let Some(target_kb) = target_size_kb {
            let size_kb = buffer.len() / 1024;
            if size_kb > target_kb as usize {
                info!("Image {} size {}KB exceeds target {}KB, but keeping current quality",
                      file_path, size_kb, target_kb);
            }
        }

        // Generate output path (replace extension with .webp)
        let path = Path::new(file_path);
        let webp_path = path.with_extension("webp");
        let webp_path_str = webp_path.to_string_lossy().to_string();

        // Write WebP file
        let mut webp_file = File::create(&webp_path)
            .map_err(|e| format!("Failed to create WebP file {}: {}", webp_path_str, e))?;

        webp_file.write_all(&buffer)
            .map_err(|e| format!("Failed to write WebP data to {}: {}", webp_path_str, e))?;

        info!("Converted {} to WebP: {}", file_path, webp_path_str);
        Ok(webp_path_str)
    }

    /// Convert all product images in a directory to WebP format
    pub async fn convert_directory_to_webp(
        directory: &str,
        quality: f32,
        target_size_kb: Option<u32>,
    ) -> Result<Vec<String>, String> {
        let mut converted_files = Vec::new();
        let supported_extensions = ["jpg", "jpeg", "png", "gif", "bmp", "tiff"];

        info!("Starting WebP conversion for directory: {}", directory);

        for entry in WalkDir::new(directory)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.file_type().is_file())
        {
            let path = entry.path();
            let path_str = path.to_string_lossy();

            // Check if file has supported extension
            if let Some(extension) = path.extension() {
                let ext_str = extension.to_string_lossy().to_lowercase();
                if supported_extensions.contains(&ext_str.as_str()) {
                    match Self::convert_image_to_webp(&path_str, quality, target_size_kb).await {
                        Ok(webp_path) => {
                            converted_files.push(webp_path);
                            info!("✅ Converted: {}", path_str);
                        }
                        Err(e) => {
                            error!("❌ Failed to convert {}: {}", path_str, e);
                        }
                    }
                }
            }
        }

        info!("Conversion complete. {} files converted to WebP", converted_files.len());
        Ok(converted_files)
    }

    /// Remove non-WebP image files from directory
    pub async fn cleanup_non_webp_images(directory: &str) -> Result<Vec<String>, String> {
        let mut removed_files = Vec::new();
        let extensions_to_remove = ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "svg"];

        info!("Starting cleanup of non-WebP images in: {}", directory);

        for entry in WalkDir::new(directory)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.file_type().is_file())
        {
            let path = entry.path();
            let path_str = path.to_string_lossy();

            // Check if file has extension to remove
            if let Some(extension) = path.extension() {
                let ext_str = extension.to_string_lossy().to_lowercase();
                if extensions_to_remove.contains(&ext_str.as_str()) {
                    // Check if corresponding WebP file exists
                    let webp_path = path.with_extension("webp");
                    if webp_path.exists() {
                        match remove_file(path) {
                            Ok(_) => {
                                removed_files.push(path_str.to_string());
                                info!("🗑️  Removed: {}", path_str);
                            }
                            Err(e) => {
                                error!("❌ Failed to remove {}: {}", path_str, e);
                            }
                        }
                    } else {
                        warn!("⚠️  No WebP equivalent found for {}, skipping removal", path_str);
                    }
                }
            }
        }

        info!("Cleanup complete. {} files removed", removed_files.len());
        Ok(removed_files)
    }

    /// Convert product images with dual aspect ratio support
    pub async fn convert_product_images_with_aspects(
        source_path: &str,
        product_slug: &str,
        image_number: u32,
        _quality: f32,
    ) -> Result<(String, String), String> {
        // Load the original image
        let image = ImageReader::open(source_path)
            .map_err(|e| format!("Failed to open image {}: {}", source_path, e))?
            .with_guessed_format()
            .map_err(|e| format!("Failed to guess format: {}", e))?
            .decode()
            .map_err(|e| format!("Failed to decode image: {}", e))?;

        let (width, height) = image.dimensions();
        
        // Create 4:5 aspect ratio (mobile)
        let mobile_image = if width as f32 / height as f32 > 4.0 / 5.0 {
            // Image is wider, crop width
            let new_width = (height as f32 * 4.0 / 5.0) as u32;
            let x_offset = (width - new_width) / 2;
            image.crop_imm(x_offset, 0, new_width, height)
        } else {
            // Image is taller, crop height
            let new_height = (width as f32 * 5.0 / 4.0) as u32;
            let y_offset = (height - new_height) / 2;
            image.crop_imm(0, y_offset, width, new_height)
        };

        // Create 16:9 aspect ratio (desktop)
        let desktop_image = if width as f32 / height as f32 > 16.0 / 9.0 {
            // Image is wider, crop width
            let new_width = (height as f32 * 16.0 / 9.0) as u32;
            let x_offset = (width - new_width) / 2;
            image.crop_imm(x_offset, 0, new_width, height)
        } else {
            // Image is taller, crop height
            let new_height = (width as f32 * 9.0 / 16.0) as u32;
            let y_offset = (height - new_height) / 2;
            image.crop_imm(0, y_offset, width, new_height)
        };

        // Get directory from source path
        let source_dir = Path::new(source_path).parent()
            .ok_or("Failed to get parent directory")?;

        // Generate output paths
        let mobile_path = source_dir.join(format!("4x5_{}_{}.webp", product_slug, image_number));
        let desktop_path = source_dir.join(format!("16x9_{}_{}.webp", product_slug, image_number));

        // Convert and save mobile version
        let mut mobile_buffer = Vec::new();
        let mut mobile_cursor = Cursor::new(&mut mobile_buffer);
        mobile_image.write_to(&mut mobile_cursor, ImageFormat::WebP)
            .map_err(|e| format!("Failed to encode mobile WebP: {}", e))?;

        let mut mobile_file = File::create(&mobile_path)
            .map_err(|e| format!("Failed to create mobile WebP file: {}", e))?;
        mobile_file.write_all(&mobile_buffer)
            .map_err(|e| format!("Failed to write mobile WebP: {}", e))?;

        // Convert and save desktop version
        let mut desktop_buffer = Vec::new();
        let mut desktop_cursor = Cursor::new(&mut desktop_buffer);
        desktop_image.write_to(&mut desktop_cursor, ImageFormat::WebP)
            .map_err(|e| format!("Failed to encode desktop WebP: {}", e))?;

        let mut desktop_file = File::create(&desktop_path)
            .map_err(|e| format!("Failed to create desktop WebP file: {}", e))?;
        desktop_file.write_all(&desktop_buffer)
            .map_err(|e| format!("Failed to write desktop WebP: {}", e))?;

        let mobile_path_str = mobile_path.to_string_lossy().to_string();
        let desktop_path_str = desktop_path.to_string_lossy().to_string();

        info!("Created dual aspect images: {} and {}", mobile_path_str, desktop_path_str);
        
        Ok((mobile_path_str, desktop_path_str))
    }
}
