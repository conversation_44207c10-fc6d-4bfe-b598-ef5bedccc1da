#!/bin/bash

# Simple Development Environment for Astro Works
# ==============================================

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🚀 Astro Works - Simple Development Setup${NC}"
echo -e "${CYAN}=========================================${NC}"
echo ""

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}[ERROR]${NC} Docker is not running. Please start Docker first."
    exit 1
fi

# Start PostgreSQL
echo -e "${BLUE}[INFO]${NC} Starting PostgreSQL..."
docker run -d --rm \
    --name astro_postgres_dev \
    -e POSTGRES_DB=astro_ecommerce \
    -e POSTGRES_USER=astro_user \
    -e POSTGRES_PASSWORD=astro_password \
    -p 5432:5432 \
    postgres:16-alpine >/dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ PostgreSQL started on port 5432${NC}"
else
    echo -e "${YELLOW}[INFO]${NC} PostgreSQL container might already be running"
fi

# Start Redis
echo -e "${BLUE}[INFO]${NC} Starting Redis..."
docker run -d --rm \
    --name astro_redis_dev \
    -p 6379:6379 \
    redis:7-alpine >/dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Redis started on port 6379${NC}"
else
    echo -e "${YELLOW}[INFO]${NC} Redis container might already be running"
fi

# Wait a bit for databases to initialize
echo -e "${BLUE}[INFO]${NC} Waiting for databases to initialize..."
sleep 5

echo ""
echo -e "${GREEN}[SUCCESS]${NC} Development environment is ready!"
echo ""
echo -e "${CYAN}Database Services:${NC}"
echo -e "  🐘 PostgreSQL: localhost:5432"
echo -e "  🔴 Redis: localhost:6379"
echo ""
echo -e "${CYAN}Next Steps:${NC}"
echo ""
echo -e "${YELLOW}1. Start Backend (Terminal 1):${NC}"
echo -e "   cd be_astro"
echo -e "   cp ../.env.dev .env"
echo -e "   PORT=7998 cargo run --bin be_astro"
echo ""
echo -e "${YELLOW}2. Start Frontend (Terminal 2):${NC}"
echo -e "   cd fe_astro"
echo -e "   bun run dev"
echo ""
echo -e "${CYAN}URLs after starting servers:${NC}"
echo -e "  🌐 Frontend: http://localhost:5173"
echo -e "  🔗 API: http://localhost:7998/api/v1"
echo -e "  ❤️  Health: http://localhost:7998/health"
echo ""
echo -e "${YELLOW}To stop databases:${NC}"
echo -e "   docker stop astro_postgres_dev astro_redis_dev"
