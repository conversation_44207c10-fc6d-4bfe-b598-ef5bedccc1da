version: '3.8'

services:
  # Backend Rust API
  astro-backend:
    build:
      context: ./be_astro
      dockerfile: Dockerfile.prod
    container_name: astro_backend_prod
    ports:
      - "7998:7998"
    environment:
      # Rust-specific configs
      RUST_LOG: ${RUST_LOG:-info}
      RUST_BACKTRACE: 1
      TOKIO_WORKER_THREADS: 4
      
      # Database connection
      DATABASE_URL: postgresql://astro_user:${POSTGRES_PASSWORD}@postgres:5432/astro_ecommerce
      
      # App configs
      HOST: 0.0.0.0
      PORT: 7998
      API_BASE_URL: ${API_BASE_URL:-http://localhost:7998}
      
      # Security
      JWT_SECRET: ${JWT_SECRET}
      ADMIN_EMAIL: ${ADMIN_EMAIL:-<EMAIL>}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD}
      ADMIN_DEFAULT_EMAIL: ${ADMIN_DEFAULT_EMAIL:-<EMAIL>}
      ADMIN_DEFAULT_PASSWORD: ${ADMIN_DEFAULT_PASSWORD}
      
      # WhatsApp config
      WHATSAPP_PHONE_NUMBER: ${WHATSAPP_PHONE_NUMBER:-***********}
      WHATSAPP_BASE_URL: https://wa.me/
      
      # Company info
      COMPANY_NAME: ${COMPANY_NAME}
      COMPANY_EMAIL: ${COMPANY_EMAIL:-<EMAIL>}
      
      # Bank info
      BANK_NAME: ${BANK_NAME:-BCA}
      BANK_ACCOUNT_NUMBER: ${BANK_ACCOUNT_NUMBER}
      BANK_ACCOUNT_NAME: ${BANK_ACCOUNT_NAME}
      
      # Redis
      REDIS_URL: redis://redis:6379
      
      # CORS
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}

    # Resource limits optimized for 1GB VPS
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

    # Enhanced logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7998/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

    # Restart policy
    restart: unless-stopped

    # Security
    security_opt:
      - no-new-privileges:true

    # Volumes for persistent data
    volumes:
      - ./be_astro/static/uploads:/app/static/uploads
      - ./logs:/app/logs
      - ./data:/app/data

    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

    networks:
      - astro_network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: astro_postgres_prod
    environment:
      POSTGRES_DB: astro_ecommerce
      POSTGRES_USER: astro_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./be_astro/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"

    # Enhanced PostgreSQL configuration
    command: >
      postgres
      -c max_connections=50
      -c shared_buffers=64MB
      -c effective_cache_size=256MB
      -c maintenance_work_mem=16MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=4MB
      -c default_statistics_target=50
      -c random_page_cost=1.1
      -c effective_io_concurrency=100
      -c log_statement=none
      -c log_min_duration_statement=2000
      -c work_mem=2MB
      -c temp_buffers=8MB

    # Resource limits for database
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

    # Enhanced logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U astro_user -d astro_ecommerce"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

    restart: unless-stopped

    security_opt:
      - no-new-privileges:true

    networks:
      - astro_network

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: astro_redis_prod
    command: >
      redis-server
      --maxmemory 64mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --appendonly no
      --tcp-keepalive 60
      --timeout 300
    ports:
      - "6379:6379"

    deploy:
      resources:
        limits:
          memory: 64M
          cpus: '0.25'
        reservations:
          memory: 32M
          cpus: '0.1'

    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

    restart: unless-stopped

    security_opt:
      - no-new-privileges:true

    networks:
      - astro_network

volumes:
  postgres_data:
    driver: local

networks:
  astro_network:
    driver: bridge
