# PostgreSQL Database for Development
services:
  postgres:
    image: postgres:16-alpine
    container_name: astro_postgres_dev
    environment:
      POSTGRES_DB: astro_ecommerce
      POSTGRES_USER: astro_user
      POSTGRES_PASSWORD: astro_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U astro_user -d astro_ecommerce"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache for Development
  redis:
    image: redis:7-alpine
    container_name: astro_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
