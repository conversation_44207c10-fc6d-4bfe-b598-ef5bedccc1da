version: '3.8'

services:
  # Backend Rust API
  astro-backend:
    build:
      context: ./be_astro
      dockerfile: Dockerfile.prod
    container_name: astro_backend_prod
    ports:
      - "7998:7998"
    environment:
      # Rust-specific configs
      - RUST_LOG=${RUST_LOG:-info}
      - RUST_BACKTRACE=1
      - TOKIO_WORKER_THREADS=4

      # Database connection with validation
      - DATABASE_URL=postgresql://astro_user:${POSTGRES_PASSWORD?Error: POSTGRES_PASSWORD not set}@postgres:5432/astro_ecommerce

      # App configs
      - APP_HOST=0.0.0.0
      - APP_PORT=7998
      - API_BASE_URL=${API_BASE_URL:-http://localhost:7998}

      # Security - REQUIRED environment variables
      - JWT_SECRET=${JWT_SECRET?Error: JWT_SECRET not set}
      - ADMIN_EMAIL=${ADMIN_EMAIL:-<EMAIL>}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD?Error: ADMIN_PASSWORD not set}

      # WhatsApp config
      - WHATSAPP_PHONE_NUMBER=${WHATSAPP_PHONE_NUMBER:-***********}
      - WHATSAPP_BASE_URL=https://wa.me/

      # Company info
      - COMPANY_NAME=${COMPANY_NAME:-Astro Works Indonesia}
      - COMPANY_EMAIL=${COMPANY_EMAIL:-<EMAIL>}

      # Bank info
      - BANK_NAME=${BANK_NAME:-BCA}
      - BANK_ACCOUNT_NUMBER=${BANK_ACCOUNT_NUMBER?Error: BANK_ACCOUNT_NUMBER not set}
      - BANK_ACCOUNT_NAME=${BANK_ACCOUNT_NAME:-Astro Works Indonesia PT}
      
    # Resource limits optimized for 1GB VPS
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

    # Enhanced logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7998/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

    # Restart policy
    restart: unless-stopped

    # Security
    security_opt:
      - no-new-privileges:true

    # Volumes for persistent data
    volumes:
      - ./be_astro/static/uploads:/app/static/uploads
      - ./logs:/app/logs
      - ./data:/app/data

    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

    networks:
      - astro_network
      - postgres_network

  # Frontend Svelte App
  astro-frontend:
    build:
      context: ./fe_astro
      dockerfile: Dockerfile.prod
    ports:
      - "3000:80"
    environment:
      # API configuration
      - PUBLIC_API_URL=http://localhost:7998/api/v1
      - PUBLIC_API_BASE_URL=http://localhost:7998
      - PUBLIC_FE_URL=http://localhost:3000
      
      # Company configuration
      - PUBLIC_COMPANY_NAME=Astro Works Indonesia
      - PUBLIC_COMPANY_EMAIL=<EMAIL>
      - PUBLIC_WHATSAPP_PHONE_NUMBER=***********
      
      # Feature flags
      - PUBLIC_ENABLE_ANALYTICS=false
      - PUBLIC_ENABLE_CHAT=true
      - PUBLIC_DEBUG_MODE=false
      
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    restart: unless-stopped
    
    security_opt:
      - no-new-privileges:true
    
    depends_on:
      - astro-backend
    
    networks:
      - astro-network

  # PostgreSQL Database with enhanced security
  postgres:
    image: postgres:15-alpine
    container_name: astro_postgres_prod
    environment:
      - POSTGRES_DB=astro_ecommerce
      - POSTGRES_USER=astro_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Error: POSTGRES_PASSWORD not set}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./be_astro/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"

    # Enhanced PostgreSQL configuration with security
    command: >
      postgres
      -c max_connections=50
      -c shared_buffers=64MB
      -c effective_cache_size=256MB
      -c maintenance_work_mem=16MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=4MB
      -c default_statistics_target=50
      -c random_page_cost=1.1
      -c effective_io_concurrency=100
      -c log_statement=none
      -c log_min_duration_statement=2000
      -c work_mem=2MB
      -c temp_buffers=8MB
      -c ssl=on
      -c log_connections=on
      -c log_disconnections=on

    # Resource limits for database
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

    # Enhanced logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U astro_user -d astro_ecommerce"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

    restart: unless-stopped

    security_opt:
      - no-new-privileges:true

    networks:
      - postgres_network

  # Redis for caching (lightweight)
  redis:
    image: redis:7-alpine
    container_name: astro_redis_prod
    command: >
      redis-server
      --maxmemory 64mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save ""
      --appendonly no
      --tcp-keepalive 60
      --timeout 300
    ports:
      - "6379:6379"

    deploy:
      resources:
        limits:
          memory: 64M
          cpus: '0.25'
        reservations:
          memory: 32M
          cpus: '0.1'

    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

    restart: unless-stopped

    security_opt:
      - no-new-privileges:true

    networks:
      - astro_network

  # Database Backup Service
  backup:
    image: postgres:15-alpine
    container_name: astro_backup
    environment:
      - PGPASSWORD=${POSTGRES_PASSWORD?Error: POSTGRES_PASSWORD not set}
    volumes:
      - postgres_data:/var/lib/postgresql/data:ro
      - ./backups:/backups
    command: |
      sh -c 'while true; do
        echo "Starting backup at $$(date)"
        pg_dump -h postgres -U astro_user -d astro_ecommerce > /backups/backup_$$(date +%Y%m%d_%H%M%S).sql
        if [ $$? -eq 0 ]; then
          echo "Backup completed successfully"
        else
          echo "Backup failed"
        fi
        find /backups -name "backup_*.sql" -mtime +7 -delete
        echo "Next backup in 24 hours"
        sleep 86400
      done'

    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'
        reservations:
          memory: 64M
          cpus: '0.1'

    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

    restart: unless-stopped

    depends_on:
      postgres:
        condition: service_healthy

    networks:
      - postgres_network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: astro_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'
        reservations:
          memory: 64M
          cpus: '0.1'

    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

    restart: unless-stopped

    networks:
      - astro_network

volumes:
  postgres_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  astro_network:
    driver: bridge
    internal: false  # Allow external access for API
  postgres_network:
    driver: bridge
    internal: true   # Isolated network for database
