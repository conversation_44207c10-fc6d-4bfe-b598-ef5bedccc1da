version: '3.8'

services:

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: astro_nginx_vps
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-vps/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx-vps/logs:/var/log/nginx
    depends_on:
      - backend
    networks:
      - astro_network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: astro_postgres_vps
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./be_astro/migrations:/docker-entrypoint-initdb.d
    networks:
      - astro_network
    restart: unless-stopped
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c log_statement=none
      -c log_min_duration_statement=1000
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: astro_redis_vps
    volumes:
      - redis_data:/data
    networks:
      - astro_network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Rust Backend API
  backend:
    build:
      context: ./be_astro
      dockerfile: Dockerfile
    container_name: astro_backend_vps
    environment:
      DATABASE_URL: postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      HOST: 0.0.0.0
      PORT: 8080
      RUST_LOG: ${RUST_LOG:-info}
      CORS_ORIGIN: ${CORS_ORIGIN}
      WHATSAPP_PHONE_NUMBER: ${WHATSAPP_PHONE_NUMBER}
      ADMIN_DEFAULT_EMAIL: ${ADMIN_DEFAULT_EMAIL}
      ADMIN_DEFAULT_PASSWORD: ${ADMIN_DEFAULT_PASSWORD}
      BANK_NAME: ${BANK_NAME}
      BANK_ACCOUNT_NUMBER: ${BANK_ACCOUNT_NUMBER}
      BANK_ACCOUNT_NAME: ${BANK_ACCOUNT_NAME}
      ENVIRONMENT: ${ENVIRONMENT}
      ENABLE_METRICS: ${ENABLE_METRICS}
    volumes:
      - ./be_astro/static:/app/static
      - ./data:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - astro_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  astro_network:
    driver: bridge
