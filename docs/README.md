# Documentation Directory

This directory contains all documentation for the Astro Works E-commerce Platform, organized by functionality and topic.

## 📁 Directory Structure

```
docs/
├── deployment/         # 🚀 Deployment guides and procedures
├── environment/        # ⚙️ Environment setup and configuration
├── frontend/          # 🎨 Frontend implementation guides
├── backend/           # 🔧 Backend development documentation
├── images/            # 🖼️ Image management and optimization
├── ssl/               # 🔒 SSL/HTTPS and domain setup
├── guides/            # 📖 General guides and troubleshooting
└── README.md          # 📋 This documentation index
```

## 🚀 Deployment Documentation (`deployment/`)

Comprehensive guides for deploying the application to various environments.

| Document | Description |
|----------|-------------|
| [`DEPLOYMENT.md`](deployment/DEPLOYMENT.md) | General deployment procedures |
| [`DEPLOYMENT_GUIDE.md`](deployment/DEPLOYMENT_GUIDE.md) | Detailed deployment guide |
| [`VPS_DEPLOYMENT_GUIDE.md`](deployment/VPS_DEPLOYMENT_GUIDE.md) | VPS-specific deployment instructions |

## ⚙️ Environment Configuration (`environment/`)

Documentation for setting up and managing environment variables and configurations.

| Document | Description |
|----------|-------------|
| [`ENV_CONFIGURATION_GUIDE.md`](environment/ENV_CONFIGURATION_GUIDE.md) | Environment variable configuration |
| [`ENV_SETUP_SUMMARY.md`](environment/ENV_SETUP_SUMMARY.md) | Environment setup summary |
| [`WRANGLER_ENV_GUIDE.md`](environment/WRANGLER_ENV_GUIDE.md) | Wrangler environment management |
| [`WRANGLER_INSTALLATION_COMPLETE.md`](environment/WRANGLER_INSTALLATION_COMPLETE.md) | Wrangler installation guide |
| [`WRANGLER_SETUP_SUMMARY.md`](environment/WRANGLER_SETUP_SUMMARY.md) | Wrangler setup summary |

## 🎨 Frontend Documentation (`frontend/`)

Frontend implementation guides and component documentation.

| Document | Description |
|----------|-------------|
| [`HOMEPAGE_IMPLEMENTATION.md`](frontend/HOMEPAGE_IMPLEMENTATION.md) | Homepage implementation guide |
| [`PRODUCT_DETAIL_IMPLEMENTATION.md`](frontend/PRODUCT_DETAIL_IMPLEMENTATION.md) | Product detail page implementation |

## 🔧 Backend Documentation (`backend/`)

Backend development documentation and API guides.

*Note: Backend-specific documentation can be added here as needed.*

## 🖼️ Image Management (`images/`)

Documentation for image processing, optimization, and management systems.

| Document | Description |
|----------|-------------|
| [`IMAGE_OPTIMIZATION_SYSTEM.md`](images/IMAGE_OPTIMIZATION_SYSTEM.md) | Image optimization system overview |
| [`README_image_download.md`](images/README_image_download.md) | Image download procedures |
| [`README_image_system.md`](images/README_image_system.md) | Image system documentation |

## 🔒 SSL/HTTPS Documentation (`ssl/`)

SSL certificate management and HTTPS setup guides.

| Document | Description |
|----------|-------------|
| [`DOMAIN_SETUP.md`](ssl/DOMAIN_SETUP.md) | Domain configuration guide |
| [`HTTPS-SETUP.md`](ssl/HTTPS-SETUP.md) | HTTPS setup procedures |

## 📖 General Guides (`guides/`)

Troubleshooting guides and general documentation.

| Document | Description |
|----------|-------------|
| [`README_404_fixes.md`](guides/README_404_fixes.md) | 404 error troubleshooting |

## 🔍 Quick Navigation

### For Developers
- **Getting Started**: [`environment/ENV_SETUP_SUMMARY.md`](environment/ENV_SETUP_SUMMARY.md)
- **Frontend Development**: [`frontend/`](frontend/)
- **Image Management**: [`images/`](images/)

### For DevOps
- **Deployment**: [`deployment/VPS_DEPLOYMENT_GUIDE.md`](deployment/VPS_DEPLOYMENT_GUIDE.md)
- **SSL Setup**: [`ssl/HTTPS-SETUP.md`](ssl/HTTPS-SETUP.md)
- **Environment Config**: [`environment/ENV_CONFIGURATION_GUIDE.md`](environment/ENV_CONFIGURATION_GUIDE.md)

### For Troubleshooting
- **404 Errors**: [`guides/README_404_fixes.md`](guides/README_404_fixes.md)
- **Image Issues**: [`images/README_image_system.md`](images/README_image_system.md)
- **Wrangler Issues**: [`environment/WRANGLER_ENV_GUIDE.md`](environment/WRANGLER_ENV_GUIDE.md)

## 📝 Contributing to Documentation

When adding new documentation:

1. **Choose the right category** - Place docs in the appropriate subdirectory
2. **Use descriptive names** - Follow the existing naming conventions
3. **Update this index** - Add new documents to the relevant tables above
4. **Cross-reference** - Link related documents when helpful
5. **Keep it current** - Update docs when features change

## 🔗 Related Documentation

- **Main README**: [`../README.md`](../README.md) - Project overview and quick start
- **Frontend README**: [`../fe_astro/README.md`](../fe_astro/README.md) - Frontend-specific documentation
- **Scripts Documentation**: [`../scripts/README.md`](../scripts/README.md) - Script organization and usage

---

*This documentation is organized to help you find information quickly. If you can't find what you're looking for, check the main project README or create an issue.*
