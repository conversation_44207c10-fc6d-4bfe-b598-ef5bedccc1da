# 🚀 Astro Works E-commerce Platform - Production Deployment Guide

This guide covers the complete production deployment process for the Astro Works e-commerce platform.

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 20GB free space
- **CPU**: 2+ cores recommended

### Required Software
- Docker 20.10+
- Docker Compose 2.0+
- Git
- curl
- Make

### Domain & SSL
- Domain name pointing to your server
- SSL certificate (Let's Encrypt recommended)

## 🔧 Quick Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd astro-works
```

### 2. Initial Setup
```bash
# Run initial setup
make setup-prod

# Edit production environment
nano .env.prod
```

### 3. Configure Environment
Edit `.env.prod` with your production values:

```bash
# Domain Configuration
DOMAIN=your-domain.com
API_URL=https://your-domain.com/api/v1
FRONTEND_URL=https://your-domain.com

# Security
POSTGRES_PASSWORD=your_super_secure_password
JWT_SECRET=your_super_secure_jwt_secret_32_chars_min
TRAEFIK_AUTH=admin:$2y$10$hashed_password

# Email for SSL
ACME_EMAIL=<EMAIL>
```

### 4. Deploy
```bash
# Build and deploy
make deploy

# Or use the deployment script
./scripts/deploy.sh
```

## 📁 Project Structure

```
astro-works/
├── be_astro/                 # Backend (Rust/Axum)
│   ├── src/                  # Source code
│   ├── Dockerfile.prod       # Production Dockerfile
│   └── static/               # Static files
├── fe_astro/                 # Frontend (Svelte)
│   ├── src/                  # Source code
│   ├── Dockerfile.prod       # Production Dockerfile
│   └── nginx.conf            # Nginx configuration
├── scripts/                  # Deployment scripts
│   ├── deploy.sh             # Main deployment script
│   ├── monitor.sh            # Production monitoring
│   └── health-check.sh       # Health check script
├── docker-compose.prod.yml   # Production compose file
├── .env.prod.example         # Environment template
└── Makefile                  # Automation commands
```

## 🐳 Docker Services

### Production Stack
- **PostgreSQL 16**: Primary database
- **Redis 7**: Caching and sessions
- **Backend**: Rust/Axum API server
- **Frontend**: Svelte app with Nginx
- **Traefik**: Reverse proxy with SSL

### Service Architecture
```
Internet → Traefik (SSL) → Frontend/Backend → Database/Cache
```

## 🔒 Security Features

### SSL/TLS
- Automatic SSL certificates via Let's Encrypt
- HTTP to HTTPS redirect
- Security headers (HSTS, CSP, etc.)

### Authentication
- JWT-based authentication
- Argon2 password hashing
- Rate limiting on API endpoints

### Network Security
- Internal Docker networks
- Non-root containers
- Minimal attack surface

## 📊 Monitoring & Maintenance

### Health Checks
```bash
# Run health check
./scripts/health-check.sh

# Continuous monitoring
./scripts/monitor.sh

# Check service status
make status
```

### Logs
```bash
# View all logs
make logs-prod

# View specific service logs
docker-compose -f docker-compose.prod.yml logs backend
docker-compose -f docker-compose.prod.yml logs frontend
```

### Backups
```bash
# Create backup
make backup

# Restore from backup
make restore BACKUP_FILE=backup_20240101_120000.sql
```

## 🔄 Updates & Maintenance

### Application Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and redeploy
make deploy

# Or use zero-downtime deployment
./scripts/deploy.sh --no-backup
```

### Database Migrations
```bash
# Run migrations
make migrate

# Or manually
docker-compose -f docker-compose.prod.yml exec backend ./be_astro migrate
```

### System Maintenance
```bash
# Restart services
make restart-prod

# Clean up old images
docker system prune -f

# Update dependencies
make update
```

## 🚨 Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# Check logs
docker-compose -f docker-compose.prod.yml logs

# Restart specific service
docker-compose -f docker-compose.prod.yml restart backend
```

#### SSL Certificate Issues
```bash
# Check certificate status
make ssl

# Force certificate renewal
docker-compose -f docker-compose.prod.yml restart traefik
```

#### Database Connection Issues
```bash
# Check database health
docker-compose -f docker-compose.prod.yml exec postgres pg_isready -U astro_user

# Reset database (⚠️ DESTRUCTIVE)
make db-reset
```

#### High Resource Usage
```bash
# Monitor resources
./scripts/monitor.sh

# Check container stats
docker stats

# Scale services (if needed)
docker-compose -f docker-compose.prod.yml up -d --scale backend=2
```

## 📈 Performance Optimization

### Database Optimization
- Connection pooling configured
- Optimized PostgreSQL settings
- Regular VACUUM and ANALYZE

### Caching Strategy
- Redis for session storage
- Static file caching via Nginx
- API response caching

### Frontend Optimization
- Gzip compression enabled
- Static asset caching
- WebP image optimization

## 🔧 Advanced Configuration

### Custom Domain Setup
1. Point your domain to the server IP
2. Update `DOMAIN` in `.env.prod`
3. Redeploy: `make deploy`

### Load Balancing
For high-traffic scenarios, consider:
- Multiple backend instances
- Database read replicas
- CDN for static assets

### Monitoring Integration
- Prometheus metrics available
- Grafana dashboard support
- Custom alerting rules

## 📞 Support

### Useful Commands
```bash
# Quick reference
make help

# Health check
make monitor

# View logs
make logs-prod

# Emergency stop
docker-compose -f docker-compose.prod.yml down
```

### Log Locations
- Application logs: Docker container logs
- System logs: `/var/log/`
- Backup logs: `data/backups/`

---

## 🎯 Production Checklist

Before going live:

- [ ] Domain configured and pointing to server
- [ ] SSL certificates working
- [ ] Environment variables set securely
- [ ] Database migrations completed
- [ ] Backups configured and tested
- [ ] Monitoring setup and alerts configured
- [ ] Security headers verified
- [ ] Performance testing completed
- [ ] Health checks passing

---

**🚀 Your Astro Works e-commerce platform is ready for production!**
