# Astro Works E-commerce Deployment Guide

## 🚀 Quick Deployment

### Prerequisites
- Server with <PERSON><PERSON> and <PERSON>er Compose
- Domain name pointing to your server
- Email address for SSL certificates

### One-Command Production Deployment

```bash
# Set your domain and deploy
DOMAIN=your-domain.com make deploy
```

## 📋 Detailed Deployment Steps

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group
sudo usermod -aG docker $USER
```

### 2. Clone and Setup

```bash
# Clone repository
git clone <your-repo-url>
cd astro-works

# Validate environment
./scripts/validate-setup.sh

# Initial setup
make setup
```

### 3. Configure Environment

```bash
# Copy and edit environment file
cp .env.example .env.prod

# Edit configuration
nano .env.prod
```

**Required Configuration:**
```env
DOMAIN=your-domain.com
ACME_EMAIL=<EMAIL>
POSTGRES_PASSWORD=secure_password_here
JWT_SECRET=your-super-secret-jwt-key-min-32-characters
```

**Generate Traefik Authentication:**
```bash
./scripts/generate-traefik-auth.sh admin your_password
# Copy the output to TRAEFIK_AUTH in .env.prod
```

### 4. DNS Configuration

Point your domain to your server:
```
A     your-domain.com        → YOUR_SERVER_IP
A     traefik.your-domain.com → YOUR_SERVER_IP
CNAME www.your-domain.com    → your-domain.com
```

### 5. Deploy

```bash
# Build and deploy
make build
make deploy

# Or in one command
make quick-deploy
```

### 6. Verify Deployment

```bash
# Check service status
make status

# View logs
make logs-prod

# Health check
curl https://your-domain.com/health
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DOMAIN` | Your domain name | `astro-works.local` |
| `ACME_EMAIL` | Email for SSL certificates | `admin@domain` |
| `POSTGRES_PASSWORD` | Database password | `astro_password_prod` |
| `JWT_SECRET` | JWT signing secret | Required |
| `TRAEFIK_AUTH` | Dashboard auth hash | Required |

### Traefik Configuration

**Custom SSL Certificates:**
```yaml
# In traefik/dynamic.yml
tls:
  certificates:
    - certFile: /etc/ssl/certs/your-domain.crt
      keyFile: /etc/ssl/private/your-domain.key
```

**Custom Rate Limiting:**
```yaml
# In traefik/dynamic.yml
http:
  middlewares:
    api-ratelimit:
      rateLimit:
        average: 20  # requests per second
        burst: 40    # burst capacity
```

### Database Configuration

**Performance Tuning:**
```yaml
# In docker-compose.prod.yml
postgres:
  command: >
    postgres
    -c max_connections=200
    -c shared_buffers=512MB
    -c effective_cache_size=2GB
```

## 🔒 Security Checklist

### Pre-Deployment
- [ ] Change default passwords
- [ ] Generate strong JWT secret
- [ ] Configure Traefik authentication
- [ ] Review CORS settings
- [ ] Set up proper firewall rules

### Post-Deployment
- [ ] Verify SSL certificates
- [ ] Test rate limiting
- [ ] Check security headers
- [ ] Validate backup procedures
- [ ] Monitor logs for errors

### Firewall Configuration
```bash
# Allow only necessary ports
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

## 📊 Monitoring & Maintenance

### Health Monitoring
```bash
# Automated health checks
./scripts/health-check.sh

# Service status
make status

# View metrics
curl https://traefik.your-domain.com/metrics
```

### Log Management
```bash
# View logs
make logs-prod

# Follow specific service
docker-compose -f docker-compose.prod.yml logs -f backend

# Log rotation (add to crontab)
0 2 * * * docker system prune -f
```

### Backup Strategy
```bash
# Daily backup (add to crontab)
0 2 * * * cd /path/to/astro-works && make backup

# Backup retention script
find backups/ -name "*.sql" -mtime +7 -delete
```

## 🔄 Updates & Maintenance

### Application Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and redeploy
make build
make restart-prod

# Or use rolling update
docker-compose -f docker-compose.prod.yml up -d --no-deps backend
```

### Database Migrations
```bash
# Run migrations
docker-compose -f docker-compose.prod.yml exec backend cargo run --bin migrate

# Backup before migrations
make backup
```

### SSL Certificate Renewal
Traefik automatically renews Let's Encrypt certificates. Monitor logs:
```bash
docker-compose -f docker-compose.prod.yml logs traefik | grep -i acme
```

## 🚨 Troubleshooting

### Common Issues

**SSL Certificate Issues:**
```bash
# Check certificate status
docker-compose -f docker-compose.prod.yml logs traefik | grep -i cert

# Force certificate renewal
docker-compose -f docker-compose.prod.yml restart traefik
```

**Database Connection Issues:**
```bash
# Check database status
docker-compose -f docker-compose.prod.yml exec postgres pg_isready

# Reset database connection
docker-compose -f docker-compose.prod.yml restart backend
```

**Performance Issues:**
```bash
# Check resource usage
docker stats

# Monitor response times
./performance_test.sh
```

### Emergency Procedures

**Rollback Deployment:**
```bash
# Stop current deployment
docker-compose -f docker-compose.prod.yml down

# Restore from backup
make restore BACKUP_FILE=backup_filename.sql

# Start previous version
git checkout previous-tag
make deploy
```

**Database Recovery:**
```bash
# Emergency database restore
docker-compose -f docker-compose.prod.yml down
docker volume rm astro-works_postgres_data
docker-compose -f docker-compose.prod.yml up -d postgres
make restore BACKUP_FILE=latest_backup.sql
```

## 📞 Support

For deployment issues:
1. Check logs: `make logs-prod`
2. Verify configuration: `./scripts/validate-setup.sh`
3. Review health status: `./scripts/health-check.sh`
4. Consult troubleshooting section above

---

**Remember:** Always test deployments in a staging environment first!
