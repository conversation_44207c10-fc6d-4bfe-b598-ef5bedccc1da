# 🚀 VPS Auto Deployment Guide

Panduan lengkap untuk auto deployment backend Astro Works E-commerce ke VPS menggunakan Docker context.

## 📋 Overview

Sistem deployment ini menggunakan Docker context untuk melakukan deployment otomatis ke VPS remote tanpa perlu login manual ke server. <PERSON><PERSON><PERSON> proses deployment dilakukan dari local machine menggunakan Docker context yang terhubung ke VPS via SSH.

## 🏗️ Architecture

```
Local Machine                    VPS (*************)
├── Docker Context              ├── Docker Engine
├── Project Files               ├── Astro Works Project
├── Deployment Scripts          ├── PostgreSQL Database
└── Monitoring Tools            ├── Redis Cache
                                ├── Rust Backend
                                └── Traefik Proxy
```

## 🔧 VPS Configuration

### Server Details
- **IP Address**: *************
- **Username**: servomo
- **OS**: Ubuntu/Debian (recommended)
- **Docker**: Auto-installed by setup script

### Required Ports
- **80**: HTTP (Traefik)
- **443**: HTTPS (Traefik)
- **8000**: Backend API
- **8080**: Traefik Dashboard
- **22**: SSH (for deployment)

## 🚀 Quick Start

### 1. Setup Docker Context
```bash
# Setup VPS Docker context (one-time setup)
make setup-vps

# Or manually
./scripts/setup-docker-context.sh
```

### 2. Deploy to VPS
```bash
# Deploy backend to VPS
make deploy-vps

# Or manually
./scripts/deploy-vps.sh
```

### 3. Monitor Deployment
```bash
# Check deployment status
make status-vps

# Monitor VPS health
make monitor-vps

# View logs
make logs-vps
```

## 📁 Files Created

### Configuration Files
```
.env.vps                    # VPS environment configuration
scripts/setup-docker-context.sh    # Docker context setup
scripts/deploy-vps.sh              # VPS deployment script
scripts/monitor-vps.sh             # VPS monitoring script
```

### VPS Directory Structure
```
~/astro-works/
├── be_astro/              # Backend source code
├── docker-compose.prod.yml # Production compose file
├── .env.vps               # Environment variables
├── data/
│   ├── postgres/          # Database data
│   ├── redis/             # Redis data
│   └── uploads/           # File uploads
├── logs/                  # Application logs
├── backups/               # Database backups
└── ssl/                   # SSL certificates
```

## 🛠️ Available Commands

### Setup & Deployment
| Command | Description |
|---------|-------------|
| `make setup-vps` | Setup Docker context for VPS |
| `make deploy-vps` | Deploy backend to VPS |
| `make rollback-vps` | Rollback VPS deployment |

### Monitoring & Management
| Command | Description |
|---------|-------------|
| `make status-vps` | Check container status |
| `make logs-vps` | View backend logs |
| `make monitor-vps` | Full VPS monitoring |
| `make health-vps` | Quick health check |
| `make ssh-vps` | SSH into VPS |

### Manual Docker Commands
```bash
# List containers on VPS
docker --context astro-vps ps

# View logs
docker --context astro-vps logs astro_backend_prod

# Execute commands in container
docker --context astro-vps exec astro_backend_prod /bin/bash

# Check container stats
docker --context astro-vps stats
```

## 🔧 Configuration

### Environment Variables (.env.vps)
```env
# Domain Configuration
DOMAIN=astrokabinet.id
API_DOMAIN=api.astrokabinet.id
FRONTEND_URL=https://astrokabinet.id
API_URL=https://api.astrokabinet.id/api/v1

# Database Configuration
POSTGRES_USER=astro_user
POSTGRES_PASSWORD=astro_secure_password_2024
POSTGRES_DB=astro_ecommerce

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-for-production
ADMIN_DEFAULT_EMAIL=<EMAIL>
ADMIN_DEFAULT_PASSWORD=admin_secure_2024

# Business Configuration
WHATSAPP_PHONE_NUMBER=***********
BANK_NAME=BCA
BANK_ACCOUNT_NUMBER=**********
```

### Docker Context Configuration
```bash
# View Docker contexts
docker context ls

# Switch to VPS context
docker context use astro-vps

# Switch back to local
docker context use default
```

## 🔄 Deployment Process

### Automated Steps
1. **Prerequisites Check**: Verify Docker context and connectivity
2. **File Sync**: Sync project files to VPS via rsync
3. **Backup Creation**: Create database backup before deployment
4. **Image Building**: Build Docker images on VPS
5. **Service Deployment**: Deploy services with Docker Compose
6. **Health Checks**: Wait for services to become healthy
7. **Database Migration**: Run database migrations
8. **Cleanup**: Remove unused Docker images

### Manual Verification
```bash
# Check if services are running
make status-vps

# Test backend API
curl http://*************:8000/health

# Check logs for errors
make logs-vps
```

## 📊 Monitoring

### Health Checks
- **Backend**: HTTP health endpoint
- **Database**: PostgreSQL connection test
- **Redis**: Redis ping command
- **System**: CPU, memory, disk usage

### Monitoring Dashboard
```bash
# Full monitoring report
make monitor-vps

# Continuous health monitoring
watch -n 30 ./scripts/monitor-vps.sh --health

# Generate monitoring report
./scripts/monitor-vps.sh --report
```

### Log Analysis
```bash
# View recent backend logs
docker --context astro-vps logs --tail 100 astro_backend_prod

# Follow logs in real-time
docker --context astro-vps logs -f astro_backend_prod

# Search for errors
docker --context astro-vps logs astro_backend_prod | grep -i error
```

## 🔐 Security

### SSH Key Authentication
- Automatic SSH key setup
- Password-less authentication
- Secure Docker context communication

### Network Security
- Traefik reverse proxy
- Automatic HTTPS with Let's Encrypt
- Internal Docker network isolation

### Data Security
- Automatic database backups
- Environment variable encryption
- Non-root container execution

## 🚨 Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   ```bash
   # Test SSH connectivity
   ssh servomo@*************
   
   # Copy SSH key if needed
   ssh-copy-id servomo@*************
   ```

2. **Docker Context Not Working**
   ```bash
   # Recreate Docker context
   docker context rm astro-vps
   ./scripts/setup-docker-context.sh
   ```

3. **Deployment Failed**
   ```bash
   # Check VPS status
   make monitor-vps
   
   # Rollback if needed
   make rollback-vps
   ```

4. **Service Not Healthy**
   ```bash
   # Check container logs
   make logs-vps
   
   # Check system resources
   ./scripts/monitor-vps.sh --resources
   ```

### Debug Commands
```bash
# SSH into VPS
make ssh-vps

# Check Docker on VPS
ssh servomo@************* "docker ps"

# Check system resources
ssh servomo@************* "htop"

# Check network connectivity
ssh servomo@************* "netstat -tlnp"
```

## 🔄 Rollback Process

### Automatic Rollback
```bash
# Rollback to previous version
make rollback-vps
```

### Manual Rollback
```bash
# Stop current services
docker --context astro-vps compose -f ~/astro-works/docker-compose.prod.yml down

# Restore from backup
docker --context astro-vps exec -i astro_postgres_prod \
  psql -U astro_user -d astro_ecommerce < ~/astro-works/backups/latest_backup.sql

# Restart services
docker --context astro-vps compose -f ~/astro-works/docker-compose.prod.yml up -d
```

## 📈 Performance Optimization

### Resource Monitoring
- CPU usage monitoring
- Memory usage tracking
- Disk space monitoring
- Network I/O analysis

### Optimization Tips
1. **Database**: Tune PostgreSQL configuration
2. **Redis**: Configure memory limits
3. **Backend**: Optimize Rust build settings
4. **Docker**: Use multi-stage builds

## 🎯 Next Steps

1. **Domain Setup**: Configure DNS to point to VPS IP
2. **SSL Certificates**: Setup Let's Encrypt certificates
3. **Monitoring**: Setup external monitoring (Uptime Robot, etc.)
4. **Backup Strategy**: Configure automated backups
5. **CI/CD**: Integrate with GitHub Actions

## 📞 Support

### Useful Resources
- VPS Provider Documentation
- Docker Context Documentation
- Traefik Configuration Guide
- PostgreSQL Tuning Guide

### Emergency Contacts
- VPS Provider Support
- Domain Registrar Support
- SSL Certificate Provider

---

**🎉 VPS Auto Deployment is now ready!**

Use `make deploy-vps` to deploy and `make monitor-vps` to monitor your deployment.
