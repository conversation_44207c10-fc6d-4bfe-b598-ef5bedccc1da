# Environment Configuration Guide

Panduan lengkap untuk mengatur file environment (.env) pada proyek Astro Works E-commerce.

## 📁 Struktur File Environment

```
projectastro/
├── .env                    # Global configuration (Docker Compose)
├── .env.production        # Production configuration template
├── .env.example          # Global configuration template
├── be_astro/
│   ├── .env              # Backend configuration (development)
│   └── .env.example      # Backend configuration template
└── fe_astro/
    ├── .env              # Frontend configuration (development)
    └── .env.example      # Frontend configuration template
```

## 🔧 Konfigurasi per Environment

### 1. Global Configuration (Root `.env`)
File ini digunakan untuk Docker Compose dan konfigurasi deployment global.

**Lokasi**: `/projectastro/.env`

**Fungsi**:
- Konfigurasi domain dan SSL
- Database dan Redis untuk Docker
- Traefik reverse proxy
- Konfigurasi global deployment

### 2. Backend Configuration (`be_astro/.env`)
File ini digunakan khusus untuk aplikasi Rust backend.

**Lokasi**: `/projectastro/be_astro/.env`

**Fungsi**:
- Database connection pool
- JWT dan security settings
- File upload configuration
- Business logic settings
- Monitoring dan logging

### 3. Frontend Configuration (`fe_astro/.env`)
File ini digunakan khusus untuk aplikasi Svelte frontend.

**Lokasi**: `/projectastro/fe_astro/.env`

**Fungsi**:
- API endpoints configuration
- UI/UX settings
- Feature flags
- Client-side business settings

## 🚀 Quick Setup

### Development Environment

1. **Copy template files**:
```bash
# Global configuration
cp .env.example .env

# Backend configuration
cp be_astro/.env.example be_astro/.env

# Frontend configuration
cp fe_astro/.env.example fe_astro/.env
```

2. **Update sensitive values**:
- Change `JWT_SECRET` to a secure random string (min 32 chars)
- Update `ADMIN_DEFAULT_PASSWORD` 
- Set your WhatsApp number in `WHATSAPP_PHONE_NUMBER`
- Update bank account information

### Production Environment

1. **Use production template**:
```bash
cp .env.production .env
```

2. **Update ALL sensitive values**:
- Generate strong passwords for database
- Create secure JWT secret
- Set production domains
- Configure SSL certificates
- Update all default passwords

## 🔐 Security Checklist

### Development
- [ ] Change default admin password
- [ ] Set unique JWT secret
- [ ] Configure CORS origins
- [ ] Set appropriate log levels

### Production
- [ ] Use strong database passwords
- [ ] Generate secure JWT secret (min 32 chars)
- [ ] Set production domains
- [ ] Configure SSL/HTTPS
- [ ] Enable rate limiting
- [ ] Set secure admin credentials
- [ ] Configure backup settings
- [ ] Enable monitoring
- [ ] Disable debug modes

## 📋 Environment Variables Reference

### Database Configuration
```env
DATABASE_URL=postgres://user:password@host:port/database
POSTGRES_USER=astro_user
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=astro_ecommerce
```

### Security Configuration
```env
JWT_SECRET=your-super-secure-jwt-secret-minimum-32-characters
ADMIN_DEFAULT_EMAIL=<EMAIL>
ADMIN_DEFAULT_PASSWORD=secure_admin_password
```

### Business Configuration
```env
WHATSAPP_PHONE_NUMBER=***********
BANK_NAME=BCA
BANK_ACCOUNT_NUMBER=**********
BANK_ACCOUNT_NAME=Astro Works Indonesia PT
```

### API Configuration (Frontend)
```env
VITE_API_URL=http://localhost:8000/api/v1
VITE_STATIC_URL=http://localhost:8000/static
VITE_WHATSAPP_PHONE_NUMBER=***********
```

## 🔄 Environment Switching

### Development to Production
1. Update domain configurations
2. Change all passwords and secrets
3. Enable production optimizations
4. Configure SSL certificates
5. Set production CORS origins

### Local Development with HTTPS
```env
# Global .env
DOMAIN=astrokabinet.id
FRONTEND_URL=https://astrokabinet.id
API_URL=https://api.astrokabinet.id/api/v1

# Frontend .env
VITE_API_URL=https://api.astrokabinet.id/api/v1

# Backend .env
CORS_ORIGIN=https://astrokabinet.id
```

## 🛠️ Troubleshooting

### Common Issues

1. **CORS Errors**:
   - Check `CORS_ORIGIN` in backend .env
   - Ensure frontend URL matches CORS settings

2. **Database Connection**:
   - Verify `DATABASE_URL` format
   - Check database credentials
   - Ensure database is running

3. **API Not Accessible**:
   - Check `VITE_API_URL` in frontend
   - Verify backend is running on correct port
   - Check network connectivity

4. **WhatsApp Integration**:
   - Verify `WHATSAPP_PHONE_NUMBER` format
   - Check `WHATSAPP_BASE_URL` setting

### Validation Commands

```bash
# Check if all required env vars are set
make validate-env

# Test database connection
make test-db

# Verify API endpoints
make test-api

# Check frontend configuration
cd fe_astro && npm run check-env
```

## 📝 Best Practices

1. **Never commit .env files** to version control
2. **Use .env.example** as templates
3. **Rotate secrets regularly** in production
4. **Use different secrets** for each environment
5. **Document custom variables** in this guide
6. **Validate configuration** before deployment
7. **Use environment-specific values** appropriately
8. **Keep sensitive data secure** and encrypted

## 🔗 Related Documentation

- [Docker Compose Configuration](./docker-compose.yml)
- [Traefik Setup](./traefik/traefik.yml)
- [SSL Configuration](./HTTPS-SETUP.md)
- [Deployment Guide](./DEPLOYMENT.md)
