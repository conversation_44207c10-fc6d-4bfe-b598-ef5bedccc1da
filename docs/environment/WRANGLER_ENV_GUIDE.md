# 🛠️ Wrangler Environment Variables Management Guide

Panduan lengkap untuk mengelola environment variables menggunakan Wrangler CLI untuk frontend Astro Works E-commerce.

## 📋 Overview

Wrangler adalah CLI tool dari <PERSON>flare yang memungkinkan Anda mengelola environment variables untuk Cloudflare Workers dengan mudah. Proyek ini sudah dikonfigurasi dengan:

- ✅ Wrangler configuration (`wrangler.toml`)
- ✅ Environment-specific configurations (dev, staging, production)
- ✅ Automated sync scripts
- ✅ Validation tools
- ✅ Interactive management interface

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Validate Current Configuration
```bash
npm run env:validate
```

### 3. Sync Local .env to Wrangler
```bash
npm run env:sync
```

### 4. Interactive Management
```bash
npm run env:manage
```

## 📁 File Structure

```
fe_astro/
├── wrangler.toml              # Wrangler configuration
├── .env                       # Local environment variables
├── scripts/
│   ├── sync-env-vars.js       # Sync .env to wrangler.toml
│   ├── validate-env-vars.js   # Validate all environments
│   └── wrangler-env-manager.js # Interactive management
└── package.json               # NPM scripts
```

## 🔧 Available Commands

### NPM Scripts

| Command | Description |
|---------|-------------|
| `npm run env:validate` | Validate all environment configurations |
| `npm run env:sync` | Sync .env to development environment |
| `npm run env:sync:staging` | Sync .env to staging environment |
| `npm run env:sync:prod` | Sync .env to production environment |
| `npm run env:manage` | Interactive environment management |
| `npm run env:list` | List environment variables |
| `npm run env:put` | Add/update environment variable |
| `npm run env:delete` | Delete environment variable |

### Wrangler Commands

| Command | Description |
|---------|-------------|
| `npx wrangler dev` | Start local development server |
| `npx wrangler deploy` | Deploy to development |
| `npx wrangler deploy --env staging` | Deploy to staging |
| `npx wrangler deploy --env production` | Deploy to production |
| `npx wrangler secret list` | List secrets |
| `npx wrangler secret put VAR_NAME` | Set secret |
| `npx wrangler secret delete VAR_NAME` | Delete secret |

## 🌍 Environment Configurations

### Development Environment
- **Name**: `astro-works-frontend-dev`
- **API URL**: `http://localhost:8000/api/v1`
- **Debug**: Enabled
- **Features**: Basic features enabled

### Staging Environment
- **Name**: `astro-works-frontend-staging`
- **API URL**: `https://api-staging.astrokabinet.id/api/v1`
- **Debug**: Disabled
- **Features**: All features enabled for testing

### Production Environment
- **Name**: `astro-works-frontend-prod`
- **API URL**: `https://api.astrokabinet.id/api/v1`
- **Debug**: Disabled
- **Features**: All features enabled
- **Performance**: Optimized

## 📝 Environment Variables

### Required Variables
```env
VITE_API_URL=https://api.astrokabinet.id/api/v1
VITE_API_BASE_URL=https://api.astrokabinet.id
VITE_WHATSAPP_PHONE_NUMBER=***********
VITE_WHATSAPP_BASE_URL=https://wa.me/
VITE_COMPANY_NAME=Astro Works Indonesia
VITE_BANK_NAME=BCA
```

### Recommended Variables
```env
VITE_COMPANY_EMAIL=<EMAIL>
VITE_COMPANY_ADDRESS=Jakarta, Indonesia
VITE_BANK_ACCOUNT_NUMBER=**********
VITE_BANK_ACCOUNT_NAME=Astro Works Indonesia PT
VITE_APP_NAME=Astro Works
VITE_APP_VERSION=1.0.0
```

### Feature Flags
```env
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_CHAT=true
VITE_ENABLE_REVIEWS=true
VITE_ENABLE_WISHLIST=true
VITE_ENABLE_PWA=true
```

## 🔄 Workflow Examples

### 1. Development Workflow
```bash
# 1. Update local .env file
vim .env

# 2. Validate configuration
npm run env:validate

# 3. Sync to development environment
npm run env:sync

# 4. Test locally
npm run wrangler:dev

# 5. Deploy to development
npm run wrangler:deploy
```

### 2. Staging Deployment
```bash
# 1. Sync to staging environment
npm run env:sync:staging

# 2. Deploy to staging
npm run wrangler:deploy:staging

# 3. Test staging environment
curl https://astro-works-frontend-staging.your-subdomain.workers.dev
```

### 3. Production Deployment
```bash
# 1. Validate all environments
npm run env:validate

# 2. Sync to production environment
npm run env:sync:prod

# 3. Deploy to production
npm run wrangler:deploy:prod

# 4. Verify production deployment
curl https://astro-works-frontend-prod.your-subdomain.workers.dev
```

### 4. Interactive Management
```bash
# Start interactive manager
npm run env:manage

# Follow the menu:
# 1. List environment variables/secrets
# 2. Add/Update environment variable
# 3. Delete environment variable
# 4. Bulk import from .env file
# 5. Deploy with environment sync
# 6. Validate environment variables
```

## 🔐 Security Best Practices

### 1. Environment Separation
- ✅ Use different API endpoints for each environment
- ✅ Use different secrets for each environment
- ✅ Never use production secrets in development

### 2. Secret Management
- ✅ Use Wrangler secrets for sensitive data
- ✅ Keep .env file in .gitignore
- ✅ Rotate secrets regularly

### 3. Validation
- ✅ Always validate before deployment
- ✅ Check for development URLs in production
- ✅ Verify required variables are set

## 🛠️ Troubleshooting

### Common Issues

1. **Wrangler not authenticated**
   ```bash
   npx wrangler login
   ```

2. **Environment variables not updating**
   ```bash
   # Clear Wrangler cache
   rm -rf .wrangler
   npm run env:sync
   npm run wrangler:deploy
   ```

3. **Validation errors**
   ```bash
   # Check detailed validation
   npm run env:validate
   
   # Fix issues in .env file
   vim .env
   
   # Re-sync
   npm run env:sync
   ```

4. **Deployment failures**
   ```bash
   # Check Wrangler logs
   npx wrangler tail
   
   # Validate configuration
   npx wrangler dev --local
   ```

### Debug Commands
```bash
# Check Wrangler version
npx wrangler --version

# Check current configuration
npx wrangler whoami

# List all deployments
npx wrangler deployments list

# View logs
npx wrangler tail --format pretty
```

## 📚 Additional Resources

- [Cloudflare Workers Documentation](https://developers.cloudflare.com/workers/)
- [Wrangler CLI Documentation](https://developers.cloudflare.com/workers/wrangler/)
- [SvelteKit Cloudflare Adapter](https://kit.svelte.dev/docs/adapter-cloudflare)

## 🎯 Next Steps

1. **Setup Cloudflare Account**: Create account and get API tokens
2. **Configure Domains**: Set up custom domains for staging/production
3. **Setup CI/CD**: Integrate with GitHub Actions for automated deployments
4. **Monitor Performance**: Set up analytics and monitoring
5. **Optimize Caching**: Configure caching strategies for better performance

---

**Happy deploying with Wrangler! 🚀**
