# Furniture Image Download Scripts

This directory contains scripts to download furniture images for the Astro Works project with the correct naming convention.

## Files

- `download_furniture_images.py` - Python script (requires requests module)
- `download_furniture_images.sh` - Bash script (uses curl, no dependencies)

## Usage

### Option 1: <PERSON><PERSON>t (Recommended)

```bash
# Make executable
chmod +x download_furniture_images.sh

# Run the script
./download_furniture_images.sh
```

### Option 2: Python Script

```bash
# Install dependencies (if needed)
pip3 install requests

# Run the script
python3 download_furniture_images.py
```

## Customization

To use your own image URLs, edit the `URLS` array in the bash script or `PLACEHOLDER_URLS` dictionary in the Python script:

### Bash Script (`download_furniture_images.sh`)

```bash
declare -A URLS=(
    ["4x5_fantasy-tv-cabinet"]="YOUR_MOBILE_TV_CABINET_URL"
    ["4x5_led-gola"]="YOUR_MOBILE_LED_GOLA_URL"
    ["4x5_tandem-box"]="YOUR_MOBILE_TANDEM_BOX_URL"
    ["16x9_fantasy-tv-cabinet"]="YOUR_DESKTOP_TV_CABINET_URL"
    ["16x9_led-gola"]="YOUR_DESKTOP_LED_GOLA_URL"
    ["16x9_tandem-box"]="YOUR_DESKTOP_TANDEM_BOX_URL"
)
```

### Python Script (`download_furniture_images.py`)

```python
PLACEHOLDER_URLS = {
    "4x5_fantasy-tv-cabinet": "YOUR_MOBILE_TV_CABINET_URL",
    "4x5_led-gola": "YOUR_MOBILE_LED_GOLA_URL", 
    "4x5_tandem-box": "YOUR_MOBILE_TANDEM_BOX_URL",
    "16x9_fantasy-tv-cabinet": "YOUR_DESKTOP_TV_CABINET_URL",
    "16x9_led-gola": "YOUR_DESKTOP_LED_GOLA_URL",
    "16x9_tandem-box": "YOUR_DESKTOP_TANDEM_BOX_URL"
}
```

## Generated Files

The scripts will create the following files in `be_astro/static/uploads/products/`:

### 4:5 Aspect Ratio (Mobile)
- `4x5_fantasy-tv-cabinet_1.jpeg`
- `4x5_fantasy-tv-cabinet_1.jpg`
- `4x5_fantasy-tv-cabinet_1.png`
- `4x5_fantasy-tv-cabinet_1.webp`
- `4x5_led-gola_1.jpeg`
- `4x5_led-gola_1.jpg`
- `4x5_led-gola_1.png`
- `4x5_led-gola_1.webp`
- `4x5_tandem-box_1.jpeg`
- `4x5_tandem-box_1.jpg`
- `4x5_tandem-box_1.png`
- `4x5_tandem-box_1.webp`

### 16:9 Aspect Ratio (Desktop)
- `16x9_fantasy-tv-cabinet_1.jpeg`
- `16x9_fantasy-tv-cabinet_1.jpg`
- `16x9_fantasy-tv-cabinet_1.png`
- `16x9_fantasy-tv-cabinet_1.webp`
- `16x9_led-gola_1.jpeg`
- `16x9_led-gola_1.jpg`
- `16x9_led-gola_1.png`
- `16x9_led-gola_1.webp`
- `16x9_tandem-box_1.jpeg`
- `16x9_tandem-box_1.jpg`
- `16x9_tandem-box_1.png`
- `16x9_tandem-box_1.webp`

## Image Sources

The current URLs use Unsplash images:
- **Fantasy TV Cabinet**: Modern living room furniture
- **LED Gola**: Kitchen cabinet lighting systems
- **Tandem Box**: Drawer hardware and mechanisms

## Notes

- Each image is downloaded in 4 different formats (jpeg, jpg, png, webp)
- Mobile images are 400x500 pixels (4:5 aspect ratio)
- Desktop images are 800x450 pixels (16:9 aspect ratio)
- The script includes delays to avoid overwhelming image servers
- All images are saved with the exact naming convention required by the DynamicImage component

## Troubleshooting

If downloads fail:
1. Check your internet connection
2. Verify the image URLs are accessible
3. Ensure you have write permissions to the target directory
4. Try running the script again (some failures may be temporary)

## Integration

These images work with the `DynamicImage.svelte` component which automatically:
- Displays 4:5 images on mobile devices (< 768px width)
- Displays 16:9 images on desktop devices (≥ 768px width)
- Falls back to placeholder images if product images are not found
- Tries multiple file extensions (.jpg, .jpeg, .png, .webp) automatically
