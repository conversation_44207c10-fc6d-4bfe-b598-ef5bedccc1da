# 🖼️ Astro Works Image System Documentation

## Overview

The Astro Works project uses a sophisticated responsive image system that automatically serves the correct image format and aspect ratio based on the user's device and viewport size.

## 🏗️ Architecture

### DynamicImage Component

The core of the image system is the `DynamicImage.svelte` component located at `fe_astro/src/lib/components/DynamicImage.svelte`.

**Key Features:**
- **Responsive Image Loading**: Automatically serves 4:5 images on mobile (< 768px) and 16:9 images on desktop (≥ 768px)
- **Format Detection**: Tries multiple file formats (.jpg, .jpeg, .png, .webp) automatically
- **Graceful Fallback**: Shows placeholder image when product images are not found
- **Performance Optimized**: Uses HEAD requests to check image availability before loading

### Image Naming Convention

All product images follow a strict naming convention:

```
{aspect_ratio}_{product_slug}_{index}.{extension}
```

**Examples:**
- `4x5_fantasy-tv-cabinet_1.jpg` (Mobile version)
- `16x9_fantasy-tv-cabinet_1.jpg` (Desktop version)
- `4x5_led-gola_1.webp` (Mobile version, WebP format)
- `16x9_tandem-box_1.png` (Desktop version, PNG format)

### Directory Structure

```
be_astro/static/uploads/products/
├── 4x5_fantasy-tv-cabinet_1.{jpg,jpeg,png,webp}
├── 4x5_led-gola_1.{jpg,jpeg,png,webp}
├── 4x5_tandem-box_1.{jpg,jpeg,png,webp}
├── 16x9_fantasy-tv-cabinet_1.{jpg,jpeg,png,webp}
├── 16x9_led-gola_1.{jpg,jpeg,png,webp}
└── 16x9_tandem-box_1.{jpg,jpeg,png,webp}
```

## 🔧 Implementation

### 1. ProductCard Component

Updated to use `DynamicImage` instead of direct image URLs:

```svelte
<ProductCard
  id="fantasy-tv-cabinet"
  name="Fantasy TV Cabinet"
  slug="fantasy-tv-cabinet"  <!-- New: slug prop -->
  price={1590000}
  category="TV Cabinet"
/>
```

### 2. ProductImageGallery Component

Simplified to use `DynamicImage` for both main image and thumbnails:

```svelte
<ProductImageGallery 
  productSlug="fantasy-tv-cabinet"
  selectedTheme="Hitam"
  productName="Fantasy TV Cabinet"
  imageCount={1}
/>
```

### 3. Homepage Integration

Updated data loading to include slugs:

```typescript
interface ProductCard {
  id: string;
  name: string;
  category: string;
  slug?: string; // Generated from name if not provided
  price: number;
  shortDescription?: string;
  isActive: boolean;
}
```

## 📱 Responsive Behavior

### Mobile (< 768px width)
- Loads 4:5 aspect ratio images (400x500px)
- Optimized for portrait viewing
- Smaller file sizes for mobile data

### Desktop (≥ 768px width)
- Loads 16:9 aspect ratio images (800x450px)
- Optimized for landscape viewing
- Higher resolution for larger screens

## 🔄 Image Loading Process

1. **Slug Generation**: Product name → kebab-case slug
2. **Viewport Detection**: Check screen width
3. **Aspect Ratio Selection**: 4:5 for mobile, 16:9 for desktop
4. **Format Testing**: Try .jpg → .jpeg → .png → .webp
5. **Fallback**: Show placeholder if no images found

## 🛠️ Adding New Products

### Step 1: Prepare Images

Create images in both aspect ratios:
- **Mobile**: 400x500px (4:5 ratio)
- **Desktop**: 800x450px (16:9 ratio)

### Step 2: Generate Product Slug

```javascript
import { generateProductSlug } from '$lib/api.js';
const slug = generateProductSlug("Fantasy TV Cabinet"); // → "fantasy-tv-cabinet"
```

### Step 3: Save Images

Save with correct naming convention:
```
4x5_{slug}_1.jpg
16x9_{slug}_1.jpg
```

### Step 4: Use in Components

```svelte
<DynamicImage
  slug="fantasy-tv-cabinet"
  index={1}
  alt="Fantasy TV Cabinet"
  className="w-full h-full object-cover"
/>
```

## 📁 File Management Scripts

### Download Script

Use the provided scripts to download images:

```bash
# Bash script (recommended)
./download_furniture_images.sh

# Python script (alternative)
python3 download_furniture_images.py
```

### Customization

Edit the URLs in the scripts to use your own images:

```bash
# In download_furniture_images.sh
declare -A URLS=(
    ["4x5_fantasy-tv-cabinet"]="YOUR_MOBILE_IMAGE_URL"
    ["16x9_fantasy-tv-cabinet"]="YOUR_DESKTOP_IMAGE_URL"
    # ... more products
)
```

## 🎯 Current Products

The system currently supports these products with real images:

1. **Fantasy TV Cabinet** (`fantasy-tv-cabinet`)
2. **LED Gola** (`led-gola`)
3. **Tandem Box** (`tandem-box`)

## 🔍 Debugging

### Enable Debug Mode

```svelte
<DynamicImage
  slug="fantasy-tv-cabinet"
  index={1}
  alt="Fantasy TV Cabinet"
  debug={true}  <!-- Enable console logging -->
/>
```

### Check Browser Console

Debug mode will log:
- Image search attempts
- Successful/failed loads
- Fallback usage

### Verify Backend Serving

Test direct image access:
```
http://localhost:8000/static/uploads/products/4x5_fantasy-tv-cabinet_1.jpg
```

## 🚀 Performance Benefits

1. **Responsive Images**: Correct size for each device
2. **Format Optimization**: WebP for modern browsers, fallback for older ones
3. **Lazy Loading**: Images load only when needed
4. **Efficient Checking**: HEAD requests before full image load
5. **Graceful Degradation**: Always shows something, even if images fail

## 🔧 Troubleshooting

### Images Not Loading

1. **Check Backend**: Ensure backend runs from `be_astro/` directory
2. **Verify Files**: Confirm images exist in `be_astro/static/uploads/products/`
3. **Test Direct Access**: Try accessing images directly via URL
4. **Check Naming**: Ensure exact naming convention is followed
5. **Clear Cache**: Browser cache might show old 404 responses

### Wrong Aspect Ratio

1. **Check Viewport**: Resize browser to test responsive behavior
2. **Verify Breakpoint**: 768px is the mobile/desktop threshold
3. **Test Both Sizes**: Ensure both 4:5 and 16:9 images exist

## 📈 Future Enhancements

- **Multiple Images**: Support for image galleries (index 2, 3, etc.)
- **Theme-Specific Images**: Different images per color theme
- **Image Optimization**: Automatic WebP conversion
- **CDN Integration**: External image hosting
- **Progressive Loading**: Blur-up effect while loading
