# Domain Setup Guide - astrokabinet.id

## Overview
This guide explains how to configure custom domains for the Astro Works e-commerce platform using `astrokabinet.id` and `api.astrokabinet.id`.

## Architecture
- **Frontend**: `astrokabinet.id` (Cloudflare Workers)
- **API Backend**: `api.astrokabinet.id` (VPS with Traefik)
- **Admin Panel**: `astrokabinet.id/manajemen` (Svelte frontend)

## Current Status
✅ Frontend deployed to Cloudflare Workers: `https://astro-works-frontend-prod.omowahyu24.workers.dev`
✅ Environment variables configured for `api.astrokabinet.id`
⏳ Custom domain setup pending DNS configuration

## Frontend Configuration (Cloudflare Workers)

### 1. Wrangler Configuration
The frontend is configured with environment variables pointing to the custom API domain:

```toml
[env.production.vars]
VITE_API_BASE_URL = "https://api.astrokabinet.id"
VITE_API_URL = "https://api.astrokabinet.id/api/v1"
VITE_STATIC_URL = "https://api.astrokabinet.id/static"
VITE_UPLOADS_URL = "https://api.astrokabinet.id/static/uploads"
VITE_IMAGES_URL = "https://api.astrokabinet.id/static/images"
```

### 2. Custom Domain Setup Steps

#### Step 1: Add Custom Domain in Cloudflare Dashboard
1. Go to Cloudflare Dashboard → Workers & Pages
2. Select `astro-works-frontend-prod`
3. Go to Settings → Triggers
4. Add Custom Domain: `astrokabinet.id`
5. Add Custom Domain: `www.astrokabinet.id`

#### Step 2: DNS Configuration
Configure these DNS records in your domain registrar:

**For astrokabinet.id:**
```
Type: CNAME
Name: @
Value: astro-works-frontend-prod.omowahyu24.workers.dev
```

**For www.astrokabinet.id:**
```
Type: CNAME
Name: www
Value: astro-works-frontend-prod.omowahyu24.workers.dev
```

**For api.astrokabinet.id:**
```
Type: A
Name: api
Value: ************* (VPS IP)
```

## Backend Configuration (VPS)

### 1. Update Docker Compose
The backend is configured to use the custom domain in `docker-compose.prod.yml`:

```yaml
labels:
  - "traefik.http.routers.backend.rule=Host(`api.astrokabinet.id`)"
  - "traefik.http.routers.backend.entrypoints=websecure"
  - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
```

### 2. Environment Variables
Update `.env.vps` with:
```bash
API_DOMAIN=api.astrokabinet.id
DOMAIN=astrokabinet.id
CORS_ORIGIN=https://astrokabinet.id,https://www.astrokabinet.id
```

## SSL/TLS Configuration

### Frontend (Cloudflare)
- Automatic SSL through Cloudflare
- No additional configuration needed

### Backend (Traefik + Let's Encrypt)
- Automatic SSL certificate generation
- Configured in docker-compose.prod.yml

## Deployment Commands

### Frontend Deployment
```bash
# Deploy to Cloudflare Workers
cd fe_astro
npm run build
npx wrangler deploy --env production

# Or use Makefile
make deploy-production
```

### Backend Deployment
```bash
# Deploy to VPS
make fast-deploy-vps

# Or traditional deployment
make deploy-vps
```

## Verification Steps

### 1. Check Frontend Deployment
```bash
curl -I https://astro-works-frontend-prod.omowahyu24.workers.dev
```

### 2. Check API Endpoints (once backend is deployed)
```bash
curl -I https://api.astrokabinet.id/health
curl -I https://api.astrokabinet.id/api/v1/categories
```

### 3. Check Custom Domain (after DNS propagation)
```bash
curl -I https://astrokabinet.id
curl -I https://www.astrokabinet.id
```

## Troubleshooting

### DNS Propagation
- DNS changes can take 24-48 hours to propagate globally
- Use `dig` or online DNS checkers to verify propagation
- Test from different locations/networks

### SSL Certificate Issues
- Cloudflare: Check SSL/TLS settings in dashboard
- Traefik: Check container logs for Let's Encrypt errors

### CORS Issues
- Ensure `CORS_ORIGIN` includes all frontend domains
- Check browser developer tools for CORS errors

## Monitoring

### Frontend
- Cloudflare Analytics in dashboard
- Worker logs and metrics

### Backend
- Traefik dashboard: `https://traefik.astrokabinet.id`
- Application logs: `docker logs astro_backend_prod`

## Security Considerations

1. **HTTPS Only**: All traffic should use HTTPS
2. **CORS Configuration**: Restrict to known domains
3. **Rate Limiting**: Configured in Traefik
4. **Security Headers**: Applied via Traefik middleware

## Next Steps

1. ✅ Configure DNS records for custom domains
2. ✅ Deploy backend to VPS with domain configuration
3. ✅ Test end-to-end functionality
4. ✅ Monitor SSL certificate generation
5. ✅ Update any hardcoded URLs in the application

## Support

For issues with:
- **Cloudflare Workers**: Check Cloudflare documentation
- **Domain DNS**: Contact domain registrar
- **VPS/Backend**: Check server logs and Traefik configuration
