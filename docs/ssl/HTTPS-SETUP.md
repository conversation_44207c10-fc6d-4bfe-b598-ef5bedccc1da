# Local HTTPS Development Setup

This guide explains how to set up local HTTPS development for Astro Cabinet project using custom domains and SSL certificates.

## 🌐 Available Domains

After setup, you can access the application using these domains:

### Primary Domains (.local)
- **Frontend**: https://astrokabinet.id
- **API**: https://api.astrokabinet.id  
- **Traefik Dashboard**: https://traefik.astrokabinet.id

### Alternative Domains (.dev)
- **Frontend**: https://astrokabinet.dev
- **API**: https://api.astrokabinet.dev
- **Traefik Dashboard**: https://traefik.astrokabinet.dev

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)
```bash
# Complete setup with one command (requires sudo)
sudo ./setup-local-https.sh
```

### Option 2: Manual Setup
```bash
# 1. Setup domains (requires sudo)
sudo ./setup-domains.sh

# 2. Generate SSL certificates
./generate-ssl.sh

# 3. Start services with <PERSON>raefik
make start-traefik
```

### Option 3: Using Makefile
```bash
# Complete HTTPS setup
make setup-https

# Or step by step
make setup-domains
make generate-ssl
make start-traefik
```

## 🔒 SSL Certificate Setup

### Browser Security Warning
When you first visit the HTTPS domains, your browser will show a security warning because we're using self-signed certificates.

**To proceed:**
1. Click "Advanced" or "Show details"
2. Click "Proceed to astrokabinet.id (unsafe)" or similar
3. Accept the certificate for each domain

### Trust the Root CA (Optional)
For a better experience without security warnings:

**Chrome/Edge:**
1. Go to Settings → Privacy and Security → Security → Manage Certificates
2. Go to "Trusted Root Certification Authorities" tab
3. Click "Import" and select `ssl/certs/ca.crt`
4. Restart browser

**Firefox:**
1. Go to Settings → Privacy & Security → Certificates → View Certificates
2. Go to "Authorities" tab
3. Click "Import" and select `ssl/certs/ca.crt`
4. Check "Trust this CA to identify websites"
5. Restart browser

## 📁 File Structure

```
project/
├── ssl/
│   ├── certs/
│   │   ├── ca.crt          # Root CA certificate
│   │   └── server.crt      # Server certificate
│   └── private/
│       ├── ca.key          # Root CA private key
│       └── server.key      # Server private key
├── traefik/
│   ├── certs/              # Certificates for Traefik
│   └── dynamic/
│       ├── cors.yml        # CORS configuration
│       └── tls.yml         # TLS configuration
├── docker-compose.traefik.yml
├── setup-local-https.sh
├── setup-domains.sh
└── generate-ssl.sh
```

## 🛠️ Management Commands

```bash
# Start services
make start-traefik
# or
docker-compose -f docker-compose.traefik.yml up -d

# Stop services
make stop-traefik
# or
docker-compose -f docker-compose.traefik.yml down

# View logs
docker-compose -f docker-compose.traefik.yml logs -f

# Restart services
docker-compose -f docker-compose.traefik.yml restart

# Regenerate SSL certificates
./generate-ssl.sh
```

## 🔧 Troubleshooting

### Domain Not Resolving
- Check if domains are in `/etc/hosts`: `cat /etc/hosts | grep astrokabinet`
- Run setup again: `sudo ./setup-domains.sh`

### SSL Certificate Issues
- Regenerate certificates: `./generate-ssl.sh`
- Clear browser cache and restart browser
- Check certificate files exist in `ssl/certs/` and `traefik/certs/`

### CORS Issues
- Check backend logs: `docker-compose -f docker-compose.traefik.yml logs astro-backend`
- Verify CORS origins in `traefik/dynamic/cors.yml`

### Services Not Starting
- Check Docker is running: `docker ps`
- Check ports are not in use: `netstat -tulpn | grep :80`
- View detailed logs: `docker-compose -f docker-compose.traefik.yml logs`

## 🔄 Switching Between Development Modes

### Local Development (HTTP)
```bash
# Frontend: http://localhost:5174
# Backend: http://localhost:8000
cd fe_astro && npm run dev
cd be_astro && cargo run
```

### Local HTTPS with Domains
```bash
# Frontend: https://astrokabinet.id
# Backend: https://api.astrokabinet.id
make start-traefik
```

## 📝 Notes

- SSL certificates are valid for 365 days
- Certificates include both `.local` and `.dev` domains
- Self-signed certificates are for development only
- Root CA is generated for easier certificate management
- All certificates support SAN (Subject Alternative Names)

## 🔐 Security

⚠️ **Important**: These certificates are for local development only!
- Never use self-signed certificates in production
- Keep private keys secure
- Regenerate certificates periodically
- Don't commit private keys to version control
