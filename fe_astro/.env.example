# =============================================================================
# ASTRO WORKS E-COMMERCE - FRONTEND CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and update the values according to your environment
# Variables prefixed with PUBLIC_ are accessible in the browser (SvelteKit)
# Variables prefixed with VITE_ are for Vite development server

# =============================================================================
# API CONFIGURATION
# =============================================================================
PUBLIC_API_BASE_URL=http://localhost:7998
PUBLIC_API_VERSION=v1
PUBLIC_API_URL=http://localhost:7998/api/v1

# Development Configuration (Vite proxy)
VITE_DEV_API_TARGET=http://localhost:7998

# Static Assets Configuration
PUBLIC_STATIC_URL=http://localhost:7998/static
PUBLIC_UPLOADS_URL=http://localhost:7998/static/uploads
PUBLIC_IMAGES_URL=http://localhost:7998/static/images

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
PUBLIC_ENVIRONMENT=development
PUBLIC_APP_NAME=Astro Works
PUBLIC_APP_VERSION=1.0.0

# =============================================================================
# BUSINESS CONFIGURATION
# =============================================================================
# WhatsApp Configuration
PUBLIC_WHATSAPP_PHONE_NUMBER=***********
PUBLIC_WHATSAPP_BASE_URL=https://wa.me/

# Company Information
PUBLIC_COMPANY_NAME=Astro Works Indonesia
PUBLIC_COMPANY_EMAIL=<EMAIL>
PUBLIC_COMPANY_ADDRESS=Jakarta, Indonesia

# Bank Information (for display purposes)
PUBLIC_BANK_NAME=BCA
PUBLIC_BANK_ACCOUNT_NUMBER=**********
PUBLIC_BANK_ACCOUNT_NAME=Astro Works Indonesia PT

# =============================================================================
# UI/UX CONFIGURATION
# =============================================================================
# Image Configuration
PUBLIC_IMAGE_QUALITY=85
PUBLIC_IMAGE_LAZY_LOADING=true
PUBLIC_IMAGE_PLACEHOLDER=true

# Cart Configuration
PUBLIC_CART_STORAGE_KEY=astro_cart
PUBLIC_CART_EXPIRY_HOURS=24

# Pagination
PUBLIC_PRODUCTS_PER_PAGE=12
PUBLIC_CATEGORIES_PER_PAGE=20

# =============================================================================
# FEATURE FLAGS
# =============================================================================
PUBLIC_ENABLE_ANALYTICS=false
PUBLIC_ENABLE_CHAT=true
PUBLIC_ENABLE_REVIEWS=false
PUBLIC_ENABLE_WISHLIST=false

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
PUBLIC_DEBUG_MODE=true
PUBLIC_SHOW_CONSOLE_LOGS=true
PUBLIC_MOCK_API=false

# Performance
PUBLIC_ENABLE_PWA=false
PUBLIC_ENABLE_SERVICE_WORKER=false
