# Multi-stage build for production
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including devDependencies for build)
RUN npm ci --only=production --silent

# Copy source code
COPY . .

# Set environment variables for build
ENV NODE_ENV=production
ENV PUBLIC_API_URL=http://localhost:7998/api/v1
ENV PUBLIC_API_BASE_URL=http://localhost:7998

# Build the application
RUN npm run build

# Runtime stage - use nginx for better performance
FROM nginx:alpine

# Copy built application from builder stage
COPY --from=builder /app/build /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create non-root user
RUN addgroup -g 1001 -S astro \
    && adduser -S astro -u 1001 -G astro \
    && chown -R astro:astro /usr/share/nginx/html \
    && chown -R astro:astro /var/cache/nginx \
    && chown -R astro:astro /var/log/nginx \
    && chown -R astro:astro /etc/nginx/conf.d \
    && touch /var/run/nginx.pid \
    && chown -R astro:astro /var/run/nginx.pid

USER astro

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

CMD ["nginx", "-g", "daemon off;"]
