{"name": "fe-astro", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "wrangler:dev": "wrangler dev", "wrangler:deploy": "wrangler deploy", "wrangler:deploy:staging": "wrangler deploy --env staging", "wrangler:deploy:prod": "wrangler deploy --env production", "env:list": "wrangler secret list", "env:put": "wrangler secret put", "env:delete": "wrangler secret delete", "env:sync": "node scripts/sync-env-vars.cjs", "env:sync:staging": "node scripts/sync-env-vars.cjs staging", "env:sync:prod": "node scripts/sync-env-vars.cjs production", "env:validate": "node scripts/validate-env-vars.cjs", "env:manage": "node scripts/wrangler-env-manager.cjs", "env:import": "node scripts/wrangler-env-manager.cjs --bulk-import"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-cloudflare": "^7.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/node": "^24.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "wrangler": "^3.0.0"}}