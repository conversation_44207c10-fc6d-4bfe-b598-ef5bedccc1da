#!/bin/bash

# =============================================================================
# Custom Domain Setup Script for Cloudflare Workers
# =============================================================================
# This script helps setup custom domain for astrokabinet.id

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="astrokabinet.id"
WWW_DOMAIN="www.astrokabinet.id"
WORKER_NAME="astro-works-frontend-prod"

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if wrangler is installed
    if ! command -v wrangler &> /dev/null; then
        print_error "Wrangler is not installed"
        print_info "Install with: npm install -g wrangler"
        exit 1
    fi
    print_success "Wrangler is installed"
    
    # Check if logged in
    if ! wrangler whoami &> /dev/null; then
        print_error "Not logged in to Cloudflare"
        print_info "Login with: wrangler login"
        exit 1
    fi
    print_success "Logged in to Cloudflare"
}

# Show current domain configuration
show_current_config() {
    print_header "Current Configuration"
    
    print_info "Worker Name: $WORKER_NAME"
    print_info "Primary Domain: $DOMAIN"
    print_info "WWW Domain: $WWW_DOMAIN"
    
    echo ""
    print_info "Current routes:"
    wrangler routes list || print_warning "No routes found"
}

# Setup custom domain
setup_custom_domain() {
    print_header "Setting Up Custom Domain"
    
    print_info "Adding custom domain: $DOMAIN"
    if wrangler custom-domains add "$DOMAIN" --environment production; then
        print_success "Custom domain $DOMAIN added"
    else
        print_warning "Failed to add custom domain $DOMAIN (may already exist)"
    fi
    
    print_info "Adding WWW domain: $WWW_DOMAIN"
    if wrangler custom-domains add "$WWW_DOMAIN" --environment production; then
        print_success "Custom domain $WWW_DOMAIN added"
    else
        print_warning "Failed to add custom domain $WWW_DOMAIN (may already exist)"
    fi
}

# Deploy with custom domain
deploy_with_domain() {
    print_header "Deploying with Custom Domain"
    
    print_info "Building for production..."
    npm run build
    
    print_info "Deploying to production environment..."
    wrangler deploy --env production
    
    print_success "Deployment completed"
}

# Verify domain setup
verify_domain() {
    print_header "Verifying Domain Setup"
    
    print_info "Checking domain status..."
    wrangler custom-domains list
    
    echo ""
    print_info "Testing domain connectivity..."
    
    # Test main domain
    if curl -s --max-time 10 "https://$DOMAIN" > /dev/null; then
        print_success "$DOMAIN is accessible"
    else
        print_warning "$DOMAIN is not yet accessible (DNS propagation may take time)"
    fi
    
    # Test WWW domain
    if curl -s --max-time 10 "https://$WWW_DOMAIN" > /dev/null; then
        print_success "$WWW_DOMAIN is accessible"
    else
        print_warning "$WWW_DOMAIN is not yet accessible (DNS propagation may take time)"
    fi
}

# Show DNS configuration instructions
show_dns_instructions() {
    print_header "DNS Configuration Instructions"
    
    echo -e "${CYAN}To complete the setup, configure your DNS records:${NC}"
    echo ""
    echo -e "${YELLOW}1. Main Domain (astrokabinet.id):${NC}"
    echo "   Type: CNAME"
    echo "   Name: @"
    echo "   Value: [Cloudflare will provide this]"
    echo ""
    echo -e "${YELLOW}2. WWW Domain (www.astrokabinet.id):${NC}"
    echo "   Type: CNAME"
    echo "   Name: www"
    echo "   Value: [Cloudflare will provide this]"
    echo ""
    echo -e "${CYAN}Get the exact CNAME values from:${NC}"
    echo "wrangler custom-domains list"
    echo ""
    echo -e "${CYAN}Or check Cloudflare Dashboard:${NC}"
    echo "https://dash.cloudflare.com/"
}

# Show environment configuration
show_environment_config() {
    print_header "Environment Configuration"
    
    echo -e "${CYAN}Current environment variables for production:${NC}"
    echo ""
    echo "VITE_API_BASE_URL = https://api.astrokabinet.id"
    echo "VITE_API_URL = https://api.astrokabinet.id/api/v1"
    echo "VITE_STATIC_URL = https://api.astrokabinet.id/static"
    echo "VITE_UPLOADS_URL = https://api.astrokabinet.id/static/uploads"
    echo "VITE_IMAGES_URL = https://api.astrokabinet.id/static/images"
    echo ""
    echo -e "${CYAN}Frontend will be available at:${NC}"
    echo "- https://astrokabinet.id"
    echo "- https://www.astrokabinet.id"
    echo ""
    echo -e "${CYAN}API should be available at:${NC}"
    echo "- https://api.astrokabinet.id"
}

# Remove custom domain (cleanup)
remove_custom_domain() {
    print_header "Removing Custom Domain"
    
    print_warning "This will remove the custom domain configuration"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Removing custom domain: $DOMAIN"
        wrangler custom-domains remove "$DOMAIN" --environment production || true
        
        print_info "Removing WWW domain: $WWW_DOMAIN"
        wrangler custom-domains remove "$WWW_DOMAIN" --environment production || true
        
        print_success "Custom domains removed"
    else
        print_info "Operation cancelled"
    fi
}

# Show help
show_help() {
    echo "Custom Domain Setup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Setup custom domain (default)"
    echo "  deploy    - Deploy with custom domain"
    echo "  verify    - Verify domain setup"
    echo "  dns       - Show DNS configuration instructions"
    echo "  config    - Show environment configuration"
    echo "  remove    - Remove custom domain"
    echo "  help      - Show this help"
}

# Main function
main() {
    echo -e "${BLUE}🌐 Custom Domain Setup for Astro Works${NC}"
    echo -e "${BLUE}=====================================${NC}"
    echo ""
    echo -e "${CYAN}Domain: ${YELLOW}$DOMAIN${NC}"
    echo -e "${CYAN}WWW Domain: ${YELLOW}$WWW_DOMAIN${NC}"
    echo ""
    
    check_prerequisites
    
    case "${1:-setup}" in
        "setup")
            show_current_config
            setup_custom_domain
            show_dns_instructions
            show_environment_config
            ;;
        "deploy")
            deploy_with_domain
            verify_domain
            ;;
        "verify")
            verify_domain
            ;;
        "dns")
            show_dns_instructions
            ;;
        "config")
            show_environment_config
            ;;
        "remove")
            remove_custom_domain
            ;;
        "help")
            show_help
            ;;
        *)
            echo "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
