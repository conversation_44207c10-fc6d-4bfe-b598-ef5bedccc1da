#!/usr/bin/env node

/**
 * Environment Variables Sync Script
 * Syncs environment variables from .env file to Wrangler configuration
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function parseEnvFile(filePath) {
    if (!fs.existsSync(filePath)) {
        log(`❌ Environment file not found: ${filePath}`, 'red');
        return {};
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const vars = {};
    
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#') && line.includes('=')) {
            const [key, ...valueParts] = line.split('=');
            const value = valueParts.join('=').replace(/^["']|["']$/g, '');
            if (key.startsWith('VITE_')) {
                vars[key] = value;
            }
        }
    });

    return vars;
}

function updateWranglerConfig(envVars, environment = 'development') {
    const wranglerPath = path.join(process.cwd(), 'wrangler.toml');
    
    if (!fs.existsSync(wranglerPath)) {
        log(`❌ wrangler.toml not found at ${wranglerPath}`, 'red');
        return false;
    }

    let content = fs.readFileSync(wranglerPath, 'utf8');
    
    // Find the environment section
    const envSectionRegex = new RegExp(`\\[env\\.${environment}\\.vars\\]([\\s\\S]*?)(?=\\[|$)`, 'g');
    const match = envSectionRegex.exec(content);
    
    if (!match) {
        log(`❌ Environment section [env.${environment}.vars] not found in wrangler.toml`, 'red');
        return false;
    }

    // Build new vars section
    let newVarsSection = `[env.${environment}.vars]\n`;
    Object.entries(envVars).forEach(([key, value]) => {
        newVarsSection += `${key} = "${value}"\n`;
    });

    // Replace the section
    content = content.replace(envSectionRegex, newVarsSection);
    
    fs.writeFileSync(wranglerPath, content);
    return true;
}

function syncEnvironmentVariables() {
    log('🔄 Syncing Environment Variables to Wrangler Configuration', 'cyan');
    log('=' .repeat(60), 'cyan');

    const envFile = path.join(process.cwd(), '.env');
    const envVars = parseEnvFile(envFile);

    if (Object.keys(envVars).length === 0) {
        log('⚠️  No VITE_ environment variables found in .env file', 'yellow');
        return;
    }

    log(`📋 Found ${Object.keys(envVars).length} VITE_ environment variables:`, 'blue');
    Object.keys(envVars).forEach(key => {
        log(`   • ${key}`, 'blue');
    });

    // Sync to development environment by default
    const environment = process.argv[2] || 'development';
    log(`\n🎯 Syncing to environment: ${environment}`, 'yellow');

    if (updateWranglerConfig(envVars, environment)) {
        log(`✅ Successfully synced environment variables to wrangler.toml`, 'green');
        log(`\n💡 To deploy with these variables, run:`, 'cyan');
        log(`   npm run wrangler:deploy${environment !== 'development' ? ':' + environment : ''}`, 'cyan');
    } else {
        log(`❌ Failed to sync environment variables`, 'red');
        process.exit(1);
    }
}

function showHelp() {
    log('Environment Variables Sync Tool', 'cyan');
    log('=' .repeat(40), 'cyan');
    log('');
    log('Usage:', 'yellow');
    log('  node scripts/sync-env-vars.js [environment]', 'blue');
    log('');
    log('Environments:', 'yellow');
    log('  development (default)', 'blue');
    log('  staging', 'blue');
    log('  production', 'blue');
    log('');
    log('Examples:', 'yellow');
    log('  node scripts/sync-env-vars.js', 'blue');
    log('  node scripts/sync-env-vars.js staging', 'blue');
    log('  node scripts/sync-env-vars.js production', 'blue');
    log('');
    log('NPM Scripts:', 'yellow');
    log('  npm run env:sync', 'blue');
    log('  npm run env:sync staging', 'blue');
    log('  npm run env:sync production', 'blue');
}

// Main execution
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
} else {
    syncEnvironmentVariables();
}
