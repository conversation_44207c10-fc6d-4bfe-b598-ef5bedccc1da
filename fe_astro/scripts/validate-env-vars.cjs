#!/usr/bin/env node

/**
 * Environment Variables Validation Script
 * Validates environment variables across different environments
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// Required environment variables
const requiredVars = [
    'VITE_API_URL',
    'VITE_API_BASE_URL',
    'VITE_WHATSAPP_PHONE_NUMBER',
    'VITE_WHATSAPP_BASE_URL',
    'VITE_COMPANY_NAME',
    'VITE_BANK_NAME'
];

// Optional but recommended variables
const recommendedVars = [
    'VITE_COMPANY_EMAIL',
    'VITE_COMPANY_ADDRESS',
    'VITE_BANK_ACCOUNT_NUMBER',
    'VITE_BANK_ACCOUNT_NAME',
    'VITE_APP_NAME',
    'VITE_APP_VERSION'
];

function parseEnvFile(filePath) {
    if (!fs.existsSync(filePath)) {
        return null;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const vars = {};
    
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#') && line.includes('=')) {
            const [key, ...valueParts] = line.split('=');
            const value = valueParts.join('=').replace(/^["']|["']$/g, '');
            if (key.startsWith('VITE_')) {
                vars[key] = value;
            }
        }
    });

    return vars;
}

function parseWranglerConfig() {
    const wranglerPath = path.join(process.cwd(), 'wrangler.toml');
    
    if (!fs.existsSync(wranglerPath)) {
        return null;
    }

    const content = fs.readFileSync(wranglerPath, 'utf8');
    const environments = {};
    
    // Parse environment sections
    const envRegex = /\[env\.(\w+)\.vars\]([\s\S]*?)(?=\[|$)/g;
    let match;
    
    while ((match = envRegex.exec(content)) !== null) {
        const envName = match[1];
        const varsSection = match[2];
        const vars = {};
        
        varsSection.split('\n').forEach(line => {
            line = line.trim();
            if (line && line.includes('=')) {
                const [key, ...valueParts] = line.split('=');
                const value = valueParts.join('=').replace(/^["']|["']$/g, '').trim();
                if (key.trim().startsWith('VITE_')) {
                    vars[key.trim()] = value;
                }
            }
        });
        
        environments[envName] = vars;
    }
    
    return environments;
}

function validateEnvironment(envName, envVars) {
    log(`\n📋 Validating ${envName} environment:`, 'blue');
    
    let passed = 0;
    let warnings = 0;
    let errors = 0;

    // Check required variables
    log('\n🔍 Required Variables:', 'yellow');
    requiredVars.forEach(varName => {
        if (envVars && envVars[varName] && envVars[varName].trim() !== '') {
            log(`  ✅ ${varName}`, 'green');
            passed++;
        } else {
            log(`  ❌ ${varName} - Missing or empty`, 'red');
            errors++;
        }
    });

    // Check recommended variables
    log('\n💡 Recommended Variables:', 'yellow');
    recommendedVars.forEach(varName => {
        if (envVars && envVars[varName] && envVars[varName].trim() !== '') {
            log(`  ✅ ${varName}`, 'green');
            passed++;
        } else {
            log(`  ⚠️  ${varName} - Missing or empty`, 'yellow');
            warnings++;
        }
    });

    // Validate specific formats
    log('\n🔍 Format Validation:', 'yellow');
    
    if (envVars) {
        // Validate API URL format
        if (envVars['VITE_API_URL']) {
            if (envVars['VITE_API_URL'].match(/^https?:\/\/.+\/api\/v\d+$/)) {
                log(`  ✅ VITE_API_URL format is valid`, 'green');
                passed++;
            } else {
                log(`  ⚠️  VITE_API_URL format may be incorrect (expected: http(s)://domain/api/v1)`, 'yellow');
                warnings++;
            }
        }

        // Validate WhatsApp phone number
        if (envVars['VITE_WHATSAPP_PHONE_NUMBER']) {
            if (envVars['VITE_WHATSAPP_PHONE_NUMBER'].match(/^\d{10,15}$/)) {
                log(`  ✅ VITE_WHATSAPP_PHONE_NUMBER format is valid`, 'green');
                passed++;
            } else {
                log(`  ⚠️  VITE_WHATSAPP_PHONE_NUMBER format may be incorrect (expected: digits only, 10-15 chars)`, 'yellow');
                warnings++;
            }
        }

        // Check for development URLs in production
        if (envName === 'production') {
            const devPatterns = ['localhost', '127.0.0.1', '.test', '.local'];
            Object.entries(envVars).forEach(([key, value]) => {
                if (key.includes('URL') && devPatterns.some(pattern => value.includes(pattern))) {
                    log(`  ⚠️  ${key} contains development URL in production environment`, 'yellow');
                    warnings++;
                }
            });
        }
    }

    return { passed, warnings, errors };
}

function validateAllEnvironments() {
    log('🔍 Environment Variables Validation', 'cyan');
    log('=' .repeat(50), 'cyan');

    let totalPassed = 0;
    let totalWarnings = 0;
    let totalErrors = 0;

    // Validate .env file
    log('\n📁 Checking .env file:', 'blue');
    const envFile = path.join(process.cwd(), '.env');
    const envVars = parseEnvFile(envFile);
    
    if (envVars) {
        log(`  ✅ Found .env file with ${Object.keys(envVars).length} VITE_ variables`, 'green');
        const result = validateEnvironment('Local (.env)', envVars);
        totalPassed += result.passed;
        totalWarnings += result.warnings;
        totalErrors += result.errors;
    } else {
        log(`  ❌ .env file not found or has no VITE_ variables`, 'red');
        totalErrors++;
    }

    // Validate Wrangler environments
    log('\n📁 Checking wrangler.toml:', 'blue');
    const wranglerEnvs = parseWranglerConfig();
    
    if (wranglerEnvs) {
        log(`  ✅ Found wrangler.toml with ${Object.keys(wranglerEnvs).length} environments`, 'green');
        
        Object.entries(wranglerEnvs).forEach(([envName, envVars]) => {
            const result = validateEnvironment(`Wrangler (${envName})`, envVars);
            totalPassed += result.passed;
            totalWarnings += result.warnings;
            totalErrors += result.errors;
        });
    } else {
        log(`  ❌ wrangler.toml not found or has no environment configurations`, 'red');
        totalErrors++;
    }

    // Summary
    log('\n📊 Validation Summary:', 'cyan');
    log('=' .repeat(30), 'cyan');
    log(`✅ Passed: ${totalPassed}`, 'green');
    log(`⚠️  Warnings: ${totalWarnings}`, 'yellow');
    log(`❌ Errors: ${totalErrors}`, 'red');

    if (totalErrors > 0) {
        log('\n❌ Validation failed. Please fix the errors above.', 'red');
        process.exit(1);
    } else if (totalWarnings > 0) {
        log('\n⚠️  Validation completed with warnings. Consider addressing them.', 'yellow');
        process.exit(0);
    } else {
        log('\n✅ All validations passed successfully!', 'green');
        process.exit(0);
    }
}

function showHelp() {
    log('Environment Variables Validation Tool', 'cyan');
    log('=' .repeat(45), 'cyan');
    log('');
    log('This tool validates environment variables in:', 'blue');
    log('  • .env file (local development)', 'blue');
    log('  • wrangler.toml (Cloudflare environments)', 'blue');
    log('');
    log('Usage:', 'yellow');
    log('  node scripts/validate-env-vars.js', 'blue');
    log('  npm run env:validate', 'blue');
    log('');
    log('Validation includes:', 'yellow');
    log('  • Required variables presence', 'blue');
    log('  • Recommended variables', 'blue');
    log('  • Format validation (URLs, phone numbers)', 'blue');
    log('  • Environment-specific checks', 'blue');
}

// Main execution
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
} else {
    validateAllEnvironments();
}
