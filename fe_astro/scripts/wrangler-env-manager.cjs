#!/usr/bin/env node

/**
 * Wrangler Environment Manager
 * Advanced environment variable management for Cloudflare Workers
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, options = {}) {
    try {
        return execSync(command, { 
            encoding: 'utf8', 
            stdio: options.silent ? 'pipe' : 'inherit',
            ...options 
        });
    } catch (error) {
        if (!options.silent) {
            log(`❌ Command failed: ${command}`, 'red');
            log(error.message, 'red');
        }
        return null;
    }
}

function createInterface() {
    return readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
}

function askQuestion(question) {
    const rl = createInterface();
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            rl.close();
            resolve(answer.trim());
        });
    });
}

async function listSecrets(environment = '') {
    log('🔍 Listing Environment Variables/Secrets', 'cyan');
    log('=' .repeat(45), 'cyan');

    const envFlag = environment ? `--env ${environment}` : '';
    const command = `npx wrangler secret list ${envFlag}`;
    
    log(`\n📋 Running: ${command}`, 'blue');
    const result = execCommand(command);
    
    if (result) {
        log('\n✅ Successfully listed secrets', 'green');
    }
}

async function putSecret(environment = '') {
    log('📝 Adding/Updating Environment Variable', 'cyan');
    log('=' .repeat(40), 'cyan');

    const varName = await askQuestion('\n🔑 Enter variable name (e.g., VITE_API_KEY): ');
    if (!varName) {
        log('❌ Variable name is required', 'red');
        return;
    }

    const varValue = await askQuestion(`💡 Enter value for ${varName}: `);
    if (!varValue) {
        log('❌ Variable value is required', 'red');
        return;
    }

    const envFlag = environment ? `--env ${environment}` : '';
    const command = `echo "${varValue}" | npx wrangler secret put ${varName} ${envFlag}`;
    
    log(`\n📤 Setting variable: ${varName}`, 'blue');
    const result = execCommand(command);
    
    if (result) {
        log(`✅ Successfully set ${varName}`, 'green');
    }
}

async function deleteSecret(environment = '') {
    log('🗑️  Deleting Environment Variable', 'cyan');
    log('=' .repeat(35), 'cyan');

    const varName = await askQuestion('\n🔑 Enter variable name to delete: ');
    if (!varName) {
        log('❌ Variable name is required', 'red');
        return;
    }

    const confirm = await askQuestion(`⚠️  Are you sure you want to delete ${varName}? (y/N): `);
    if (confirm.toLowerCase() !== 'y') {
        log('❌ Deletion cancelled', 'yellow');
        return;
    }

    const envFlag = environment ? `--env ${environment}` : '';
    const command = `npx wrangler secret delete ${varName} ${envFlag}`;
    
    log(`\n🗑️  Deleting variable: ${varName}`, 'blue');
    const result = execCommand(command);
    
    if (result) {
        log(`✅ Successfully deleted ${varName}`, 'green');
    }
}

async function bulkImport(environment = '') {
    log('📦 Bulk Import Environment Variables', 'cyan');
    log('=' .repeat(40), 'cyan');

    const envFile = path.join(process.cwd(), '.env');
    if (!fs.existsSync(envFile)) {
        log('❌ .env file not found', 'red');
        return;
    }

    const content = fs.readFileSync(envFile, 'utf8');
    const vars = {};
    
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#') && line.includes('=')) {
            const [key, ...valueParts] = line.split('=');
            const value = valueParts.join('=').replace(/^["']|["']$/g, '');
            if (key.startsWith('VITE_')) {
                vars[key] = value;
            }
        }
    });

    if (Object.keys(vars).length === 0) {
        log('❌ No VITE_ variables found in .env file', 'red');
        return;
    }

    log(`\n📋 Found ${Object.keys(vars).length} VITE_ variables:`, 'blue');
    Object.keys(vars).forEach(key => {
        log(`   • ${key}`, 'blue');
    });

    const confirm = await askQuestion(`\n📤 Import all variables to ${environment || 'default'} environment? (y/N): `);
    if (confirm.toLowerCase() !== 'y') {
        log('❌ Import cancelled', 'yellow');
        return;
    }

    const envFlag = environment ? `--env ${environment}` : '';
    let successCount = 0;
    let errorCount = 0;

    for (const [key, value] of Object.entries(vars)) {
        log(`\n📤 Setting ${key}...`, 'blue');
        const command = `echo "${value}" | npx wrangler secret put ${key} ${envFlag}`;
        const result = execCommand(command, { silent: true });
        
        if (result !== null) {
            log(`  ✅ ${key}`, 'green');
            successCount++;
        } else {
            log(`  ❌ ${key}`, 'red');
            errorCount++;
        }
    }

    log(`\n📊 Import Summary:`, 'cyan');
    log(`✅ Success: ${successCount}`, 'green');
    log(`❌ Errors: ${errorCount}`, 'red');
}

async function deployWithEnv(environment = 'development') {
    log(`🚀 Deploy to ${environment} environment`, 'cyan');
    log('=' .repeat(40), 'cyan');

    // First sync local env vars to wrangler.toml
    log('\n🔄 Syncing local environment variables...', 'blue');
    const syncResult = execCommand(`node scripts/sync-env-vars.js ${environment}`);
    
    if (!syncResult) {
        log('❌ Failed to sync environment variables', 'red');
        return;
    }

    // Then deploy
    log(`\n🚀 Deploying to ${environment}...`, 'blue');
    const envFlag = environment === 'development' ? '' : `--env ${environment}`;
    const deployCommand = `npx wrangler deploy ${envFlag}`;
    
    const deployResult = execCommand(deployCommand);
    
    if (deployResult) {
        log(`\n✅ Successfully deployed to ${environment}!`, 'green');
        
        // Show deployment info
        if (environment === 'production') {
            log('\n🌐 Your app is now live at:', 'cyan');
            log('   https://astro-works-frontend-prod.your-subdomain.workers.dev', 'blue');
        } else if (environment === 'staging') {
            log('\n🌐 Your staging app is at:', 'cyan');
            log('   https://astro-works-frontend-staging.your-subdomain.workers.dev', 'blue');
        }
    }
}

async function showMenu() {
    log('\n🛠️  Wrangler Environment Manager', 'cyan');
    log('=' .repeat(35), 'cyan');
    log('');
    log('1. List environment variables/secrets', 'blue');
    log('2. Add/Update environment variable', 'blue');
    log('3. Delete environment variable', 'blue');
    log('4. Bulk import from .env file', 'blue');
    log('5. Deploy with environment sync', 'blue');
    log('6. Validate environment variables', 'blue');
    log('0. Exit', 'blue');
    log('');

    const choice = await askQuestion('Select an option (0-6): ');
    return choice;
}

async function selectEnvironment() {
    log('\n🌍 Select Environment:', 'yellow');
    log('1. Development (default)', 'blue');
    log('2. Staging', 'blue');
    log('3. Production', 'blue');
    log('');

    const choice = await askQuestion('Select environment (1-3): ');
    
    switch (choice) {
        case '2': return 'staging';
        case '3': return 'production';
        default: return 'development';
    }
}

async function main() {
    try {
        while (true) {
            const choice = await showMenu();
            
            switch (choice) {
                case '1':
                    const listEnv = await selectEnvironment();
                    await listSecrets(listEnv);
                    break;
                    
                case '2':
                    const putEnv = await selectEnvironment();
                    await putSecret(putEnv);
                    break;
                    
                case '3':
                    const deleteEnv = await selectEnvironment();
                    await deleteSecret(deleteEnv);
                    break;
                    
                case '4':
                    const importEnv = await selectEnvironment();
                    await bulkImport(importEnv);
                    break;
                    
                case '5':
                    const deployEnv = await selectEnvironment();
                    await deployWithEnv(deployEnv);
                    break;
                    
                case '6':
                    execCommand('node scripts/validate-env-vars.js');
                    break;
                    
                case '0':
                    log('\n👋 Goodbye!', 'cyan');
                    process.exit(0);
                    
                default:
                    log('❌ Invalid choice. Please try again.', 'red');
            }
            
            await askQuestion('\nPress Enter to continue...');
        }
    } catch (error) {
        log(`❌ Error: ${error.message}`, 'red');
        process.exit(1);
    }
}

// Command line arguments handling
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
    log('Wrangler Environment Manager', 'cyan');
    log('=' .repeat(30), 'cyan');
    log('');
    log('Interactive tool for managing Cloudflare Workers environment variables', 'blue');
    log('');
    log('Usage:', 'yellow');
    log('  node scripts/wrangler-env-manager.js', 'blue');
    log('');
    log('Features:', 'yellow');
    log('  • List environment variables', 'blue');
    log('  • Add/update variables', 'blue');
    log('  • Delete variables', 'blue');
    log('  • Bulk import from .env', 'blue');
    log('  • Deploy with environment sync', 'blue');
    log('  • Validate configurations', 'blue');
} else {
    main();
}
