/**
 * API helper functions for communicating with the backend
 */

import { config } from './config/env';

const API_BASE = config.api.url;
const STATIC_BASE = config.api.baseUrl;

interface ApiRequestOptions extends RequestInit {
	headers?: Record<string, string>;
}

/**
 * Generic API request function
 */
async function apiRequest(endpoint: string, options: ApiRequestOptions = {}): Promise<any> {
	const url = `${API_BASE}${endpoint}`;

	const requestConfig: RequestInit = {
		headers: {
			'Content-Type': 'application/json',
			...options.headers,
		},
		...options,
	};

	try {
		const response = await fetch(url, requestConfig);

		if (!response.ok) {
			throw new Error(`API Error: ${response.status} ${response.statusText}`);
		}

		// Handle empty responses (like DELETE requests)
		if (response.status === 204) {
			return null;
		}

		// Check if response has content
		const contentType = response.headers.get('content-type');
		if (contentType && contentType.includes('application/json')) {
			return await response.json();
		} else {
			// For responses without JSON content, return null or empty object
			return null;
		}
	} catch (error) {
		console.error('API Request failed:', error);
		throw error;
	}
}

// Product API functions
export const productApi = {
	/**
	 * Get all products
	 */
	async getAll(params: Record<string, string> = {}): Promise<any[]> {
		const searchParams = new URLSearchParams(params);
		const endpoint = `/products${searchParams.toString() ? `?${searchParams}` : ''}`;
		return apiRequest(endpoint);
	},

	/**
	 * Get a single product by ID
	 */
	async getById(id: string): Promise<any> {
		return apiRequest(`/products/${id}`);
	},

	/**
	 * Create a new product
	 */
	async create(productData: any): Promise<any> {
		return apiRequest('/products', {
			method: 'POST',
			body: JSON.stringify(productData),
		});
	},

	/**
	 * Update an existing product
	 */
	async update(id: string, productData: any): Promise<any> {
		return apiRequest(`/products/${id}`, {
			method: 'PATCH',
			body: JSON.stringify(productData),
		});
	},

	/**
	 * Delete a product
	 */
	async delete(id: string): Promise<null> {
		return apiRequest(`/products/${id}`, {
			method: 'DELETE',
		});
	},

	/**
	 * Get featured products
	 */
	async getFeatured(): Promise<any[]> {
		return apiRequest('/products/featured');
	},

	/**
	 * Search products
	 */
	async search(query: string, params: Record<string, string> = {}): Promise<any[]> {
		const searchParams = new URLSearchParams({ search: query, ...params });
		return apiRequest(`/products/search?${searchParams}`);
	},
};

// Category API functions
export const categoryApi = {
	/**
	 * Get all categories
	 */
	async getAll(): Promise<any[]> {
		return apiRequest('/categories');
	},

	/**
	 * Get a single category by ID
	 */
	async getById(id: string): Promise<any> {
		return apiRequest(`/categories/${id}`);
	},

	/**
	 * Create a new category
	 */
	async create(categoryData: any): Promise<any> {
		return apiRequest('/categories', {
			method: 'POST',
			body: JSON.stringify(categoryData),
		});
	},

	/**
	 * Update an existing category
	 */
	async update(id: string, categoryData: any): Promise<any> {
		return apiRequest(`/categories/${id}`, {
			method: 'PATCH',
			body: JSON.stringify(categoryData),
		});
	},

	/**
	 * Delete a category
	 */
	async delete(id: string): Promise<null> {
		return apiRequest(`/categories/${id}`, {
			method: 'DELETE',
		});
	},
};

// Order API functions
export const orderApi = {
	/**
	 * Get all orders
	 */
	async getAll(params: Record<string, string> = {}): Promise<any[]> {
		const searchParams = new URLSearchParams(params);
		const endpoint = `/orders${searchParams.toString() ? `?${searchParams}` : ''}`;
		return apiRequest(endpoint);
	},

	/**
	 * Get a single order by ID
	 */
	async getById(id: string): Promise<any> {
		return apiRequest(`/orders/${id}`);
	},

	/**
	 * Create a new order
	 */
	async create(orderData: any): Promise<any> {
		return apiRequest('/checkout/orders', {
			method: 'POST',
			body: JSON.stringify(orderData),
		});
	},

	/**
	 * Update an existing order
	 */
	async update(id: string, orderData: any): Promise<any> {
		return apiRequest(`/orders/${id}`, {
			method: 'PATCH',
			body: JSON.stringify(orderData),
		});
	},
};

/**
 * Upload multiple product images with enhanced optimization
 */
export async function uploadProductImages(
	productSlug: string,
	imageFiles: FileList | File[],
	startIndex: number = 1,
	onProgress?: (current: number, total: number) => void
): Promise<any> {
	const formData = new FormData();
	formData.append('product_slug', productSlug);
	formData.append('start_index', startIndex.toString());

	// Add all files to the form data
	Array.from(imageFiles).forEach(file => {
		formData.append('images', file);
	});

	const url = `${API_BASE}/uploads/products`;

	try {
		const response = await fetch(url, {
			method: 'POST',
			body: formData,
		});

		if (!response.ok) {
			throw new Error(`Upload Error: ${response.status} ${response.statusText}`);
		}

		const result = await response.json();

		// Call progress callback if provided
		if (onProgress && result.total_processed) {
			onProgress(result.total_processed, result.total_processed + result.total_failed);
		}

		return result;
	} catch (error) {
		console.error('Image upload failed:', error);
		throw error;
	}
}

/**
 * Upload a single product image (legacy - for backward compatibility)
 */
export async function uploadProductImage(aspect: string, productSlug: string, imageFile: File): Promise<any> {
	// Use the new batch upload API for single files
	const result = await uploadProductImages(productSlug, [imageFile], 1);

	// Return the first result for compatibility
	if (result.results && result.results.length > 0) {
		return result.results[0];
	}

	throw new Error('Upload failed');
}

/**
 * Upload multiple images for a product (legacy - for backward compatibility)
 */
export async function uploadMultipleProductImages(
	productSlug: string,
	imageFiles: FileList | File[],
	onProgress?: (current: number, total: number) => void
): Promise<any[]> {
	const result = await uploadProductImages(productSlug, imageFiles, 1, onProgress);
	return result.results || [];
}

/**
 * Get all images for a product
 */
export async function getProductImages(productSlug: string): Promise<any> {
	return apiRequest(`/uploads/products/${productSlug}`);
}

/**
 * Delete a product image
 */
export async function deleteProductImage(filename: string): Promise<any> {
	const url = `${API_BASE}/uploads/delete/${filename}`;

	try {
		const response = await fetch(url, {
			method: 'DELETE',
		});

		if (!response.ok) {
			throw new Error(`Delete Error: ${response.status} ${response.statusText}`);
		}

		return await response.json();
	} catch (error) {
		console.error('Image delete failed:', error);
		throw error;
	}
}

/**
 * Clean up legacy non-WebP images
 */
export async function cleanupLegacyImages(dryRun: boolean = true): Promise<any> {
	const url = `${API_BASE}/uploads/cleanup`;

	try {
		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ dry_run: dryRun }),
		});

		if (!response.ok) {
			throw new Error(`Cleanup Error: ${response.status} ${response.statusText}`);
		}

		return await response.json();
	} catch (error) {
		console.error('Image cleanup failed:', error);
		throw error;
	}
}

/**
 * Get upload statistics
 */
export async function getUploadStats(): Promise<any> {
	const url = `${API_BASE}/uploads/stats`;

	try {
		const response = await fetch(url);

		if (!response.ok) {
			throw new Error(`Stats Error: ${response.status} ${response.statusText}`);
		}

		return await response.json();
	} catch (error) {
		console.error('Failed to get upload stats:', error);
		throw error;
	}
}

/**
 * Generate product slug from name
 */
export function generateProductSlug(name: string): string {
	return name
		.toLowerCase()
		.replace(/[^a-z0-9\s-]/g, '') // Remove special characters
		.replace(/\s+/g, '-') // Replace spaces with hyphens
		.replace(/-+/g, '-') // Replace multiple hyphens with single
		.replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number): string {
	return new Intl.NumberFormat('id-ID', {
		style: 'currency',
		currency: 'IDR',
		minimumFractionDigits: 0,
		maximumFractionDigits: 0,
	}).format(amount);
}

/**
 * Format date for display
 */
export function formatDate(date: string | Date): string {
	return new Intl.DateTimeFormat('id-ID', {
		year: 'numeric',
		month: 'long',
		day: 'numeric',
		hour: '2-digit',
		minute: '2-digit',
	}).format(new Date(date));
}
