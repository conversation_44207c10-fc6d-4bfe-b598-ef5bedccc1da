import type {
	Product,
	ProductDimension,
	ProductTheme,
	CalculatePriceRequest,
	PriceCalculationResponse
} from '$lib/types/product';
import { config } from '$lib/config/env';

const API_BASE = config.api.url;

export class ProductAPI {
	static async getProduct(id: string): Promise<Product> {
		const response = await fetch(`${API_BASE}/products/${id}`);
		if (!response.ok) {
			throw new Error('Failed to fetch product');
		}
		return response.json();
	}

	static async getProductWithPricing(id: string): Promise<Product> {
		const response = await fetch(`${API_BASE}/products/${id}/pricing`);
		if (!response.ok) {
			throw new Error('Failed to fetch product with pricing');
		}
		return response.json();
	}

	static async calculatePrice(request: CalculatePriceRequest): Promise<PriceCalculationResponse> {
		const response = await fetch(`${API_BASE}/products/${request.product_id}/calculate-price`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(request),
		});
		
		if (!response.ok) {
			throw new Error('Failed to calculate price');
		}
		return response.json();
	}

	static async getProductDimensions(productId: string): Promise<ProductDimension[]> {
		const response = await fetch(`${API_BASE}/products/${productId}/dimensions`);
		if (!response.ok) {
			throw new Error('Failed to fetch product dimensions');
		}
		return response.json();
	}

	static async getProductThemes(productId: string): Promise<ProductTheme[]> {
		const response = await fetch(`${API_BASE}/products/${productId}/themes`);
		if (!response.ok) {
			throw new Error('Failed to fetch product themes');
		}
		return response.json();
	}

	static async getAvailableAccessories(): Promise<Product[]> {
		const response = await fetch(`${API_BASE}/products/accessories`);
		if (!response.ok) {
			throw new Error('Failed to fetch available accessories');
		}
		return response.json();
	}

	static async listProducts(params?: {
		category_id?: string;
		is_featured?: boolean;
		is_active?: boolean;
		search?: string;
		page?: number;
		per_page?: number;
	}): Promise<Product[]> {
		const searchParams = new URLSearchParams();
		
		if (params) {
			Object.entries(params).forEach(([key, value]) => {
				if (value !== undefined) {
					searchParams.append(key, value.toString());
				}
			});
		}

		const url = `${API_BASE}/products${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
		const response = await fetch(url);
		
		if (!response.ok) {
			throw new Error('Failed to fetch products');
		}
		return response.json();
	}

	static async getFeaturedProducts(): Promise<Product[]> {
		const response = await fetch(`${API_BASE}/products/featured`);
		if (!response.ok) {
			throw new Error('Failed to fetch featured products');
		}
		return response.json();
	}
}

export function formatPrice(price: number): string {
	return new Intl.NumberFormat('id-ID', {
		style: 'currency',
		currency: 'IDR',
		minimumFractionDigits: 0,
		maximumFractionDigits: 0,
	}).format(price);
}

export function formatPriceCompact(price: number): string {
	if (price >= 1000000) {
		return `Rp ${(price / 1000000).toFixed(1)}jt`;
	} else if (price >= 1000) {
		return `Rp ${(price / 1000).toFixed(0)}rb`;
	} else {
		return `Rp ${price}`;
	}
}
