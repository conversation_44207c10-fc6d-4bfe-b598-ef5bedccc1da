<script lang="ts">
	/**
	 * AccessoriesSelector Component
	 * Displays accessories with quantity controls and price calculations
	 *
	 * @component
	 * @example
	 * <AccessoriesSelector
	 *   accessories={productAccessories}
	 *   selectedAccessories={selectedAccessoriesMap}
	 *   onAccessoryChange={handleAccessoryChange}
	 * />
	 */

	import { config } from '$lib/config/env';
	import ImagePlaceholder from './ImagePlaceholder.svelte';

	// Accessory interface
	interface Accessory {
		id: string;
		name: string;
		description?: string;
		price: number;
		category: string;
		image?: string;
		maxQuantity?: number;
	}

	// Props interface
	interface Props {
		/** Array of available accessories */
		accessories: Accessory[];
		/** Map of selected accessories with quantities */
		selectedAccessories: Map<string, {accessory: Accessory, quantity: number}>;
		/** Callback function when accessory quantity changes */
		onAccessoryChange: (accessory: Accessory, quantity: number) => void;
	}

	let { accessories, selectedAccessories, onAccessoryChange }: Props = $props();

	// Component state
	let hoveredAccessory = $state<string | null>(null);
	
	/**
	 * Get current quantity for an accessory
	 * @param accessoryId - Accessory ID
	 * @returns Current quantity
	 */
	function getCurrentQuantity(accessoryId: string): number {
		return selectedAccessories.get(accessoryId)?.quantity || 0;
	}
	
	/**
	 * Handle quantity change for an accessory
	 * @param accessory - Accessory object
	 * @param newQuantity - New quantity value
	 */
	function handleQuantityChange(accessory: Accessory, newQuantity: number) {
		// Remove quantity restrictions - allow unlimited selection
		const validQuantity = Math.max(0, newQuantity);
		onAccessoryChange(accessory, validQuantity);
	}
	
	/**
	 * Increment accessory quantity
	 * @param accessory - Accessory object
	 */
	function incrementQuantity(accessory: Accessory) {
		const currentQty = getCurrentQuantity(accessory.id);
		handleQuantityChange(accessory, currentQty + 1);
	}
	
	/**
	 * Decrement accessory quantity
	 * @param accessory - Accessory object
	 */
	function decrementQuantity(accessory: Accessory) {
		const currentQty = getCurrentQuantity(accessory.id);
		handleQuantityChange(accessory, currentQty - 1);
	}
	
	/**
	 * Handle direct input change
	 * @param accessory - Accessory object
	 * @param event - Input event
	 */
	function handleInputChange(accessory: Accessory, event: Event) {
		const target = event.target as HTMLInputElement;
		const value = parseInt(target.value) || 0;
		handleQuantityChange(accessory, value);
	}
	
	/**
	 * Format price to Indonesian Rupiah
	 * @param price - Price in number format
	 * @returns Formatted price string
	 */
	function formatPrice(price: number): string {
		return new Intl.NumberFormat('id-ID').format(price);
	}
	
	/**
	 * Calculate subtotal for an accessory
	 * @param accessory - Accessory object
	 * @returns Subtotal amount
	 */
	function calculateSubtotal(accessory: Accessory): number {
		const quantity = getCurrentQuantity(accessory.id);
		return accessory.price * quantity;
	}
	
	/**
	 * Check if accessory is selected (quantity > 0)
	 * @param accessoryId - Accessory ID
	 * @returns True if selected
	 */
	function isSelected(accessoryId: string): boolean {
		return getCurrentQuantity(accessoryId) > 0;
	}
	
	/**
	 * Get accessory image URL from additional-items directory
	 * @param accessory - Accessory object
	 * @returns Image URL for the accessory
	 */
	function getAccessoryImageUrl(accessory: Accessory): string {
		if (accessory.image) {
			// If image URL is provided, use it
			return accessory.image;
		}

		// Generate image filename from accessory name
		const imageName = accessory.name.toLowerCase()
			.replace(/\s+/g, '-')
			.replace(/[^a-z0-9-]/g, '');

		// Try multiple image formats
		return `${config.api.imagesUrl}/additional-items/${imageName}.webp`;
	}

	/**
	 * Get multiple fallback images for accessories without images
	 * @returns Array of fallback image URLs in order of preference
	 */
	function getFallbackImages(): string[] {
		return [
			`${config.api.staticUrl}/placeholder-image.svg`,
			// Data URL fallback - simple gray rectangle with icon
			'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMDAgMTUwQzE4Ni4xOSAxNTAgMTc1IDE2MS4xOSAxNzUgMTc1QzE3NSAxODguODEgMTg2LjE5IDIwMCAyMDAgMjAwQzIxMy44MSAyMDAgMjI1IDE4OC44MSAyMjUgMTc1QzIyNSAxNjEuMTkgMjEzLjgxIDE1MCAyMDAgMTUwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTI1IDI1MEgzMDBWMjc1SDEyNVYyNTBaIiBmaWxsPSIjOUNBM0FGIi8+CjwvdXZnPgo=',
			// External placeholder as last resort
			'https://via.placeholder.com/400x400/f3f4f6/9ca3af?text=No+Image'
		];
	}

	/**
	 * Handle image error with multiple fallbacks
	 * @param event - Image error event
	 * @param accessoryName - Name of the accessory for logging
	 */
	function handleImageError(event: Event, accessoryName: string) {
		const img = event.target as HTMLImageElement;
		const fallbacks = getFallbackImages();

		// Get current fallback index from data attribute
		const currentIndex = parseInt(img.dataset.fallbackIndex || '0');
		const nextIndex = currentIndex + 1;

		if (nextIndex < fallbacks.length) {
			// Try next fallback
			img.dataset.fallbackIndex = nextIndex.toString();
			img.src = fallbacks[nextIndex];

			console.warn(`Image failed for ${accessoryName}, trying fallback ${nextIndex + 1}:`, fallbacks[nextIndex]);
		} else {
			// All fallbacks failed, show error state
			img.style.display = 'none';
			const container = img.parentElement;
			if (container) {
				container.innerHTML = `
					<div class="w-full h-full flex flex-col items-center justify-center bg-gray-100 text-gray-400">
						<svg class="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
						</svg>
						<span class="text-xs text-center">Gambar tidak tersedia</span>
					</div>
				`;
			}

			console.error(`All image fallbacks failed for ${accessoryName}`);
		}
	}
</script>

<!-- Accessories Selector Container -->
<div class="space-y-4">
	<!-- Accessories Grid -->
	<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
		{#each accessories as accessory (accessory.id)}
			{@const quantity = getCurrentQuantity(accessory.id)}
			{@const subtotal = calculateSubtotal(accessory)}
			{@const selected = isSelected(accessory.id)}
			{@const isHovered = hoveredAccessory === accessory.id}

			<!-- Accessory Card -->
			<div
				class="relative bg-white border-2 rounded-lg p-4 transition-all duration-200"
				class:border-blue-500={selected}
				class:bg-blue-50={selected}
				class:border-gray-200={!selected && !isHovered}
				class:border-gray-300={!selected && isHovered}
				class:shadow-md={selected}
				class:shadow-sm={!selected && isHovered}
				role="group"
				aria-label="Accessory selection"
				onmouseenter={() => hoveredAccessory = accessory.id}
				onmouseleave={() => hoveredAccessory = null}
			>
				<!-- Selected Badge -->
				{#if selected}
					<div class="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
						Added
					</div>
				{/if}

				<!-- Accessory Content Layout -->
				<div class="space-y-3">
					<!-- Accessory Image -->
					<div class="w-full">
						<div class="h-40 bg-gray-100 rounded-lg overflow-hidden">
							<img
								src={getAccessoryImageUrl(accessory)}
								alt={accessory.name}
								class="w-full h-full object-cover rounded"
								loading="lazy"
								data-fallback-index="0"
								onerror={(e) => handleImageError(e, accessory.name)}
							/>
						</div>
					</div>

					<!-- Accessory Details -->
					<div class="space-y-3">
						<!-- Name and Description -->
						<div class="space-y-1">
							<h4 class="text-lg font-semibold text-gray-900">
								{accessory.name}
							</h4>
							{#if accessory.description}
								<p class="text-sm text-gray-600 line-clamp-2">
									{accessory.description}
								</p>
							{/if}
						</div>

						<!-- Price -->
						<div>
							<div class="flex items-baseline space-x-1">
								<span class="text-sm text-gray-600">Rp</span>
								<span class="text-xl font-bold text-gray-900">
									{formatPrice(accessory.price)}
								</span>
							</div>
						</div>

						<!-- Quantity Controls -->
						<div class="flex items-center justify-between">
							<!-- Quantity Selector -->
							<div class="flex items-center space-x-2">
								<!-- Decrease Button -->
								<button
									class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
									onclick={() => decrementQuantity(accessory)}
									disabled={quantity <= 0}
									aria-label={`Decrease ${accessory.name} quantity`}
								>
									<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
									</svg>
								</button>

								<!-- Quantity Input -->
								<input
									type="number"
									min="0"
									value={quantity}
									class="w-16 text-center border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
									oninput={(e) => handleInputChange(accessory, e)}
									aria-label={`${accessory.name} quantity`}
								/>

								<!-- Increase Button -->
								<button
									class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
									onclick={() => incrementQuantity(accessory)}
									aria-label={`Increase ${accessory.name} quantity`}
								>
									<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
									</svg>
								</button>
							</div>

							<!-- Subtotal -->
							{#if quantity > 0}
								<div class="text-right">
									<div class="flex items-baseline space-x-1">
										<span class="text-xs text-blue-600">Rp</span>
										<span class="text-sm font-bold text-blue-700">
											{formatPrice(subtotal)}
										</span>
									</div>
									<div class="text-xs text-gray-500">
										{quantity} × {formatPrice(accessory.price)}
									</div>
								</div>
							{/if}
						</div>
					</div>
				</div>
			</div>
		{/each}
	</div>
	
	<!-- Selected Accessories Summary -->
	{#if selectedAccessories.size > 0}
		<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
			<h4 class="text-sm font-semibold text-blue-900 mb-3">Selected Accessories</h4>
			<div class="space-y-2">
				{#each Array.from(selectedAccessories.values()) as item}
					{#if item.quantity > 0}
						<div class="flex items-center justify-between text-sm">
							<div class="flex items-center space-x-2">
								<span class="w-2 h-2 bg-blue-500 rounded-full"></span>
								<span class="text-blue-900">{item.accessory.name}</span>
								<span class="text-blue-700">× {item.quantity}</span>
							</div>
							<div class="flex items-baseline space-x-1">
								<span class="text-xs text-blue-600">Rp</span>
								<span class="font-semibold text-blue-900">
									{formatPrice(item.accessory.price * item.quantity)}
								</span>
							</div>
						</div>
					{/if}
				{/each}
			</div>
		</div>
	{/if}
	
	<!-- No Accessories Message -->
	{#if accessories.length === 0}
		<div class="text-center py-8 text-gray-500">
			<svg class="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
			</svg>
			<p class="text-sm">No accessories available for this product</p>
		</div>
	{/if}
</div>

<!-- Accessibility Instructions -->
<div class="sr-only" aria-live="polite">
	{#if selectedAccessories.size > 0}
		{selectedAccessories.size} accessories selected.
		{#each Array.from(selectedAccessories.values()) as item}
			{#if item.quantity > 0}
				{item.accessory.name}: {item.quantity} items.
			{/if}
		{/each}
	{/if}
</div>

<style>
	/* Line clamp utility for description */
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
	
	/* Custom focus styles for better accessibility */
	button:focus-visible,
	input:focus-visible {
		outline: 2px solid #3B82F6;
		outline-offset: 2px;
	}
	
	/* Smooth transitions for all interactive elements */
	button, input, div {
		transition-property: all;
		transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	}
	
	/* Ensure proper contrast for accessibility */
	@media (prefers-reduced-motion: reduce) {
		* {
			transition-duration: 0.01ms !important;
			animation-duration: 0.01ms !important;
		}
	}
	
	/* High contrast mode support */
	@media (prefers-contrast: high) {
		button, input {
			border-width: 2px;
		}
	}
	
	/* Remove number input spinners */
	input[type="number"]::-webkit-outer-spin-button,
	input[type="number"]::-webkit-inner-spin-button {
		-webkit-appearance: none;
		margin: 0;
	}
	
	input[type="number"] {
		-moz-appearance: textfield;
	}
</style>
