<script lang="ts">
	import type { Product, AccessorySelection } from '$lib/types/product';
	import { formatPrice } from '$lib/api/products';

	interface Props {
		accessories: Product[];
		selectedAccessories: Map<string, AccessorySelection>;
		onSelectionChange: (accessories: Map<string, AccessorySelection>) => void;
	}

	let { accessories, selectedAccessories = $bindable(), onSelectionChange }: Props = $props();

	function updateAccessoryQuantity(productId: string, quantity: number) {
		const newSelection = new Map(selectedAccessories);
		
		if (quantity <= 0) {
			newSelection.delete(productId);
		} else {
			newSelection.set(productId, { product_id: productId, quantity });
		}
		
		selectedAccessories = newSelection;
		onSelectionChange(newSelection);
	}

	function getSelectedQuantity(productId: string): number {
		return selectedAccessories.get(productId)?.quantity || 0;
	}

	function calculateAccessoryTotal(accessory: Product, quantity: number): number {
		return accessory.price * quantity;
	}

	function getTotalAccessoriesPrice(): number {
		let total = 0;
		for (const [productId, selection] of selectedAccessories) {
			const accessory = accessories.find(a => a.id === productId);
			if (accessory) {
				total += accessory.price * selection.quantity;
			}
		}
		return total;
	}
</script>

<div class="accessory-selector">
	<h3 class="text-lg font-semibold mb-4">Tambahan (Opsional)</h3>
	
	<div class="space-y-4">
		{#each accessories as accessory (accessory.id)}
			{@const selectedQuantity = getSelectedQuantity(accessory.id)}
			<div class="accessory-item border border-gray-200 rounded-lg p-4">
				<div class="flex items-start justify-between">
					<div class="accessory-info flex-1">
						<h4 class="font-medium text-gray-900">{accessory.name}</h4>
						{#if accessory.short_description}
							<p class="text-sm text-gray-600 mt-1">{accessory.short_description}</p>
						{/if}
						<div class="accessory-pricing mt-2">
							<span class="text-lg font-semibold text-blue-600">
								{formatPrice(accessory.price)}
							</span>
							<span class="text-sm text-gray-500 ml-1">per {accessory.unit}</span>
						</div>
					</div>
					
					<div class="accessory-controls ml-4">
						<div class="quantity-selector flex items-center space-x-2">
							<button
								class="quantity-btn"
								onclick={() => updateAccessoryQuantity(accessory.id, Math.max(0, selectedQuantity - 1))}
								disabled={selectedQuantity <= 0}
							>
								-
							</button>
							
							<input
								type="number"
								class="quantity-input"
								value={selectedQuantity}
								min="0"
								step={accessory.unit === 'meters' ? '0.1' : '1'}
								onchange={(e) => {
									const value = parseFloat(e.currentTarget.value) || 0;
									updateAccessoryQuantity(accessory.id, Math.max(0, value));
								}}
							/>
							
							<button
								class="quantity-btn"
								onclick={() => updateAccessoryQuantity(accessory.id, selectedQuantity + (accessory.unit === 'meters' ? 0.5 : 1))}
							>
								+
							</button>
						</div>
						
						{#if selectedQuantity > 0}
							<div class="accessory-total mt-2 text-right">
								<span class="text-sm font-medium text-gray-900">
									{formatPrice(calculateAccessoryTotal(accessory, selectedQuantity))}
								</span>
							</div>
						{/if}
					</div>
				</div>
			</div>
		{/each}
	</div>

	{#if selectedAccessories.size > 0}
		<div class="accessories-summary mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
			<h4 class="font-medium text-gray-900 mb-3">Ringkasan Tambahan</h4>
			<div class="space-y-2">
				{#each Array.from(selectedAccessories.entries()) as [productId, selection] (productId)}
					{@const accessory = accessories.find(a => a.id === productId)}
					{#if accessory}
						<div class="flex justify-between items-center text-sm">
							<span>{accessory.name} ({selection.quantity} {accessory.unit})</span>
							<span class="font-medium">{formatPrice(accessory.price * selection.quantity)}</span>
						</div>
					{/if}
				{/each}
				<div class="border-t border-gray-300 pt-2 mt-2">
					<div class="flex justify-between items-center font-semibold">
						<span>Total Tambahan:</span>
						<span class="text-blue-600">{formatPrice(getTotalAccessoriesPrice())}</span>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.accessory-selector {
		@apply mb-6;
	}

	.accessory-item {
		@apply transition-all duration-200 hover:shadow-md;
	}

	.quantity-btn {
		@apply w-8 h-8 border border-gray-300 rounded-md flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;
	}

	.quantity-input {
		@apply w-16 h-8 text-center border border-gray-300 rounded-md text-sm;
	}

	.accessory-controls {
		@apply min-w-0;
	}
</style>
