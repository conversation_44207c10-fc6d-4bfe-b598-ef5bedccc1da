<script lang="ts">
	/**
	 * CategorySection component for displaying product categories
	 * Features responsive grid layout and horizontal scrolling on mobile
	 */
	
	import ProductCard from './ProductCard.svelte';
	
	// Define interfaces locally for this component
	interface Product {
		id: string;
		name: string;
		slug?: string;
		price: number;
		description?: string;
		image: string;
		alt?: string;
	}

	interface Category {
		id: string;
		name: string;
		products: Product[];
	}
	
	// Component props
	interface Props {
		category: Category;
	}
	
	let { category }: Props = $props();
	
	// Scroll state for mobile horizontal scrolling
	let scrollContainer: HTMLElement;
	let canScrollLeft = $state(false);
	let canScrollRight = $state(true);
	
	/**
	 * Check scroll position and update scroll indicators
	 */
	function checkScrollPosition() {
		if (scrollContainer) {
			canScrollLeft = scrollContainer.scrollLeft > 0;
			canScrollRight = scrollContainer.scrollLeft < (scrollContainer.scrollWidth - scrollContainer.clientWidth);
		}
	}
	
	/**
	 * Scroll left in mobile view
	 */
	function scrollLeft() {
		if (scrollContainer) {
			scrollContainer.scrollBy({ left: -300, behavior: 'smooth' });
		}
	}
	
	/**
	 * Scroll right in mobile view
	 */
	function scrollRight() {
		if (scrollContainer) {
			scrollContainer.scrollBy({ left: 300, behavior: 'smooth' });
		}
	}
</script>

<section class="mb-12 px-4 sm:px-6 lg:px-8">
	<div class="container mx-auto">
		<!-- Category Title -->
		<div class="mb-8">
			<h2 class="text-3xl font-bold text-gray-900 mb-2">
				{category.name}
			</h2>
			<div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"></div>
		</div>
		
		<!-- Products Grid - Desktop -->
		<div class="hidden md:grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
			{#each category.products as product}
				<ProductCard
					id={product.id}
					name={product.name}
					slug={product.slug}
					price={product.price}
					category={category.name}
					shortDescription={product.description}
				/>
			{/each}
		</div>
		
		<!-- Products Horizontal Scroll - Mobile/Tablet -->
		<div class="md:hidden relative">
			<!-- Scroll Controls -->
			{#if category.products.length > 1}
				<div class="flex justify-between items-center mb-4">
					<button
						class="p-2 rounded-full bg-white shadow-md border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
						onclick={scrollLeft}
						disabled={!canScrollLeft}
						aria-label="Scroll left"
					>
						<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
						</svg>
					</button>
					
					<div class="flex space-x-2">
						{#each category.products as _}
							<div class="w-2 h-2 rounded-full bg-gray-300"></div>
						{/each}
					</div>
					
					<button
						class="p-2 rounded-full bg-white shadow-md border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
						onclick={scrollRight}
						disabled={!canScrollRight}
						aria-label="Scroll right"
					>
						<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
						</svg>
					</button>
				</div>
			{/if}
			
			<!-- Scrollable Container -->
			<div 
				bind:this={scrollContainer}
				class="flex space-x-4 overflow-x-auto scrollbar-hide pb-4"
				onscroll={checkScrollPosition}
				style="scroll-snap-type: x mandatory;"
			>
				{#each category.products as product}
					<div class="flex-none w-72 sm:w-80" style="scroll-snap-align: start;">
						<ProductCard
							id={product.id}
							name={product.name}
							slug={product.slug}
							price={product.price}
							category={category.name}
							shortDescription={product.description}
						/>
					</div>
				{/each}
			</div>
		</div>
		
		<!-- Empty State -->
		{#if category.products.length === 0}
			<div class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
				<svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-4.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7"></path>
				</svg>
				<h3 class="text-lg font-medium text-gray-900 mb-2">No products available</h3>
				<p class="text-gray-500">Products for this category will appear here soon.</p>
			</div>
		{/if}
	</div>
</section>

<style>
	/* Hide scrollbar for horizontal scroll */
	.scrollbar-hide {
		-ms-overflow-style: none;  /* Internet Explorer 10+ */
		scrollbar-width: none;  /* Firefox */
	}
	
	.scrollbar-hide::-webkit-scrollbar { 
		display: none;  /* Safari and Chrome */
	}
	
	/* Smooth scrolling behavior */
	.scrollbar-hide {
		scroll-behavior: smooth;
	}
</style>
