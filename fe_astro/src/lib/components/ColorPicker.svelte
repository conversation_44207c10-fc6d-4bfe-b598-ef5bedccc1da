<script lang="ts">
	/**
	 * ColorPicker Component (Theme Selector)
	 * Displays circular color swatches for theme selection with accessibility features
	 * 
	 * @component
	 * @example
	 * <ColorPicker 
	 *   themes={productThemes} 
	 *   selectedTheme="hitam" 
	 *   onThemeChange={handleThemeChange}
	 * />
	 */
	
	// Theme interface
	interface Theme {
		id: string;
		name: string;
		color: string;
		isDefault?: boolean;
	}
	
	// Props interface
	interface Props {
		/** Array of available themes */
		themes: Theme[];
		/** Currently selected theme ID */
		selectedTheme: string;
		/** Callback function when theme changes */
		onThemeChange: (theme: Theme) => void;
	}
	
	let { themes, selectedTheme, onThemeChange }: Props = $props();
	
	// Component state
	let hoveredTheme = $state<string | null>(null);
	let focusedTheme = $state<string | null>(null);
	
	/**
	 * Handle theme selection
	 * @param theme - Selected theme object
	 */
	function selectTheme(theme: Theme) {
		onThemeChange(theme);
	}
	
	/**
	 * Handle keyboard navigation
	 * @param event - Keyboard event
	 * @param theme - Theme object
	 */
	function handleKeydown(event: KeyboardEvent, theme: Theme) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			selectTheme(theme);
		}
	}
	
	/**
	 * Get contrast color for text based on background color
	 * @param hexColor - Hex color string
	 * @returns 'white' or 'black' for optimal contrast
	 */
	function getContrastColor(hexColor: string): string {
		// Remove # if present
		const color = hexColor.replace('#', '');
		
		// Convert to RGB
		const r = parseInt(color.substr(0, 2), 16);
		const g = parseInt(color.substr(2, 2), 16);
		const b = parseInt(color.substr(4, 2), 16);
		
		// Calculate luminance
		const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
		
		return luminance > 0.5 ? 'black' : 'white';
	}
	
	/**
	 * Check if a color is light or dark
	 * @param hexColor - Hex color string
	 * @returns true if color is light
	 */
	function isLightColor(hexColor: string): boolean {
		return getContrastColor(hexColor) === 'black';
	}
	
	/**
	 * Generate accessible border color for theme swatch
	 * @param theme - Theme object
	 * @param isSelected - Whether theme is selected
	 * @param isHovered - Whether theme is hovered
	 * @returns CSS border class
	 */
	function getBorderClass(theme: Theme, isSelected: boolean, isHovered: boolean): string {
		if (isSelected) {
			return 'ring-4 ring-blue-500 ring-offset-2';
		}
		if (isHovered) {
			return 'ring-2 ring-gray-400 ring-offset-1';
		}
		return 'border-2 border-gray-200';
	}
	
	/**
	 * Get shadow class for theme swatch
	 * @param isSelected - Whether theme is selected
	 * @param isHovered - Whether theme is hovered
	 * @returns CSS shadow class
	 */
	function getShadowClass(isSelected: boolean, isHovered: boolean): string {
		if (isSelected) {
			return 'shadow-lg';
		}
		if (isHovered) {
			return 'shadow-md';
		}
		return 'shadow-sm';
	}
</script>

<!-- Color Picker Container -->
<div class="space-y-3" role="radiogroup" aria-label="Select product theme">
	<!-- Theme Options -->
	<div class="flex flex-wrap gap-3">
		{#each themes as theme (theme.id)}
			{@const isSelected = selectedTheme === theme.id}
			{@const isHovered = hoveredTheme === theme.id}
			{@const isFocused = focusedTheme === theme.id}
			{@const contrastColor = getContrastColor(theme.color)}
			{@const isLight = isLightColor(theme.color)}
			
			<!-- Theme Swatch Button -->
			<button
				class="relative flex flex-col items-center space-y-2 p-2 rounded-lg transition-all duration-200 focus:outline-none group"
				class:bg-blue-50={isSelected}
				onclick={() => selectTheme(theme)}
				onkeydown={(e) => handleKeydown(e, theme)}
				onmouseenter={() => hoveredTheme = theme.id}
				onmouseleave={() => hoveredTheme = null}
				onfocus={() => focusedTheme = theme.id}
				onblur={() => focusedTheme = null}
				role="radio"
				aria-checked={isSelected}
				aria-label={`Select ${theme.name} theme`}
				tabindex={isSelected ? 0 : -1}
			>
				<!-- Color Swatch -->
				<div
					class="w-12 h-12 rounded-full transition-all duration-200 transform"
					class:scale-110={isSelected || isHovered}
					class:scale-100={!isSelected && !isHovered}
					style="background-color: {theme.color}"
				>
					<!-- Border and Ring Effects -->
					<div
						class="w-full h-full rounded-full transition-all duration-200 {getBorderClass(theme, isSelected, isHovered || isFocused)} {getShadowClass(isSelected, isHovered || isFocused)}"
					></div>
					
					<!-- Selected Checkmark -->
					{#if isSelected}
						<div class="absolute inset-0 flex items-center justify-center">
							<svg 
								class="w-6 h-6 transition-all duration-200"
								style="color: {contrastColor}"
								fill="currentColor" 
								viewBox="0 0 20 20"
							>
								<path 
									fill-rule="evenodd" 
									d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
									clip-rule="evenodd"
								></path>
							</svg>
						</div>
					{/if}
				</div>
				
				<!-- Theme Name -->
				<span 
					class="text-sm font-medium transition-colors duration-200"
					class:text-blue-600={isSelected}
					class:text-gray-700={!isSelected && !isHovered}
					class:text-gray-900={!isSelected && isHovered}
				>
					{theme.name}
				</span>
				
				<!-- Tooltip on Hover -->
				{#if isHovered && !isSelected}
					<div class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
						{theme.name}
						<!-- Tooltip Arrow -->
						<div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
					</div>
				{/if}
			</button>
		{/each}
	</div>
	
	<!-- Selected Theme Display -->
	<!-- {#if selectedTheme}
		{@const currentTheme = themes.find(t => t.id === selectedTheme)}
		{#if currentTheme}
			<div class="flex items-center space-x-2 text-sm text-gray-600">
				<span>Selected:</span>
				<div class="flex items-center space-x-2">
					<div 
						class="w-4 h-4 rounded-full border border-gray-300"
						style="background-color: {currentTheme.color}"
					></div>
					<span class="font-medium text-gray-900">{currentTheme.name}</span>
				</div>
			</div>
		{/if}
	{/if} -->
</div>

<!-- Accessibility Instructions -->
<div class="sr-only" aria-live="polite">
	{#if selectedTheme}
		{@const currentTheme = themes.find(t => t.id === selectedTheme)}
		{#if currentTheme}
			{currentTheme.name} theme selected.
		{/if}
	{/if}
</div>

<style>
	/* Custom focus styles for better accessibility */
	button:focus-visible {
		outline: 2px solid #3B82F6;
		outline-offset: 2px;
	}
	
	/* Smooth transitions for all interactive elements */
	button, div {
		transition-property: all;
		transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	}
	
	/* Ensure proper contrast for accessibility */
	@media (prefers-reduced-motion: reduce) {
		* {
			transition-duration: 0.01ms !important;
			animation-duration: 0.01ms !important;
		}
	}
	
	/* High contrast mode support */
	@media (prefers-contrast: high) {
		button {
			border-width: 2px;
		}
		
		.ring-4 {
			ring-width: 6px;
		}
		
		.ring-2 {
			ring-width: 4px;
		}
	}
</style>
