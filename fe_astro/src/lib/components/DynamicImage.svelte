<script>
	import { onMount } from 'svelte';
	import { config } from '$lib/config/env';

	/**
	 * DynamicImage Component
	 * Displays responsive images based on device viewport:
	 * - Mobile: 4x5 aspect ratio images
	 * - Desktop: 16x9 aspect ratio images
	 */

	// Props
	export let slug = ''; // Product slug (required)
	export let index = 1; // Image index (default: 1)
	export let alt = ''; // Alt text
	export let className = ''; // Additional CSS classes
	export let style = ''; // Inline styles
	export let fallbackSrc = `${config.api.staticUrl}/placeholder-image.svg`; // Fallback image
	export let debug = false; // Enable debug logging

	// Internal state
	let currentSrc = '';
	let imageLoaded = false;
	let imageError = false;
	let isMobile = false;

	// Reactive statements
	$: aspect = isMobile ? '4x5' : '16x9';
	$: imagePath = slug ? `${config.api.staticUrl}/uploads/products/${aspect}_${slug}_${index}` : '';

	// Check if we're on mobile viewport
	function checkMobile() {
		if (typeof window !== 'undefined') {
			isMobile = window.innerWidth < 768; // Tailwind's md breakpoint
		}
	}

	// Find the correct image with proper extension
	async function findImageWithExtension(basePath) {
		const extensions = ['jpg', 'jpeg', 'png', 'webp'];

		if (debug) console.log(`🔍 Searching for images with base path: ${basePath}`);

		for (const ext of extensions) {
			const testSrc = `${basePath}.${ext}`;
			try {
				const response = await fetch(testSrc, { method: 'HEAD' });
				if (response.ok) {
					if (debug) console.log(`✅ Found image: ${testSrc}`);
					return testSrc;
				} else {
					if (debug) console.log(`❌ Image not found (${response.status}): ${testSrc}`);
				}
			} catch (error) {
				if (debug) console.log(`❌ Error checking image: ${testSrc}`, error);
				// Continue to next extension
			}
		}

		if (debug) console.log(`❌ No images found for base path: ${basePath}`);
		return null;
	}

	// Load the appropriate image
	async function loadImage() {
		if (!slug) {
			currentSrc = fallbackSrc;
			return;
		}

		imageLoaded = false;
		imageError = false;

		try {
			const foundSrc = await findImageWithExtension(imagePath);
			if (foundSrc) {
				currentSrc = foundSrc;
			} else {
				// Try the other aspect ratio as fallback
				const fallbackAspect = isMobile ? '16x9' : '4x5';
				const fallbackPath = `${config.api.staticUrl}/uploads/products/${fallbackAspect}_${slug}_${index}`;
				const fallbackFoundSrc = await findImageWithExtension(fallbackPath);
				
				if (fallbackFoundSrc) {
					currentSrc = fallbackFoundSrc;
				} else {
					currentSrc = fallbackSrc;
					imageError = true;
				}
			}
		} catch (error) {
			currentSrc = fallbackSrc;
			imageError = true;
		}
	}

	// Handle image load success
	function handleImageLoad() {
		imageLoaded = true;
		imageError = false;
	}

	// Handle image load error
	function handleImageError() {
		imageError = true;
		if (currentSrc !== fallbackSrc) {
			currentSrc = fallbackSrc;
		}
	}

	// Handle window resize
	function handleResize() {
		const wasMobile = isMobile;
		checkMobile();
		
		// Reload image if viewport changed
		if (wasMobile !== isMobile) {
			loadImage();
		}
	}

	// Component lifecycle
	onMount(() => {
		checkMobile();
		loadImage();

		// Listen for window resize
		if (typeof window !== 'undefined') {
			window.addEventListener('resize', handleResize);
			
			return () => {
				window.removeEventListener('resize', handleResize);
			};
		}
	});

	// Reactive: reload image when slug or index changes
	$: if (slug || index) {
		loadImage();
	}
</script>

<!-- Image element with responsive behavior -->
<div class="dynamic-image-container {className}" {style}>
	{#if currentSrc}
		<img
			src={currentSrc}
			{alt}
			class="dynamic-image {imageLoaded ? 'loaded' : 'loading'} {imageError ? 'error' : ''}"
			on:load={handleImageLoad}
			on:error={handleImageError}
			loading="lazy"
		/>
	{:else}
		<!-- Loading placeholder -->
		<div class="dynamic-image-placeholder">
			<div class="animate-pulse bg-gray-200 w-full h-full flex items-center justify-center">
				<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
				</svg>
			</div>
		</div>
	{/if}
	
	<!-- Debug info (only in development) -->
	<!-- {#if import.meta.env.DEV}
		<div class="absolute top-0 left-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-br">
			{aspect} | {index}
		</div>
	{/if} -->
</div>

<style>
	.dynamic-image-container {
		position: relative;
		overflow: hidden;
	}

	.dynamic-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: opacity 0.3s ease;
	}

	.dynamic-image.loading {
		opacity: 0;
	}

	.dynamic-image.loaded {
		opacity: 1;
	}

	.dynamic-image.error {
		opacity: 0.7;
		filter: grayscale(100%);
	}

	.dynamic-image-placeholder {
		width: 100%;
		height: 100%;
		min-height: 200px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f3f4f6;
	}

	/* Responsive aspect ratios */
	@media (max-width: 767px) {
		/* Mobile: 4:5 aspect ratio */
		.dynamic-image-container {
			aspect-ratio: 4 / 5;
		}
	}

	@media (min-width: 768px) {
		/* Desktop: 16:9 aspect ratio */
		.dynamic-image-container {
			aspect-ratio: 16 / 9;
		}
	}
</style>
