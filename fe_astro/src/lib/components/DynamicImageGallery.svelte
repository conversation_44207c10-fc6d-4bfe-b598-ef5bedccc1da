<!--
@component DynamicImageGallery
Enhanced image gallery with smooth transitions, arrow navigation, and swipe support
Uses Unsplash images as fallbacks for better visual experience
-->

<script lang="ts">
	import { onMount } from 'svelte';
	import { config } from '$lib/config/env';
	import DynamicImageSingle from './DynamicImageSingle.svelte';

	interface Props {
		/** Product slug for image loading */
		slug: string;
		/** Product name for alt text */
		name: string;
		/** Additional CSS classes */
		className?: string;
		/** Number of images to show in gallery */
		imageCount?: number;
		/** Enable auto-advance */
		autoAdvance?: boolean;
		/** Auto-advance interval in milliseconds */
		autoAdvanceInterval?: number;
		/** Border radius for all images in gallery (e.g., 'xl', '2xl', '3xl', 'full') */
		rounded?: string;
	}

	let {
		slug,
		name,
		className = '',
		imageCount = 3,
		autoAdvance = true,
		autoAdvanceInterval = 5000,
		rounded = '2xl'
	}: Props = $props();

	// Gallery state
	let currentIndex = $state(0);
	let isTransitioning = $state(false);
	let galleryContainer: HTMLDivElement;
	let touchStartX = $state(0);
	let touchEndX = $state(0);
	let autoAdvanceTimer: number | null = null;

	// Generate image sources (mix of product images and Unsplash fallbacks)
	const totalImages = Math.max(imageCount, 5); // Minimum 5 images

	// Generate border radius classes
	const roundedClasses = $derived(() => {
		const baseClass = 'rounded';
		if (!rounded || rounded === 'none') return '';
		if (rounded === 'sm') return `${baseClass}-sm`;
		if (rounded === 'md') return `${baseClass}-md`;
		if (rounded === 'lg') return `${baseClass}-lg`;
		if (rounded === 'xl') return `${baseClass}-xl`;
		if (rounded === '2xl') return `${baseClass}-2xl`;
		if (rounded === '3xl') return `${baseClass}-3xl`;
		if (rounded === 'full') return `${baseClass}-full`;
		// Custom rounded value (e.g., '12px', '1rem')
		return `${baseClass}-[${rounded}]`;
	});
	
	type ImageSource = {
		type: 'product';
		index: number;
		alt: string;
	} | {
		type: 'unsplash';
		url: string;
		alt: string;
	};

	const imageSources = $derived((): ImageSource[] => {
		const sources: ImageSource[] = [];
		
		// Add product images
		for (let i = 1; i <= imageCount; i++) {
			sources.push({
				type: 'product',
				index: i,
				alt: `${name} - Image ${i}`
			});
		}
		
		// Add Unsplash fallback images
		const remainingSlots = totalImages - sources.length;
		for (let i = 0; i < remainingSlots; i++) {
			const unsplashIndex = i % config.unsplash.fallbackImages.length;
			sources.push({
				type: 'unsplash',
				url: config.unsplash.fallbackImages[unsplashIndex],
				alt: `${name} - Gallery Image ${sources.length + 1}`
			});
		}
		
		return sources;
	});

	/**
	 * Navigate to specific image
	 */
	function goToImage(index: number): void {
		if (isTransitioning || index === currentIndex) return;
		
		isTransitioning = true;
		currentIndex = index;
		
		// Reset auto-advance timer
		resetAutoAdvance();
		
		setTimeout(() => {
			isTransitioning = false;
		}, 300);
	}

	/**
	 * Navigate to previous image
	 */
	function previousImage(): void {
		const newIndex = currentIndex === 0 ? imageSources().length - 1 : currentIndex - 1;
		goToImage(newIndex);
	}

	/**
	 * Navigate to next image
	 */
	function nextImage(): void {
		const newIndex = currentIndex === imageSources().length - 1 ? 0 : currentIndex + 1;
		goToImage(newIndex);
	}

	/**
	 * Handle touch start
	 */
	function handleTouchStart(event: TouchEvent): void {
		touchStartX = event.touches[0].clientX;
	}

	/**
	 * Handle touch end and detect swipe
	 */
	function handleTouchEnd(event: TouchEvent): void {
		touchEndX = event.changedTouches[0].clientX;
		handleSwipe();
	}

	/**
	 * Process swipe gesture
	 */
	function handleSwipe(): void {
		const swipeThreshold = 50;
		const swipeDistance = touchStartX - touchEndX;

		if (Math.abs(swipeDistance) > swipeThreshold) {
			if (swipeDistance > 0) {
				// Swipe left - next image
				nextImage();
			} else {
				// Swipe right - previous image
				previousImage();
			}
		}
	}

	/**
	 * Handle keyboard navigation
	 */
	function handleKeydown(event: KeyboardEvent): void {
		switch (event.key) {
			case 'ArrowLeft':
				event.preventDefault();
				previousImage();
				break;
			case 'ArrowRight':
				event.preventDefault();
				nextImage();
				break;
		}
	}

	/**
	 * Start auto-advance timer
	 */
	function startAutoAdvance(): void {
		if (!autoAdvance) return;
		
		autoAdvanceTimer = window.setInterval(() => {
			if (!isTransitioning) {
				nextImage();
			}
		}, autoAdvanceInterval);
	}

	/**
	 * Stop auto-advance timer
	 */
	function stopAutoAdvance(): void {
		if (autoAdvanceTimer) {
			clearInterval(autoAdvanceTimer);
			autoAdvanceTimer = null;
		}
	}

	/**
	 * Reset auto-advance timer
	 */
	function resetAutoAdvance(): void {
		stopAutoAdvance();
		startAutoAdvance();
	}

	onMount(() => {
		startAutoAdvance();

		return () => {
			stopAutoAdvance();
		};
	});
</script>

<svelte:window on:keydown={handleKeydown} />

<div
	class="relative w-full aspect-[16/9] overflow-hidden bg-gray-100 {roundedClasses()} group {className}"
	bind:this={galleryContainer}
	ontouchstart={handleTouchStart}
	ontouchend={handleTouchEnd}
	onmouseenter={stopAutoAdvance}
	onmouseleave={startAutoAdvance}
	role="button"
	tabindex="0"
	aria-label="Product image gallery"
>
	<!-- Main Image Display -->
	<div class="relative w-full h-full">
		{#each imageSources() as source, index}
			<div 
				class="absolute inset-0 transition-all duration-500 ease-in-out {
					index === currentIndex 
						? 'opacity-100 scale-100' 
						: 'opacity-0 scale-105'
				}"
				style="z-index: {index === currentIndex ? 10 : 1}"
			>
				{#if source.type === 'product'}
					<DynamicImageSingle
						{slug}
						index={source.index}
						alt={source.alt}
						className="w-full h-full {roundedClasses()}"
					/>
				{:else}
					<img
						src={source.url}
						alt={source.alt}
						class="w-full h-full object-cover {roundedClasses()}"
						loading="lazy"
					/>
				{/if}
			</div>
		{/each}

		<!-- Navigation Arrows -->
		<button
			class="absolute left-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 text-gray-800 p-2 rounded-full shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100 z-20"
			onclick={previousImage}
			aria-label="Previous image"
		>
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
			</svg>
		</button>

		<button
			class="absolute right-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 text-gray-800 p-2 rounded-full shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100 z-20"
			onclick={nextImage}
			aria-label="Next image"
		>
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
			</svg>
		</button>

		<!-- Image Indicators -->
		<div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-20">
			{#each imageSources() as _, index}
				<button
					class="w-2 h-2 rounded-full transition-all duration-200 {
						index === currentIndex
							? 'bg-white scale-125'
							: 'bg-white bg-opacity-50 hover:bg-opacity-75'
					}"
					onclick={() => goToImage(index)}
					aria-label="Go to image {index + 1}"
				></button>
			{/each}
		</div>

		<!-- Loading Overlay -->
		{#if isTransitioning}
			<div class="absolute inset-0 bg-black bg-opacity-10 z-30 pointer-events-none"></div>
		{/if}
	</div>

	<!-- Thumbnail Strip (Desktop Only) -->
	<div class="absolute bottom-4 right-4 hidden lg:flex space-x-2 z-20">
		{#each imageSources().slice(0, 4) as source, index}
			<button
				class="w-12 h-8 {roundedClasses()} overflow-hidden border-2 transition-all duration-200 {
					index === currentIndex
						? 'border-white scale-110'
						: 'border-white border-opacity-50 hover:border-opacity-100'
				}"
				onclick={() => goToImage(index)}
			>
				{#if source.type === 'product'}
					<DynamicImageSingle
						{slug}
						index={source.index}
						alt={source.alt}
						className="w-full h-full {roundedClasses()}"
					/>
				{:else}
					<img
						src={source.url}
						alt={source.alt}
						class="w-full h-full object-cover {roundedClasses()}"
						loading="lazy"
					/>
				{/if}
			</button>
		{/each}
	</div>
</div>

<style>
	/* Smooth transitions for touch devices */
	@media (hover: none) {
		.group:hover .opacity-0 {
			opacity: 1;
		}
	}
</style>
