<!--
@component DynamicImageSingle
Simple responsive image component for single image display
Automatically detects viewport and loads appropriate aspect ratio
-->

<script lang="ts">
	import { onMount } from 'svelte';
	import { config } from '$lib/config/env';

	interface Props {
		/** Product slug for image loading */
		slug: string;
		/** Image index (default: 1) */
		index?: number;
		/** Alt text for accessibility */
		alt: string;
		/** Additional CSS classes */
		className?: string;
		/** Inline styles */
		style?: string;
		/** Enable debug logging */
		debug?: boolean;
		/** Border radius for image (e.g., 'xl', '2xl', '3xl', 'full') */
		rounded?: string;
	}

	let {
		slug,
		index = 1,
		alt,
		className = '',
		style = '',
		debug = false,
		rounded = ''
	}: Props = $props();

	// Component state
	let currentSrc = $state('');
	let imageLoaded = $state(false);
	let imageError = $state(false);
	let isMobile = $state(false);
	let fallbackIndex = $state(0);

	// Derived values
	const aspect = $derived(isMobile ? '4x5' : '16x9');
	const imagePath = $derived(slug ? `${config.api.staticUrl}/uploads/products/${aspect}_${slug}_${index}` : '');

	// Multiple fallback options
	const fallbackSources = [
		`${config.api.staticUrl}/placeholder-image.svg`,
		// Data URL fallback - simple gray rectangle with icon
		'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMDAgMTUwQzE4Ni4xOSAxNTAgMTc1IDE2MS4xOSAxNzUgMTc1QzE3NSAxODguODEgMTg2LjE5IDIwMCAyMDAgMjAwQzIxMy44MSAyMDAgMjI1IDE4OC44MSAyMjUgMTc1QzIyNSAxNjEuMTkgMjEzLjgxIDE1MCAyMDAgMTUwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTI1IDI1MEgzMDBWMjc1SDEyNVYyNTBaIiBmaWxsPSIjOUNBM0FGIi8+CjwvdXZnPgo=',
		// External placeholder as last resort
		'https://via.placeholder.com/400x400/f3f4f6/9ca3af?text=No+Image'
	];

	// Generate border radius classes
	const roundedClasses = $derived(() => {
		if (!rounded) return '';
		const baseClass = 'rounded';
		if (rounded === 'none') return '';
		if (rounded === 'sm') return `${baseClass}-sm`;
		if (rounded === 'md') return `${baseClass}-md`;
		if (rounded === 'lg') return `${baseClass}-lg`;
		if (rounded === 'xl') return `${baseClass}-xl`;
		if (rounded === '2xl') return `${baseClass}-2xl`;
		if (rounded === '3xl') return `${baseClass}-3xl`;
		if (rounded === 'full') return `${baseClass}-full`;
		// Custom rounded value (e.g., '12px', '1rem')
		return `${baseClass}-[${rounded}]`;
	});

	/**
	 * Check if we're on mobile viewport
	 */
	function checkMobile(): void {
		if (typeof window !== 'undefined') {
			isMobile = window.innerWidth < 768; // Tailwind's md breakpoint
		}
	}

	/**
	 * Find image with correct extension
	 */
	async function findImageWithExtension(basePath: string): Promise<string | null> {
		const extensions = ['jpg', 'jpeg', 'png', 'webp'];

		if (debug) console.log(`🔍 Searching for images with base path: ${basePath}`);

		for (const ext of extensions) {
			const testSrc = `${basePath}.${ext}`;
			try {
				const response = await fetch(testSrc, { method: 'HEAD' });
				if (response.ok) {
					if (debug) console.log(`✅ Found image: ${testSrc}`);
					return testSrc;
				}
			} catch (error) {
				if (debug) console.log(`❌ Error checking image: ${testSrc}`, error);
			}
		}

		if (debug) console.log(`❌ No images found for base path: ${basePath}`);
		return null;
	}

	/**
	 * Load the appropriate image
	 */
	async function loadImage(): Promise<void> {
		if (!slug) {
			currentSrc = fallbackSources[0];
			return;
		}

		imageLoaded = false;
		imageError = false;
		fallbackIndex = 0;

		try {
			const foundSrc = await findImageWithExtension(imagePath);
			if (foundSrc) {
				currentSrc = foundSrc;
			} else {
				// Try the other aspect ratio as fallback
				const fallbackAspect = isMobile ? '16x9' : '4x5';
				const fallbackPath = `${config.api.staticUrl}/uploads/products/${fallbackAspect}_${slug}_${index}`;
				const fallbackFoundSrc = await findImageWithExtension(fallbackPath);

				if (fallbackFoundSrc) {
					currentSrc = fallbackFoundSrc;
				} else {
					currentSrc = fallbackSources[0];
					imageError = true;
				}
			}
		} catch (error) {
			currentSrc = fallbackSources[0];
			imageError = true;
		}
	}

	/**
	 * Handle image load success
	 */
	function handleImageLoad(): void {
		imageLoaded = true;
		imageError = false;
	}

	/**
	 * Handle image load error with multiple fallbacks
	 */
	function handleImageError(): void {
		imageError = true;

		// Try next fallback
		if (fallbackIndex < fallbackSources.length - 1) {
			fallbackIndex++;
			currentSrc = fallbackSources[fallbackIndex];

			if (debug) {
				console.warn(`Image failed for ${slug}, trying fallback ${fallbackIndex + 1}:`, fallbackSources[fallbackIndex]);
			}
		} else {
			// All fallbacks failed, keep the last one
			if (debug) {
				console.error(`All image fallbacks failed for ${slug}`);
			}
		}
	}

	/**
	 * Handle window resize
	 */
	function handleResize(): void {
		const wasMobile = isMobile;
		checkMobile();
		
		// Reload image if viewport changed
		if (wasMobile !== isMobile) {
			loadImage();
		}
	}

	// Component lifecycle
	onMount(() => {
		checkMobile();
		loadImage();

		// Listen for window resize
		if (typeof window !== 'undefined') {
			window.addEventListener('resize', handleResize);
			
			return () => {
				window.removeEventListener('resize', handleResize);
			};
		}
	});

	// Reactive: reload image when slug or index changes
	$effect(() => {
		if (slug || index) {
			loadImage();
		}
	});
</script>

<!-- Single Image Container -->
<div class="dynamic-image-single {roundedClasses()} {className}" {style}>
	{#if currentSrc}
		<img
			src={currentSrc}
			{alt}
			class="w-full h-full object-cover transition-opacity duration-300 {roundedClasses()} {
				imageLoaded ? 'opacity-100' : 'opacity-0'
			} {imageError ? 'grayscale' : ''}"
			onload={handleImageLoad}
			onerror={handleImageError}
			loading="lazy"
		/>
	{:else}
		<!-- Loading placeholder -->
		<div class="w-full h-full min-h-[200px] bg-gray-100 flex items-center justify-center">
			<div class="animate-pulse">
				<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
				</svg>
			</div>
		</div>
	{/if}
</div>

<style>
	.dynamic-image-single {
		position: relative;
		overflow: hidden;
	}

	/* Responsive aspect ratios */
	@media (max-width: 767px) {
		/* Mobile: 4:5 aspect ratio */
		.dynamic-image-single {
			aspect-ratio: 4 / 5;
		}
	}

	@media (min-width: 768px) {
		/* Desktop: 16:9 aspect ratio */
		.dynamic-image-single {
			aspect-ratio: 16 / 9;
		}
	}
</style>
