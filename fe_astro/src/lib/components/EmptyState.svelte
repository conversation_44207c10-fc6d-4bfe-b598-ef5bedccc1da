<script lang="ts">
	/**
	 * EmptyState Component
	 * Reusable component for displaying empty states with optional actions
	 */

	interface Props {
		title: string;
		description: string;
		icon?: 'products' | 'categories' | 'search' | 'error' | 'upload';
		actionText?: string;
		actionHref?: string;
		onAction?: () => void;
		showRefresh?: boolean;
		onRefresh?: () => void;
	}

	let {
		title,
		description,
		icon = 'products',
		actionText,
		actionHref,
		onAction,
		showRefresh = false,
		onRefresh
	}: Props = $props();

	// Icon definitions
	const icons = {
		products: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>`,
		categories: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>`,
		search: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>`,
		error: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>`,
		upload: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>`
	};

	function handleAction() {
		if (onAction) {
			onAction();
		} else if (actionHref) {
			window.location.href = actionHref;
		}
	}
</script>

<div class="text-center py-12">
	<!-- Icon -->
	<div class="text-gray-400 mb-4">
		<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			{@html icons[icon]}
		</svg>
	</div>

	<!-- Title -->
	<h2 class="text-xl font-semibold text-gray-900 mb-2">{title}</h2>

	<!-- Description -->
	<p class="text-gray-600 mb-6 max-w-md mx-auto">{description}</p>

	<!-- Actions -->
	<div class="flex flex-col sm:flex-row gap-3 justify-center items-center">
		{#if actionText && (actionHref || onAction)}
			<button
				class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
				onclick={handleAction}
			>
				{actionText}
			</button>
		{/if}

		{#if showRefresh && onRefresh}
			<button
				class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors font-medium flex items-center"
				onclick={onRefresh}
			>
				<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
				</svg>
				Refresh
			</button>
		{/if}
	</div>
</div>

<style>
	/* Additional styling if needed */
</style>
