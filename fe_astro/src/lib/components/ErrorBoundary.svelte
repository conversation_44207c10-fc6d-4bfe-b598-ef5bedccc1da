<script lang="ts">
	/**
	 * ErrorBoundary Component
	 * Handles API errors and provides fallback UI with retry functionality
	 */

	interface Props {
		error?: string | null;
		loading?: boolean;
		onRetry?: () => void;
		fallbackTitle?: string;
		fallbackDescription?: string;
		showRetry?: boolean;
		children?: any;
	}

	let {
		error = null,
		loading = false,
		onRetry,
		fallbackTitle = "Something went wrong",
		fallbackDescription = "We encountered an error while loading the data. Please try again.",
		showRetry = true,
		children
	}: Props = $props();

	// Determine error type and provide appropriate messaging
	function getErrorDetails(errorMessage: string | null) {
		if (!errorMessage) return null;

		const message = errorMessage.toLowerCase();

		if (message.includes('401') || message.includes('unauthorized')) {
			return {
				title: "Authentication Required",
				description: "Please log in to access this content.",
				icon: "🔒",
				showRetry: false
			};
		}

		if (message.includes('403') || message.includes('forbidden')) {
			return {
				title: "Access Denied",
				description: "You don't have permission to access this content.",
				icon: "🚫",
				showRetry: false
			};
		}

		if (message.includes('404') || message.includes('not found')) {
			return {
				title: "Content Not Found",
				description: "The requested content could not be found.",
				icon: "🔍",
				showRetry: false
			};
		}

		if (message.includes('500') || message.includes('internal server error')) {
			return {
				title: "Server Error",
				description: "Our servers are experiencing issues. Please try again in a few moments.",
				icon: "⚠️",
				showRetry: true
			};
		}

		if (message.includes('network') || message.includes('fetch')) {
			return {
				title: "Connection Error",
				description: "Unable to connect to the server. Please check your internet connection.",
				icon: "🌐",
				showRetry: true
			};
		}

		// Default error
		return {
			title: fallbackTitle,
			description: fallbackDescription,
			icon: "❌",
			showRetry: true
		};
	}

	let errorDetails = $derived(error ? getErrorDetails(error) : null);
</script>

{#if loading}
	<!-- Loading State -->
	<div class="flex items-center justify-center py-12">
		<div class="text-center">
			<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
			<p class="text-gray-600">Loading...</p>
		</div>
	</div>
{:else if error && errorDetails}
	<!-- Error State -->
	<div class="text-center py-12">
		<div class="mb-4 text-4xl">{errorDetails.icon}</div>
		<h2 class="text-xl font-semibold text-gray-900 mb-2">{errorDetails.title}</h2>
		<p class="text-gray-600 mb-6 max-w-md mx-auto">{errorDetails.description}</p>
		
		{#if errorDetails.showRetry && showRetry && onRetry}
			<button
				class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
				onclick={onRetry}
			>
				Try Again
			</button>
		{/if}

		<!-- Technical error details (for debugging) -->
		{#if error}
			<details class="mt-6 text-left max-w-md mx-auto">
				<summary class="text-sm text-gray-500 cursor-pointer hover:text-gray-700">
					Technical Details
				</summary>
				<pre class="mt-2 text-xs text-gray-600 bg-gray-100 p-3 rounded overflow-auto">{error}</pre>
			</details>
		{/if}
	</div>
{:else}
	<!-- Success State - Render Children -->
	{@render children?.()}
{/if}

<style>
	details summary {
		list-style: none;
	}
	
	details summary::-webkit-details-marker {
		display: none;
	}
	
	details summary::before {
		content: "▶ ";
		transition: transform 0.2s;
	}
	
	details[open] summary::before {
		transform: rotate(90deg);
	}
</style>
