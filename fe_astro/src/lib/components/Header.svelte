<script lang="ts">
	import logo from '../../assets/logo/Astroworks.svg?url'
	import homeIcon from '../../assets/icons/home.svg?url'
	import cartIcon from '../../assets/icons/cart.svg?url'
	import chatIcon from '../../assets/icons/chat.svg?url'
	import tutorialIcon from '../../assets/icons/tutorial.svg?url'
	import { cart } from '$lib/stores/cart';
	import { getWhatsAppUrl, fetchBackendConfig } from '$lib/config/env';
	
	/**
	 * Header component for Astro Works homepage
	 * Features responsive navigation with gradient background matching the reference design
	 */
	
	// Mobile menu state
	let mobileMenuOpen = $state(false);

	// WhatsApp URL state
	let whatsappUrl = $state(getWhatsAppUrl());
	
	/**
	 * Toggle mobile menu visibility
	 */
	function toggleMobileMenu() {
		mobileMenuOpen = !mobileMenuOpen;
	}
	
	/**
	 * Close mobile menu when clicking outside or on menu items
	 */
	function closeMobileMenu() {
		mobileMenuOpen = false;
	}
</script>

<header class="sticky top-0 z-50 bg-gradient-to-b from-[#5EC2DB] to-[#5F44F0] lg:shadow-lg">
	<div class="container mx-auto px-4 sm:px-6 lg:px-8">
		<div class="lg:relative flex flex-col lg:flex-row items-center justify-center py-4 lg:py-8">
			<!-- Logo/Brand -->
			<div class="flex-shrink-0 mt-8 lg:mt-0">
				<a href="/" class="flex items-center">
					<img src={logo} alt="Astro Works Logo" class="h-4 lg:h-6 mb-2 lg:ml-2" />
				</a>
			</div>
			<!-- Navigation Bar -->
			<nav class="lg:absolute flex justify-around w-full lg:w-fit items-center lg:gap-10 text-white lg:right-0 mt-8 lg:mt-0">
				<!-- Company -->
				<a href="/" class="flex flex-col items-center hover:text-blue-200 transition-colors duration-200">
					<img src={homeIcon} alt="Company" class="h-5 mb-1" />
					<span class="text-xs lg:text-sm">Company</span>
				</a>

				<!-- Tutorial -->
				<a href="/" class="flex flex-col items-center hover:text-blue-200 transition-colors duration-200">
					<img src={tutorialIcon} alt="Tutorial" class="h-5 mb-1" />
					<span class="text-xs lg:text-sm">Tutorial</span>
				</a>

				<!-- Chat -->
				<a href={whatsappUrl} class="flex flex-col items-center hover:text-blue-200 transition-colors duration-200">
					<img src={chatIcon} alt="Chat" class="h-5 mb-1" />
					<span class="text-xs lg:text-sm">Chat</span>
				</a>

				<!-- Keranjang -->
				<a href="/checkout" class="flex flex-col items-center hover:text-blue-200 transition-colors duration-200">
					<img src={cartIcon} alt="Keranjang" class="h-5 mb-1" />
					<span class="text-xs lg:text-sm">Keranjang</span>
				</a>
			</nav>
			
		</div>
	</div>
</header>
