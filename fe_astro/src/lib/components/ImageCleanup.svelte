<!--
@component ImageCleanup
Component for cleaning up legacy non-WebP images from the products directory
-->

<script lang="ts">
	import { config } from '$lib/config/env';
	
	interface Props {
		/** Callback when cleanup completes */
		onCleanupComplete?: (result: CleanupResult) => void;
	}

	interface CleanupResult {
		success: boolean;
		message: string;
		files_removed: string[];
		total_removed: number;
		dry_run: boolean;
	}

	interface UploadStats {
		totalFiles: number;
		totalSize: number;
		webpFiles: number;
		legacyFiles: number;
		fileTypes: Record<string, number>;
	}

	let { onCleanupComplete }: Props = $props();

	// State
	let loading = $state(false);
	let stats: UploadStats | null = $state(null);
	let cleanupResult: CleanupResult | null = $state(null);
	let showConfirmation = $state(false);

	// Load stats on component mount
	$effect(() => {
		loadStats();
	});

	/**
	 * Load current upload directory statistics
	 */
	async function loadStats() {
		loading = true;
		try {
			const response = await fetch(`${config.api.url}/uploads/stats`);
			if (response.ok) {
				const data = await response.json();
				stats = data.stats;
			}
		} catch (error) {
			console.error('Failed to load stats:', error);
		} finally {
			loading = false;
		}
	}

	/**
	 * Perform cleanup with dry run first
	 */
	async function performCleanup(dryRun: boolean = true) {
		loading = true;
		cleanupResult = null;

		try {
			const response = await fetch(`${config.api.url}/uploads/cleanup`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ dry_run: dryRun }),
			});

			if (!response.ok) {
				throw new Error(`Cleanup failed: ${response.statusText}`);
			}

			const result = await response.json();
			cleanupResult = result;

			// Reload stats after actual cleanup
			if (!dryRun) {
				await loadStats();
			}

			// Call completion callback
			if (onCleanupComplete) {
				onCleanupComplete(result);
			}

		} catch (error) {
			console.error('Cleanup failed:', error);
			cleanupResult = {
				success: false,
				message: error instanceof Error ? error.message : 'Cleanup failed',
				files_removed: [],
				total_removed: 0,
				dry_run: dryRun,
			};
		} finally {
			loading = false;
			showConfirmation = false;
		}
	}

	/**
	 * Format file size for display
	 */
	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 B';
		const k = 1024;
		const sizes = ['B', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
	}

	/**
	 * Get file type color for display
	 */
	function getFileTypeColor(extension: string): string {
		switch (extension) {
			case 'webp': return '#10b981'; // green
			case 'jpg':
			case 'jpeg': return '#f59e0b'; // yellow
			case 'png': return '#3b82f6'; // blue
			case 'gif': return '#8b5cf6'; // purple
			case 'bmp': return '#ef4444'; // red
			default: return '#6b7280'; // gray
		}
	}
</script>

<div class="image-cleanup">
	<div class="header">
		<h2>Image Cleanup Utility</h2>
		<p>Remove legacy non-WebP images from the products directory</p>
	</div>

	<!-- Loading State -->
	{#if loading && !stats}
		<div class="loading">
			<div class="spinner"></div>
			<p>Loading statistics...</p>
		</div>
	{/if}

	<!-- Statistics Display -->
	{#if stats}
		<div class="stats-section">
			<h3>Current Directory Statistics</h3>
			<div class="stats-grid">
				<div class="stat-card">
					<div class="stat-number">{stats.totalFiles}</div>
					<div class="stat-label">Total Files</div>
				</div>
				<div class="stat-card">
					<div class="stat-number">{formatFileSize(stats.totalSize)}</div>
					<div class="stat-label">Total Size</div>
				</div>
				<div class="stat-card success">
					<div class="stat-number">{stats.webpFiles}</div>
					<div class="stat-label">WebP Files</div>
				</div>
				<div class="stat-card warning">
					<div class="stat-number">{stats.legacyFiles}</div>
					<div class="stat-label">Legacy Files</div>
				</div>
			</div>

			{#if Object.keys(stats.fileTypes).length > 0}
				<div class="file-types">
					<h4>File Types Breakdown</h4>
					<div class="file-type-list">
						{#each Object.entries(stats.fileTypes) as [extension, count]}
							<div class="file-type-item">
								<div 
									class="file-type-indicator" 
									style="background-color: {getFileTypeColor(extension)}"
								></div>
								<span class="file-type-ext">.{extension}</span>
								<span class="file-type-count">{count} files</span>
							</div>
						{/each}
					</div>
				</div>
			{/if}
		</div>
	{/if}

	<!-- Cleanup Actions -->
	{#if stats && stats.legacyFiles > 0}
		<div class="actions-section">
			<h3>Cleanup Actions</h3>
			<p class="warning-text">
				⚠️ This will permanently remove {stats.legacyFiles} legacy image files. 
				WebP files will be preserved.
			</p>
			
			<div class="action-buttons">
				<button 
					class="btn btn-secondary"
					onclick={() => performCleanup(true)}
					disabled={loading}
				>
					{loading ? 'Processing...' : 'Preview Cleanup (Dry Run)'}
				</button>
				
				<button 
					class="btn btn-danger"
					onclick={() => showConfirmation = true}
					disabled={loading}
				>
					Perform Cleanup
				</button>
			</div>
		</div>
	{:else if stats && stats.legacyFiles === 0}
		<div class="no-cleanup">
			<div class="success-icon">✅</div>
			<h3>No Cleanup Needed</h3>
			<p>All product images are already in WebP format. No legacy files found.</p>
		</div>
	{/if}

	<!-- Confirmation Dialog -->
	{#if showConfirmation}
		<div class="confirmation-overlay">
			<div class="confirmation-dialog">
				<h3>Confirm Cleanup</h3>
				<p>
					Are you sure you want to permanently delete {stats?.legacyFiles} legacy image files?
					This action cannot be undone.
				</p>
				<div class="confirmation-buttons">
					<button 
						class="btn btn-secondary"
						onclick={() => showConfirmation = false}
					>
						Cancel
					</button>
					<button 
						class="btn btn-danger"
						onclick={() => performCleanup(false)}
						disabled={loading}
					>
						{loading ? 'Deleting...' : 'Yes, Delete Files'}
					</button>
				</div>
			</div>
		</div>
	{/if}

	<!-- Cleanup Results -->
	{#if cleanupResult}
		<div class="results-section">
			<h3>Cleanup Results</h3>
			<div class="result-card {cleanupResult.success ? 'success' : 'error'}">
				<div class="result-header">
					<div class="result-icon">
						{cleanupResult.success ? '✅' : '❌'}
					</div>
					<div class="result-message">
						{cleanupResult.message}
					</div>
				</div>
				
				{#if cleanupResult.files_removed.length > 0}
					<div class="removed-files">
						<h4>
							{cleanupResult.dry_run ? 'Files that would be removed:' : 'Files removed:'}
						</h4>
						<div class="file-list">
							{#each cleanupResult.files_removed.slice(0, 10) as file}
								<div class="file-item">{file}</div>
							{/each}
							{#if cleanupResult.files_removed.length > 10}
								<div class="file-item more">
									... and {cleanupResult.files_removed.length - 10} more files
								</div>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		</div>
	{/if}

	<!-- Refresh Button -->
	<div class="refresh-section">
		<button 
			class="btn btn-primary"
			onclick={loadStats}
			disabled={loading}
		>
			{loading ? 'Refreshing...' : 'Refresh Statistics'}
		</button>
	</div>
</div>

<style>
	.image-cleanup {
		max-width: 800px;
		margin: 0 auto;
		padding: 1rem;
	}

	.header {
		text-align: center;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 1px solid #e5e7eb;
	}

	.header h2 {
		margin: 0 0 0.5rem;
		color: #111827;
		font-size: 1.5rem;
		font-weight: 600;
	}

	.header p {
		margin: 0;
		color: #6b7280;
	}

	.loading {
		text-align: center;
		padding: 2rem;
	}

	.spinner {
		width: 2rem;
		height: 2rem;
		border: 2px solid #e5e7eb;
		border-top: 2px solid #3b82f6;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 1rem;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.stats-section {
		margin-bottom: 2rem;
		padding: 1.5rem;
		background: #f9fafb;
		border-radius: 0.75rem;
		border: 1px solid #e5e7eb;
	}

	.stats-section h3 {
		margin: 0 0 1rem;
		color: #111827;
		font-size: 1.25rem;
		font-weight: 600;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
		gap: 1rem;
		margin-bottom: 1.5rem;
	}

	.stat-card {
		padding: 1rem;
		background: white;
		border-radius: 0.5rem;
		border: 1px solid #e5e7eb;
		text-align: center;
	}

	.stat-card.success {
		border-color: #10b981;
		background: #f0fdf4;
	}

	.stat-card.warning {
		border-color: #f59e0b;
		background: #fffbeb;
	}

	.stat-number {
		font-size: 1.5rem;
		font-weight: 700;
		color: #111827;
		margin-bottom: 0.25rem;
	}

	.stat-label {
		font-size: 0.875rem;
		color: #6b7280;
	}

	.file-types h4 {
		margin: 0 0 0.75rem;
		color: #374151;
		font-size: 1rem;
		font-weight: 600;
	}

	.file-type-list {
		display: flex;
		flex-wrap: wrap;
		gap: 0.75rem;
	}

	.file-type-item {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem 0.75rem;
		background: white;
		border-radius: 0.375rem;
		border: 1px solid #e5e7eb;
		font-size: 0.875rem;
	}

	.file-type-indicator {
		width: 0.75rem;
		height: 0.75rem;
		border-radius: 50%;
	}

	.file-type-ext {
		font-weight: 600;
		color: #374151;
	}

	.file-type-count {
		color: #6b7280;
	}

	.actions-section {
		margin-bottom: 2rem;
		padding: 1.5rem;
		background: #fef3c7;
		border-radius: 0.75rem;
		border: 1px solid #f59e0b;
	}

	.actions-section h3 {
		margin: 0 0 1rem;
		color: #92400e;
		font-size: 1.25rem;
		font-weight: 600;
	}

	.warning-text {
		margin: 0 0 1.5rem;
		color: #92400e;
		font-weight: 500;
	}

	.action-buttons {
		display: flex;
		gap: 1rem;
		justify-content: center;
	}

	.no-cleanup {
		text-align: center;
		padding: 2rem;
		background: #f0fdf4;
		border-radius: 0.75rem;
		border: 1px solid #10b981;
		margin-bottom: 2rem;
	}

	.success-icon {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.no-cleanup h3 {
		margin: 0 0 0.5rem;
		color: #059669;
		font-size: 1.25rem;
		font-weight: 600;
	}

	.no-cleanup p {
		margin: 0;
		color: #065f46;
	}

	.confirmation-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.confirmation-dialog {
		background: white;
		padding: 2rem;
		border-radius: 0.75rem;
		max-width: 400px;
		margin: 1rem;
		box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
	}

	.confirmation-dialog h3 {
		margin: 0 0 1rem;
		color: #111827;
		font-size: 1.25rem;
		font-weight: 600;
	}

	.confirmation-dialog p {
		margin: 0 0 1.5rem;
		color: #6b7280;
		line-height: 1.6;
	}

	.confirmation-buttons {
		display: flex;
		gap: 1rem;
		justify-content: flex-end;
	}

	.results-section {
		margin-bottom: 2rem;
	}

	.results-section h3 {
		margin: 0 0 1rem;
		color: #111827;
		font-size: 1.25rem;
		font-weight: 600;
	}

	.result-card {
		padding: 1.5rem;
		border-radius: 0.75rem;
		border: 1px solid;
	}

	.result-card.success {
		background: #f0fdf4;
		border-color: #10b981;
	}

	.result-card.error {
		background: #fef2f2;
		border-color: #ef4444;
	}

	.result-header {
		display: flex;
		align-items: center;
		gap: 0.75rem;
		margin-bottom: 1rem;
	}

	.result-icon {
		font-size: 1.25rem;
	}

	.result-message {
		font-weight: 600;
		color: #111827;
	}

	.removed-files h4 {
		margin: 0 0 0.75rem;
		color: #374151;
		font-size: 1rem;
		font-weight: 600;
	}

	.file-list {
		max-height: 200px;
		overflow-y: auto;
		background: white;
		border-radius: 0.5rem;
		border: 1px solid #e5e7eb;
	}

	.file-item {
		padding: 0.5rem 0.75rem;
		border-bottom: 1px solid #f3f4f6;
		font-size: 0.875rem;
		color: #374151;
		font-family: monospace;
	}

	.file-item:last-child {
		border-bottom: none;
	}

	.file-item.more {
		font-style: italic;
		color: #6b7280;
		text-align: center;
	}

	.refresh-section {
		text-align: center;
		padding-top: 1rem;
		border-top: 1px solid #e5e7eb;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		border: none;
		font-size: 0.875rem;
	}

	.btn:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.btn-primary {
		background: #3b82f6;
		color: white;
	}

	.btn-primary:hover:not(:disabled) {
		background: #2563eb;
	}

	.btn-secondary {
		background: #6b7280;
		color: white;
	}

	.btn-secondary:hover:not(:disabled) {
		background: #4b5563;
	}

	.btn-danger {
		background: #ef4444;
		color: white;
	}

	.btn-danger:hover:not(:disabled) {
		background: #dc2626;
	}

	@media (max-width: 768px) {
		.stats-grid {
			grid-template-columns: repeat(2, 1fr);
		}

		.action-buttons,
		.confirmation-buttons {
			flex-direction: column;
		}

		.file-type-list {
			flex-direction: column;
		}
	}
</style>
