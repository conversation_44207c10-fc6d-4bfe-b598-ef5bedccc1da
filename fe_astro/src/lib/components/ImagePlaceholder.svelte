<!--
@component ImagePlaceholder
Fallback component for when images fail to load
Displays a nice placeholder with icon and text
-->

<script lang="ts">
	interface Props {
		/** Alt text for the placeholder */
		alt?: string;
		/** Additional CSS classes */
		className?: string;
		/** Size of the icon (default: 'md') */
		iconSize?: 'sm' | 'md' | 'lg' | 'xl';
		/** Show text below icon */
		showText?: boolean;
		/** Custom text to display */
		text?: string;
		/** Background color theme */
		theme?: 'gray' | 'blue' | 'green' | 'yellow' | 'red';
	}

	let {
		alt = 'Image not available',
		className = '',
		iconSize = 'md',
		showText = true,
		text = 'Gambar tidak tersedia',
		theme = 'gray'
	}: Props = $props();

	// Icon size classes
	const iconSizes = {
		sm: 'w-6 h-6',
		md: 'w-8 h-8',
		lg: 'w-12 h-12',
		xl: 'w-16 h-16'
	};

	// Theme classes
	const themes = {
		gray: {
			bg: 'bg-gray-100',
			icon: 'text-gray-400',
			text: 'text-gray-500'
		},
		blue: {
			bg: 'bg-blue-50',
			icon: 'text-blue-400',
			text: 'text-blue-500'
		},
		green: {
			bg: 'bg-green-50',
			icon: 'text-green-400',
			text: 'text-green-500'
		},
		yellow: {
			bg: 'bg-yellow-50',
			icon: 'text-yellow-400',
			text: 'text-yellow-500'
		},
		red: {
			bg: 'bg-red-50',
			icon: 'text-red-400',
			text: 'text-red-500'
		}
	};

	const currentTheme = themes[theme];
</script>

<div 
	class="w-full h-full flex flex-col items-center justify-center {currentTheme.bg} {className}"
	role="img"
	aria-label={alt}
>
	<!-- Image Icon -->
	<svg 
		class="{iconSizes[iconSize]} {currentTheme.icon} {showText ? 'mb-2' : ''}" 
		fill="none" 
		stroke="currentColor" 
		viewBox="0 0 24 24"
		aria-hidden="true"
	>
		<path 
			stroke-linecap="round" 
			stroke-linejoin="round" 
			stroke-width="2" 
			d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
		></path>
	</svg>
	
	<!-- Text -->
	{#if showText}
		<span class="text-xs text-center {currentTheme.text} px-2">
			{text}
		</span>
	{/if}
</div>
