<script>
	import { uploadMultipleProductImages, deleteProductImage } from '$lib/api.js';

	/**
	 * MultiImageUpload Component
	 * Handles uploading multiple images with aspect-specific naming
	 */

	// Props
	export let aspect = '4x5'; // '4x5' or '16x9'
	export let productSlug = '';
	export let label = 'Upload Images';
	export let maxFiles = 10;
	export let existingImages = []; // Array of existing image objects

	// Internal state
	let fileInput;
	let selectedFiles = [];
	let uploading = false;
	let uploadProgress = { current: 0, total: 0 };
	let uploadResults = [];
	let dragOver = false;

	// Reactive statements
	$: aspectLabel = aspect === '4x5' ? 'Mobile (4:5)' : 'Desktop (16:9)';
	$: canUpload = productSlug && selectedFiles.length > 0 && !uploading;

	// File selection handler
	function handleFileSelect(event) {
		const files = Array.from(event.target.files || []);
		addFiles(files);
	}

	// Add files to selection
	function addFiles(files) {
		const validFiles = files.filter(file => {
			// Validate file type
			if (!file.type.startsWith('image/')) {
				alert(`${file.name} is not an image file`);
				return false;
			}
			
			// Validate file size (5MB max)
			if (file.size > 5 * 1024 * 1024) {
				alert(`${file.name} is too large (max 5MB)`);
				return false;
			}
			
			return true;
		});

		// Add to selected files (up to max limit)
		const remainingSlots = maxFiles - selectedFiles.length;
		const filesToAdd = validFiles.slice(0, remainingSlots);
		
		selectedFiles = [...selectedFiles, ...filesToAdd];
		
		if (validFiles.length > remainingSlots) {
			alert(`Only ${remainingSlots} more files can be added (max ${maxFiles} total)`);
		}
	}

	// Remove file from selection
	function removeFile(index) {
		selectedFiles = selectedFiles.filter((_, i) => i !== index);
	}

	// Clear all selected files
	function clearFiles() {
		selectedFiles = [];
		uploadResults = [];
		if (fileInput) {
			fileInput.value = '';
		}
	}

	// Upload all selected files
	async function uploadFiles() {
		if (!canUpload) return;

		uploading = true;
		uploadResults = [];

		try {
			const results = await uploadMultipleProductImages(
				productSlug,
				selectedFiles,
				(current, total) => {
					uploadProgress = { current, total };
				}
			);

			uploadResults = results;
			
			// Clear successful uploads from selection
			const successfulUploads = results.filter(r => r.success);
			if (successfulUploads.length > 0) {
				// Emit event for parent component
				const event = new CustomEvent('upload-complete', {
					detail: { results: successfulUploads, aspect }
				});
				document.dispatchEvent(event);
				
				// Clear selected files if all were successful
				if (successfulUploads.length === selectedFiles.length) {
					clearFiles();
				}
			}
		} catch (error) {
			console.error('Upload failed:', error);
			alert(`Upload failed: ${error.message}`);
		} finally {
			uploading = false;
			uploadProgress = { current: 0, total: 0 };
		}
	}

	// Delete existing image
	async function deleteExistingImage(filename) {
		if (!confirm(`Delete ${filename}?`)) return;

		try {
			await deleteProductImage(filename);
			
			// Emit event for parent component
			const event = new CustomEvent('image-deleted', {
				detail: { filename, aspect }
			});
			document.dispatchEvent(event);
		} catch (error) {
			console.error('Delete failed:', error);
			alert(`Delete failed: ${error.message}`);
		}
	}

	// Drag and drop handlers
	function handleDragOver(event) {
		event.preventDefault();
		dragOver = true;
	}

	function handleDragLeave(event) {
		event.preventDefault();
		dragOver = false;
	}

	function handleDrop(event) {
		event.preventDefault();
		dragOver = false;
		
		const files = Array.from(event.dataTransfer.files);
		addFiles(files);
	}

	// Format file size
	function formatFileSize(bytes) {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}
</script>

<div class="multi-image-upload">
	<!-- Header -->
	<div class="flex items-center justify-between mb-4">
		<h3 class="text-lg font-semibold text-gray-900">
			{label} - {aspectLabel}
		</h3>
		<div class="text-sm text-gray-500">
			Max {maxFiles} files, 5MB each
		</div>
	</div>

	<!-- Existing Images -->
	{#if existingImages.length > 0}
		<div class="mb-6">
			<h4 class="text-sm font-medium text-gray-700 mb-2">Existing Images</h4>
			<div class="grid grid-cols-2 md:grid-cols-4 gap-4">
				{#each existingImages as image}
					<div class="relative group">
						<img 
							src={image.url} 
							alt="Existing image" 
							class="w-full h-24 object-cover rounded-lg border border-gray-200"
						>
						<button
							on:click={() => deleteExistingImage(image.filename)}
							class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
						>
							×
						</button>
						<div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b-lg truncate">
							{image.filename}
						</div>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- File Upload Area -->
	<div 
		class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center transition-colors duration-200 {dragOver ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-400'}"
		on:dragover={handleDragOver}
		on:dragleave={handleDragLeave}
		on:drop={handleDrop}
		role="button"
		tabindex="0"
		on:click={() => fileInput?.click()}
		on:keydown={(e) => e.key === 'Enter' && fileInput?.click()}
	>
		<input
			bind:this={fileInput}
			type="file"
			multiple
			accept="image/jpeg,image/png,image/webp"
			on:change={handleFileSelect}
			class="hidden"
		>
		
		<div class="space-y-2">
			<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
			</svg>
			<div class="text-gray-600">
				<span class="font-medium text-blue-600 hover:text-blue-500 cursor-pointer">
					Click to upload
				</span>
				or drag and drop
			</div>
			<p class="text-xs text-gray-500">
				PNG, JPG, WebP up to 5MB each
			</p>
		</div>
	</div>

	<!-- Selected Files -->
	{#if selectedFiles.length > 0}
		<div class="mt-6">
			<div class="flex items-center justify-between mb-3">
				<h4 class="text-sm font-medium text-gray-700">
					Selected Files ({selectedFiles.length})
				</h4>
				<button
					on:click={clearFiles}
					class="text-sm text-red-600 hover:text-red-800"
					disabled={uploading}
				>
					Clear All
				</button>
			</div>
			
			<div class="space-y-2 max-h-40 overflow-y-auto">
				{#each selectedFiles as file, index}
					<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
						<div class="flex items-center space-x-3">
							<div class="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
								<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
								</svg>
							</div>
							<div>
								<div class="text-sm font-medium text-gray-900">{file.name}</div>
								<div class="text-xs text-gray-500">{formatFileSize(file.size)}</div>
							</div>
						</div>
						<button
							on:click={() => removeFile(index)}
							class="text-red-600 hover:text-red-800"
							disabled={uploading}
						>
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
							</svg>
						</button>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Upload Progress -->
	{#if uploading}
		<div class="mt-4">
			<div class="flex items-center justify-between mb-2">
				<span class="text-sm font-medium text-gray-700">Uploading...</span>
				<span class="text-sm text-gray-500">
					{uploadProgress.current} / {uploadProgress.total}
				</span>
			</div>
			<div class="w-full bg-gray-200 rounded-full h-2">
				<div 
					class="bg-blue-600 h-2 rounded-full transition-all duration-300"
					style="width: {uploadProgress.total > 0 ? (uploadProgress.current / uploadProgress.total) * 100 : 0}%"
				></div>
			</div>
		</div>
	{/if}

	<!-- Upload Results -->
	{#if uploadResults.length > 0}
		<div class="mt-4">
			<h4 class="text-sm font-medium text-gray-700 mb-2">Upload Results</h4>
			<div class="space-y-1">
				{#each uploadResults as result}
					<div class="flex items-center justify-between p-2 rounded {result.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}">
						<span class="text-sm">
							{result.filename || result.error}
						</span>
						<span class="text-xs">
							{result.success ? '✓' : '✗'}
						</span>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Upload Button -->
	<div class="mt-6">
		<button
			on:click={uploadFiles}
			disabled={!canUpload}
			class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
		>
			{#if uploading}
				<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" fill="none" viewBox="0 0 24 24">
					<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
					<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
				</svg>
				Uploading...
			{:else}
				Upload {selectedFiles.length} Image{selectedFiles.length !== 1 ? 's' : ''}
			{/if}
		</button>
	</div>
</div>
