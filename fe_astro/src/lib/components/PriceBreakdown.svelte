<script lang="ts">
	/**
	 * PriceBreakdown Component
	 * Displays detailed price breakdown with real-time calculations
	 * 
	 * @component
	 * @example
	 * <PriceBreakdown 
	 *   basePrice={1590000}
	 *   accessoriesTotal={500000}
	 *   finalTotal={2090000}
	 *   selectedAccessories={accessoriesMap}
	 *   quantity={2}
	 * />
	 */
	
	// Accessory interface
	interface Accessory {
		id: string;
		name: string;
		description?: string;
		price: number;
		category: string;
		image?: string;
		maxQuantity?: number;
	}
	
	// Props interface
	interface Props {
		/** Base price (size price × quantity) */
		basePrice: number;
		/** Total accessories price */
		accessoriesTotal: number;
		/** Final total price */
		finalTotal: number;
		/** Map of selected accessories with quantities */
		selectedAccessories: Map<string, {accessory: Accessory, quantity: number}>;
		/** Main product quantity */
		quantity: number;
		/** Whether to show detailed breakdown */
		showBreakdown?: boolean;
		/** Currency symbol */
		currency?: string;
	}
	
	let { 
		basePrice, 
		accessoriesTotal, 
		finalTotal, 
		selectedAccessories, 
		quantity,
		showBreakdown = true,
		currency = 'Rp'
	}: Props = $props();
	
	// Component state
	let isExpanded = $state(false);
	let previousTotal = $state(finalTotal);
	let showPriceAnimation = $state(false);
	
	// Watch for price changes to trigger animation
	$effect(() => {
		if (finalTotal !== previousTotal) {
			showPriceAnimation = true;
			previousTotal = finalTotal;
			
			// Reset animation after duration
			setTimeout(() => {
				showPriceAnimation = false;
			}, 500);
		}
	});
	
	// Derived values
	let hasAccessories = $derived(() => selectedAccessories.size > 0);
	let accessoriesArray = $derived(() => Array.from(selectedAccessories.values()).filter(item => item.quantity > 0));
	
	/**
	 * Format price to Indonesian Rupiah
	 * @param price - Price in number format
	 * @returns Formatted price string
	 */
	function formatPrice(price: number): string {
		return new Intl.NumberFormat('id-ID').format(price);
	}
	
	/**
	 * Format price with currency symbol
	 * @param price - Price in number format
	 * @returns Formatted price string with currency
	 */
	function formatPriceWithCurrency(price: number): string {
		return `${currency} ${formatPrice(price)}`;
	}
	
	/**
	 * Calculate unit price (price per single item)
	 * @param totalPrice - Total price
	 * @param qty - Quantity
	 * @returns Unit price
	 */
	function calculateUnitPrice(totalPrice: number, qty: number): number {
		return qty > 0 ? totalPrice / qty : 0;
	}
	
	/**
	 * Toggle breakdown visibility
	 */
	function toggleBreakdown() {
		isExpanded = !isExpanded;
	}
	
	/**
	 * Get price change indicator
	 * @returns Price change direction
	 */
	function getPriceChangeDirection(): 'up' | 'down' | 'none' {
		if (finalTotal > previousTotal) return 'up';
		if (finalTotal < previousTotal) return 'down';
		return 'none';
	}
</script>

<!-- Price Calculator Container -->
<div class="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
	<!-- Main Total Display -->
	<div class="flex items-center justify-between">
		<div>
			<h3 class="text-lg font-semibold text-gray-900">Total Price</h3>
			{#if quantity > 1}
				<p class="text-sm text-gray-600">For {quantity} items</p>
			{/if}
		</div>
		
		<div class="text-right">
			<div 
				class="flex items-baseline space-x-1 transition-all duration-500"
				class:scale-110={showPriceAnimation}
				class:text-green-600={showPriceAnimation && getPriceChangeDirection() === 'down'}
				class:text-blue-600={showPriceAnimation && getPriceChangeDirection() === 'up'}
			>
				<span class="text-sm text-gray-600">{currency}</span>
				<span class="text-2xl font-bold text-gray-900">
					{formatPrice(finalTotal)}
				</span>
			</div>
			
			<!-- Unit Price Display -->
			{#if quantity > 1}
				<div class="text-sm text-gray-500">
					{formatPriceWithCurrency(calculateUnitPrice(finalTotal, quantity))} per item
				</div>
			{/if}
		</div>
	</div>
	
	<!-- Price Change Indicator -->
	{#if showPriceAnimation && getPriceChangeDirection() !== 'none'}
		<div class="flex items-center justify-center text-sm">
			{#if getPriceChangeDirection() === 'up'}
				<div class="flex items-center space-x-1 text-blue-600">
					<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
					</svg>
					<span>Price updated</span>
				</div>
			{:else}
				<div class="flex items-center space-x-1 text-green-600">
					<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
					</svg>
					<span>Price reduced</span>
				</div>
			{/if}
		</div>
	{/if}
	
	<!-- Breakdown Toggle -->
	{#if showBreakdown && (hasAccessories || quantity > 1)}
		<button
			class="w-full flex items-center justify-between text-sm text-gray-600 hover:text-gray-900 transition-colors duration-200"
			onclick={toggleBreakdown}
			aria-expanded={isExpanded}
			aria-label="Toggle price breakdown"
		>
			<span>Price Breakdown</span>
			<svg 
				class="w-4 h-4 transition-transform duration-200"
				class:rotate-180={isExpanded}
				fill="none" 
				stroke="currentColor" 
				viewBox="0 0 24 24"
			>
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
			</svg>
		</button>
	{/if}
	
	<!-- Detailed Breakdown -->
	{#if showBreakdown && isExpanded}
		<div class="border-t pt-4 space-y-3">
			<!-- Base Price -->
			<div class="flex items-center justify-between text-sm">
				<div class="flex items-center space-x-2">
					<span class="w-2 h-2 bg-blue-500 rounded-full"></span>
					<span class="text-gray-700">
						Base Price
						{#if quantity > 1}
							<span class="text-gray-500">(× {quantity})</span>
						{/if}
					</span>
				</div>
				<span class="font-medium text-gray-900">
					{formatPriceWithCurrency(basePrice)}
				</span>
			</div>
			
			<!-- Accessories Breakdown -->
			{#if hasAccessories}
				<div class="space-y-2">
					<div class="text-sm font-medium text-gray-700">Accessories:</div>
					{#each accessoriesArray as item}
						<div class="flex items-center justify-between text-sm ml-4">
							<div class="flex items-center space-x-2">
								<span class="w-2 h-2 bg-green-500 rounded-full"></span>
								<span class="text-gray-600">
									{item.accessory.name}
									<span class="text-gray-500">
										(× {item.quantity}
										{#if quantity > 1}
											× {quantity}
										{/if})
									</span>
								</span>
							</div>
							<span class="font-medium text-gray-900">
								{formatPriceWithCurrency(item.accessory.price * item.quantity * quantity)}
							</span>
						</div>
					{/each}
					
					<!-- Accessories Subtotal -->
					<div class="flex items-center justify-between text-sm ml-4 pt-2 border-t border-gray-100">
						<span class="font-medium text-gray-700">Accessories Total:</span>
						<span class="font-semibold text-gray-900">
							{formatPriceWithCurrency(accessoriesTotal)}
						</span>
					</div>
				</div>
			{/if}
			
			<!-- Final Total -->
			<div class="flex items-center justify-between text-base font-semibold pt-3 border-t border-gray-200">
				<span class="text-gray-900">Grand Total:</span>
				<span class="text-gray-900">
					{formatPriceWithCurrency(finalTotal)}
				</span>
			</div>
		</div>
	{/if}
	
	<!-- Quick Summary for Mobile -->
	{#if !isExpanded && hasAccessories}
		<div class="text-xs text-gray-500 text-center">
			Base: {formatPriceWithCurrency(basePrice)} + 
			Accessories: {formatPriceWithCurrency(accessoriesTotal)}
		</div>
	{/if}
	
	<!-- Payment Methods Info -->
	<div class="text-xs text-gray-500 space-y-1">
		<p>💳 <strong>Payment:</strong> Cash, Transfer, or Installments available</p>
		<p>🚚 <strong>Delivery:</strong> Free consultation and measurement</p>
		<p>⚡ <strong>Installation:</strong> Professional installation included</p>
	</div>
</div>

<!-- Accessibility Live Region -->
<div class="sr-only" aria-live="polite">
	Total price updated to {formatPriceWithCurrency(finalTotal)}.
	{#if quantity > 1}
		That's {formatPriceWithCurrency(calculateUnitPrice(finalTotal, quantity))} per item.
	{/if}
</div>

<style>
	/* Smooth transitions for all interactive elements */
	button, div {
		transition-property: all;
		transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	}
	
	/* Price animation */
	@keyframes priceUpdate {
		0% { transform: scale(1); }
		50% { transform: scale(1.05); }
		100% { transform: scale(1); }
	}
	
	.scale-110 {
		animation: priceUpdate 0.5s ease-in-out;
	}
	
	/* Ensure proper contrast for accessibility */
	@media (prefers-reduced-motion: reduce) {
		* {
			transition-duration: 0.01ms !important;
			animation-duration: 0.01ms !important;
		}
		
		.scale-110 {
			animation: none;
			transform: none;
		}
	}
	
	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.border {
			border-width: 2px;
		}
	}
	
	/* Focus styles for accessibility */
	button:focus-visible {
		outline: 2px solid #3B82F6;
		outline-offset: 2px;
	}
</style>
