<script lang="ts">
	/**
	 * ProductCard Component
	 * Displays a product card with responsive image, name, price, and category
	 * Links to the product detail page
	 * Uses DynamicImage component for responsive image loading
	 *
	 * @component
	 * @example
	 * <ProductCard
	 *   id="fantasy-tv-cabinet"
	 *   name="Fantasy TV Cabinet"
	 *   slug="fantasy-tv-cabinet"
	 *   price={1590000}
	 *   category="TV Cabinet"
	 *   shortDescription="Modern TV cabinet with premium finish"
	 * />
	 */

	import { generateProductSlug } from '$lib/api';
	import DynamicImageSingle from './DynamicImageSingle.svelte';

	// Props interface
	interface Props {
		/** Product ID for identification (legacy support) */
		id: string;
		/** Product display name */
		name: string;
		/** Product slug for routing and image loading (optional, will be generated from name if not provided) */
		slug?: string;
		/** Base price for display */
		price: number;
		/** Product category */
		category: string;
		/** Optional brief description */
		shortDescription?: string;
	}

	let { id, name, slug, price, category, shortDescription }: Props = $props();

	// Generate slug from name if not provided
	const productSlug = slug || generateProductSlug(name);
	
	// Component state
	let isHovered = $state(false);

	/**
	 * Format price to Indonesian Rupiah
	 * @param price - Price in number format
	 * @returns Formatted price string
	 */
	function formatPrice(price: number): string {
		return new Intl.NumberFormat('id-ID').format(price);
	}
</script>

<!-- Product Card Container -->
<a
	href="/products/{productSlug}"
	class="group block rounded-2xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden"
	onmouseenter={() => isHovered = true}
	onmouseleave={() => isHovered = false}
	aria-label="View {name} details"
>
	<!-- Product Image -->
	<div class="relative aspect-[4:5] lg:aspect-[16/9] overflow-hidden bg-gray-100 rounded-2xl">
		<DynamicImageSingle
			slug={productSlug}
			index={1}
			alt={name}
			className="w-full h-full transition-transform duration-300 group-hover:scale-105"
			rounded="2xl"
		/>


		<!-- Category Badge Overlay -->
		<!-- <div class="absolute top-3 left-3 z-20">
			<span class="bg-white bg-opacity-90 text-gray-700 text-xs font-medium px-2 py-1 rounded-full">
				{category}
			</span>
		</div> -->

		<!-- View Details Overlay -->
		{#if isHovered}
			<div class="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 pointer-events-none">
				<div class="bg-white bg-opacity-90 text-gray-900 px-4 py-2 rounded-full text-sm font-medium">
					View Details
				</div>
			</div>
		{/if}
	</div>

	<!-- Product Information -->
	<div class="p-4 space-y-3">
		<!-- Product Name -->
		<h3 class="text-lg text-center font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
			{name}
		</h3>

		<!-- Short Description -->
		<!-- {#if shortDescription}
			<p class="text-sm text-gray-600 line-clamp-2">
				{shortDescription}
			</p>
		{/if} -->

		<!-- Price -->
		<!-- <div class="flex items-center justify-between">
			<div class="flex items-baseline space-x-1">
				<span class="text-xs text-gray-500">Starting from</span>
			</div>
			<div class="flex items-baseline space-x-1">
				<span class="text-sm text-gray-600">Rp</span>
				<span class="text-xl font-bold text-gray-900">
					{formatPrice(price)}
				</span>
			</div>
		</div> -->

		<!-- Call to Action -->
		<!-- <div class="pt-2">
			<div class="w-full bg-blue-600 text-white text-center py-2 rounded-lg font-medium text-sm group-hover:bg-blue-700 transition-colors duration-200">
				View Details
			</div>
		</div> -->
	</div>
</a>

<style>
	/* Line clamp utilities */
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* Smooth transitions for all interactive elements */
	a, div {
		transition-property: all;
		transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	}

	/* Ensure proper contrast for accessibility */
	@media (prefers-reduced-motion: reduce) {
		* {
			transition-duration: 0.01ms !important;
			animation-duration: 0.01ms !important;
		}
	}

	/* Focus styles for accessibility */
	a:focus-visible {
		outline: 2px solid #3B82F6;
		outline-offset: 2px;
	}
</style>
