<script>
	import { createEventDispatcher } from 'svelte';

	export let dimensions = [];
	export let productId = null;
	export let disabled = false;

	const dispatch = createEventDispatcher();

	let newDimension = {
		name: '',
		width: '',
		height: '',
		depth: '',
		unit: 'm',
		price_modifier_type: 'addition',
		price_modifier_value: 0,
		is_default: false
	};

	let showAddForm = false;
	let editingDimension = null;
	let loading = false;

	const priceModifierTypes = [
		{ value: 'addition', label: 'Tambah Harga (+)' },
		{ value: 'subtraction', label: 'Kurangi Harga (-)' },
		{ value: 'replacement', label: 'Ganti <PERSON> (=)' },
		{ value: 'percentage', label: 'Persentase (%)' }
	];

	const units = [
		{ value: 'm', label: 'Meter (m)' },
		{ value: 'cm', label: 'Centimeter (cm)' },
		{ value: 'mm', label: 'Millimeter (mm)' }
	];

	function resetForm() {
		newDimension = {
			name: '',
			width: '',
			height: '',
			depth: '',
			unit: 'm',
			price_modifier_type: 'addition',
			price_modifier_value: 0,
			is_default: false
		};
		editingDimension = null;
		showAddForm = false;
	}

	function startEdit(dimension) {
		editingDimension = { ...dimension };
		newDimension = { ...dimension };
		showAddForm = true;
	}

	function cancelEdit() {
		resetForm();
	}

	async function saveDimension() {
		if (!newDimension.name.trim()) {
			alert('Nama ukuran wajib diisi');
			return;
		}

		if (!newDimension.width || !newDimension.height) {
			alert('Lebar dan tinggi wajib diisi');
			return;
		}

		loading = true;
		try {
			const dimensionData = {
				...newDimension,
				name: newDimension.name.trim(),
				width: parseFloat(newDimension.width),
				height: parseFloat(newDimension.height),
				depth: newDimension.depth ? parseFloat(newDimension.depth) : null,
				price_modifier_value: parseFloat(newDimension.price_modifier_value) || 0
			};

			if (editingDimension) {
				// Update existing dimension
				dispatch('update', { id: editingDimension.id, data: dimensionData });
			} else {
				// Add new dimension
				dispatch('add', dimensionData);
			}

			resetForm();
		} catch (error) {
			alert('Gagal menyimpan ukuran: ' + error.message);
		} finally {
			loading = false;
		}
	}

	function deleteDimension(dimension) {
		if (confirm(`Hapus ukuran "${dimension.name}"?`)) {
			dispatch('delete', dimension.id);
		}
	}

	function setAsDefault(dimension) {
		dispatch('setDefault', dimension.id);
	}

	function formatPrice(value) {
		return new Intl.NumberFormat('id-ID').format(value);
	}

	function getPriceDisplay(dimension) {
		const value = dimension.price_modifier_value;
		switch (dimension.price_modifier_type) {
			case 'addition':
				return value > 0 ? `+Rp ${formatPrice(value)}` : 'Harga Dasar';
			case 'subtraction':
				return `-Rp ${formatPrice(value)}`;
			case 'replacement':
				return `Rp ${formatPrice(value)}`;
			case 'percentage':
				return `+${value}%`;
			default:
				return 'Harga Dasar';
		}
	}

	function getDimensionDisplay(dimension) {
		let display = `${dimension.width} x ${dimension.height}`;
		if (dimension.depth) {
			display += ` x ${dimension.depth}`;
		}
		display += ` ${dimension.unit}`;
		return display;
	}
</script>

<div class="space-y-4">
	<div class="flex items-center justify-between">
		<h3 class="text-lg font-semibold text-gray-900">Ukuran Produk</h3>
		<button
			type="button"
			on:click={() => showAddForm = true}
			class="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
			{disabled}
		>
			<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
			</svg>
			Tambah Ukuran
		</button>
	</div>

	<!-- Existing Dimensions List -->
	{#if dimensions.length > 0}
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			{#each dimensions as dimension (dimension.id)}
				<div class="border border-gray-200 rounded-lg p-4 {dimension.is_default ? 'ring-2 ring-blue-500 bg-blue-50' : 'bg-white'}">
					<div class="flex items-start justify-between">
						<div>
							<h4 class="font-medium text-gray-900">
								{dimension.name}
								{#if dimension.is_default}
									<span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Default</span>
								{/if}
							</h4>
							<p class="text-sm text-gray-600">{getDimensionDisplay(dimension)}</p>
							<p class="text-sm font-medium text-green-600">{getPriceDisplay(dimension)}</p>
						</div>
						<div class="flex items-center space-x-2">
							{#if !dimension.is_default}
								<button
									type="button"
									on:click={() => setAsDefault(dimension)}
									class="text-xs text-blue-600 hover:text-blue-800"
									title="Set as default"
								>
									Set Default
								</button>
							{/if}
							<button
								type="button"
								on:click={() => startEdit(dimension)}
								class="text-blue-600 hover:text-blue-800"
								title="Edit ukuran"
							>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
								</svg>
							</button>
							<button
								type="button"
								on:click={() => deleteDimension(dimension)}
								class="text-red-600 hover:text-red-800"
								title="Hapus ukuran"
							>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
								</svg>
							</button>
						</div>
					</div>
				</div>
			{/each}
		</div>
	{:else}
		<div class="text-center py-8 text-gray-500">
			<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4a2 2 0 012-2h8a2 2 0 012 2v4m0 0v8a2 2 0 01-2 2H6a2 2 0 01-2-2V8m0 0h12M8 12h8"></path>
			</svg>
			<p class="mt-2">Belum ada ukuran. Tambahkan ukuran pertama untuk produk ini.</p>
		</div>
	{/if}

	<!-- Add/Edit Dimension Form -->
	{#if showAddForm}
		<div class="border border-gray-200 rounded-lg p-6 bg-gray-50">
			<h4 class="text-lg font-medium text-gray-900 mb-4">
				{editingDimension ? 'Edit Ukuran' : 'Tambah Ukuran Baru'}
			</h4>
			
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- Dimension Name -->
				<div class="md:col-span-2">
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Nama Ukuran <span class="text-red-500">*</span>
					</label>
					<input
						type="text"
						bind:value={newDimension.name}
						placeholder="Contoh: 2.4 x 2.7m Standard, 3.0 x 2.7m Large"
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						required
					>
				</div>

				<!-- Width -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Lebar <span class="text-red-500">*</span>
					</label>
					<input
						type="number"
						bind:value={newDimension.width}
						placeholder="2.4"
						step="0.1"
						min="0"
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						required
					>
				</div>

				<!-- Height -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Tinggi <span class="text-red-500">*</span>
					</label>
					<input
						type="number"
						bind:value={newDimension.height}
						placeholder="2.7"
						step="0.1"
						min="0"
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						required
					>
				</div>

				<!-- Depth -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Kedalaman (Opsional)
					</label>
					<input
						type="number"
						bind:value={newDimension.depth}
						placeholder="0.6"
						step="0.1"
						min="0"
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					>
				</div>

				<!-- Unit -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Satuan
					</label>
					<select
						bind:value={newDimension.unit}
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					>
						{#each units as unit}
							<option value={unit.value}>{unit.label}</option>
						{/each}
					</select>
				</div>

				<!-- Price Modifier Type -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Jenis Modifikasi Harga
					</label>
					<select
						bind:value={newDimension.price_modifier_type}
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					>
						{#each priceModifierTypes as type}
							<option value={type.value}>{type.label}</option>
						{/each}
					</select>
				</div>

				<!-- Price Modifier Value -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Nilai Modifikasi
					</label>
					<input
						type="number"
						bind:value={newDimension.price_modifier_value}
						placeholder="0"
						min="0"
						step={newDimension.price_modifier_type === 'percentage' ? '1' : '1000'}
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					>
					<p class="mt-1 text-xs text-gray-500">
						{#if newDimension.price_modifier_type === 'percentage'}
							Masukkan persentase (contoh: 10 untuk 10%)
						{:else}
							Masukkan nilai dalam Rupiah
						{/if}
					</p>
				</div>

				<!-- Is Default -->
				<div class="md:col-span-2">
					<label class="flex items-center">
						<input
							type="checkbox"
							bind:checked={newDimension.is_default}
							class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
						>
						<span class="ml-2 text-sm text-gray-700">Set sebagai ukuran default</span>
					</label>
				</div>
			</div>

			<!-- Form Actions -->
			<div class="flex items-center justify-end space-x-3 mt-6">
				<button
					type="button"
					on:click={cancelEdit}
					class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
					disabled={loading}
				>
					Batal
				</button>
				<button
					type="button"
					on:click={saveDimension}
					class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50"
					disabled={loading || !newDimension.name.trim() || !newDimension.width || !newDimension.height}
				>
					{#if loading}
						<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
					{/if}
					{editingDimension ? 'Update' : 'Simpan'}
				</button>
			</div>
		</div>
	{/if}
</div>
