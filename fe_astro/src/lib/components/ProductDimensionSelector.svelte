<script lang="ts">
	import type { ProductDimension } from '$lib/types/product';
	import { formatPrice } from '$lib/api/products';

	interface Props {
		dimensions: ProductDimension[];
		selectedDimension?: ProductDimension;
		onSelect: (dimension: ProductDimension) => void;
		basePrice: number;
	}

	let { dimensions, selectedDimension = $bindable(), onSelect, basePrice }: Props = $props();

	function handleDimensionSelect(dimension: ProductDimension) {
		selectedDimension = dimension;
		onSelect(dimension);
	}

	function calculateDisplayPrice(dimension: ProductDimension): number {
		switch (dimension.price_modifier_type) {
			case 'addition':
				return basePrice + dimension.price_modifier_value;
			case 'subtraction':
				return basePrice - dimension.price_modifier_value;
			case 'replacement':
				return dimension.price_modifier_value;
			case 'percentage':
				return basePrice + (basePrice * dimension.price_modifier_value / 100);
			default:
				return basePrice;
		}
	}

	function getPriceModifierDisplay(dimension: ProductDimension): string {
		if (dimension.price_modifier_value === 0) {
			return '';
		}

		switch (dimension.price_modifier_type) {
			case 'addition':
				return `+${formatPrice(dimension.price_modifier_value)}`;
			case 'subtraction':
				return `-${formatPrice(dimension.price_modifier_value)}`;
			case 'replacement':
				return `${formatPrice(dimension.price_modifier_value)}`;
			case 'percentage':
				return `+${dimension.price_modifier_value}%`;
			default:
				return '';
		}
	}
</script>

<div class="dimension-selector">
	<h3 class="text-lg font-semibold mb-4">Pilih Ukuran</h3>
	
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
		{#each dimensions as dimension (dimension.id)}
			<button
				class="dimension-option {selectedDimension?.id === dimension.id ? 'selected' : ''}"
				onclick={() => handleDimensionSelect(dimension)}
			>
				<div class="dimension-info">
					<div class="dimension-name font-medium">{dimension.name}</div>
					<div class="dimension-size text-sm text-gray-600">{dimension.display_name}</div>
				</div>
				
				<div class="dimension-pricing">
					<div class="final-price font-semibold text-blue-600">
						{formatPrice(calculateDisplayPrice(dimension))}
					</div>
					{#if dimension.price_modifier_value !== 0}
						<div class="price-modifier text-xs text-gray-500">
							{getPriceModifierDisplay(dimension)}
						</div>
					{/if}
				</div>
			</button>
		{/each}
	</div>
</div>

<style>
	.dimension-selector {
		@apply mb-6;
	}

	.dimension-option {
		@apply border-2 border-gray-200 rounded-lg p-4 text-left transition-all duration-200 hover:border-blue-300 hover:shadow-md;
	}

	.dimension-option.selected {
		@apply border-blue-500 bg-blue-50 shadow-md;
	}

	.dimension-info {
		@apply mb-2;
	}

	.dimension-name {
		@apply text-gray-900;
	}

	.dimension-pricing {
		@apply flex items-center justify-between;
	}

	.final-price {
		@apply text-lg;
	}

	.price-modifier {
		@apply ml-2;
	}
</style>
