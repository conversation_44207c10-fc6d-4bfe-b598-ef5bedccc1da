<script lang="ts">
	/**
	 * ProductImageGallery Component
	 * Displays product images with zoom functionality and thumbnail navigation
	 * Uses DynamicImage component for responsive image loading
	 *
	 * @component
	 * @example
	 * <ProductImageGallery
	 *   productSlug="fantasy-tv-cabinet"
	 *   selectedTheme="Hitam"
	 *   productName="Fantasy TV Cabinet"
	 *   imageCount={3}
	 * />
	 */

	import { onMount } from 'svelte';
	import DynamicImageSingle from './DynamicImageSingle.svelte';

	// Props interface
	interface Props {
		/** Product slug for image loading */
		productSlug: string;
		/** Currently selected theme name for filtering */
		selectedTheme: string;
		/** Product name for alt text */
		productName: string;
		/** Array of image URLs to display */
		images?: string[];
		/** Number of images available (default: 1) */
		imageCount?: number;
		/** Border radius for all images in gallery (e.g., 'xl', '2xl', '3xl', 'full') */
		rounded?: string;
	}

	let { productSlug, selectedTheme, productName, images = [], imageCount = 1, rounded = '2xl' }: Props = $props();
	
	// Component state using Svelte 5 runes
	let currentImageIndex = $state(0);
	let isZoomed = $state(false);
	let isTransitioning = $state(false);
	let galleryContainer: HTMLDivElement;

	// Use provided images or fallback to default count
	const totalImages = images.length > 0 ? images.length : Math.max(imageCount, 1);

	// Derived values
	let hasMultipleImages = $derived(totalImages > 1);

	// Generate border radius classes
	const roundedClasses = $derived(() => {
		const baseClass = 'rounded';
		if (!rounded || rounded === 'none') return '';
		if (rounded === 'sm') return `${baseClass}-sm`;
		if (rounded === 'md') return `${baseClass}-md`;
		if (rounded === 'lg') return `${baseClass}-lg`;
		if (rounded === 'xl') return `${baseClass}-xl`;
		if (rounded === '2xl') return `${baseClass}-2xl`;
		if (rounded === '3xl') return `${baseClass}-3xl`;
		if (rounded === 'full') return `${baseClass}-full`;
		// Custom rounded value (e.g., '12px', '1rem')
		return `${baseClass}-[${rounded}]`;
	});

	// Type for image sources
	type ImageSource = {
		type: 'product';
		index: number;
		alt: string;
	} | {
		type: 'unsplash';
		url: string;
		alt: string;
	};

	// Generate image sources - always use local product images for consistency with homepage
	const imageSources = $derived((): ImageSource[] => {
		const sources: ImageSource[] = [];

		// Always use local product images (ignore database URLs for consistency)
		// This ensures the same images are shown on homepage and detail page
		// Will look for files like: /static/uploads/products/16x9_kitchen-island-premium_1.jpg
		const imageCountToUse = Math.max(imageCount, 1);
		for (let i = 1; i <= imageCountToUse; i++) {
			sources.push({
				type: 'product',
				index: i,
				alt: `${productName} - ${selectedTheme} - Image ${i}`
			});
		}

		return sources;
	});
	
	/**
	 * Handle thumbnail click with smooth transition
	 * @param index - Index of the clicked thumbnail
	 */
	function selectImage(index: number) {
		if (index >= 0 && index < totalImages && !isTransitioning) {
			isTransitioning = true;
			currentImageIndex = index;
			isZoomed = false;

			setTimeout(() => {
				isTransitioning = false;
			}, 300);
		}
	}

	/**
	 * Navigate to previous image with smooth transition
	 */
	function previousImage() {
		if (!isTransitioning) {
			isTransitioning = true;
			currentImageIndex = currentImageIndex > 0 ? currentImageIndex - 1 : totalImages - 1;
			isZoomed = false;

			setTimeout(() => {
				isTransitioning = false;
			}, 300);
		}
	}

	/**
	 * Navigate to next image with smooth transition
	 */
	function nextImage() {
		if (!isTransitioning) {
			isTransitioning = true;
			currentImageIndex = currentImageIndex < totalImages - 1 ? currentImageIndex + 1 : 0;
			isZoomed = false;

			setTimeout(() => {
				isTransitioning = false;
			}, 300);
		}
	}

	/**
	 * Toggle zoom state
	 */
	function toggleZoom() {
		isZoomed = !isZoomed;
	}
	
	/**
	 * Handle keyboard navigation
	 * @param event - Keyboard event
	 */
	function handleKeydown(event: KeyboardEvent) {
		if (!galleryContainer) return;
		
		switch (event.key) {
			case 'ArrowLeft':
				event.preventDefault();
				previousImage();
				break;
			case 'ArrowRight':
				event.preventDefault();
				nextImage();
				break;
			case 'Escape':
				event.preventDefault();
				isZoomed = false;
				break;
			case ' ':
			case 'Enter':
				event.preventDefault();
				toggleZoom();
				break;
		}
	}
	
	/**
	 * Handle touch/swipe gestures for mobile
	 */
	let touchStartX = 0;
	let touchEndX = 0;
	
	function handleTouchStart(event: TouchEvent) {
		touchStartX = event.changedTouches[0].screenX;
	}
	
	function handleTouchEnd(event: TouchEvent) {
		touchEndX = event.changedTouches[0].screenX;
		handleSwipe();
	}
	
	function handleSwipe() {
		const swipeThreshold = 50;
		const swipeDistance = touchStartX - touchEndX;
		
		if (Math.abs(swipeDistance) > swipeThreshold) {
			if (swipeDistance > 0) {
				// Swipe left - next image
				nextImage();
			} else {
				// Swipe right - previous image
				previousImage();
			}
		}
	}
	
	// Add keyboard event listener on mount (no auto-advance)
	onMount(() => {
		const handleGlobalKeydown = (event: KeyboardEvent) => {
			// Only handle if gallery is focused or no other input is focused
			if (document.activeElement?.tagName === 'INPUT' ||
				document.activeElement?.tagName === 'TEXTAREA') {
				return;
			}
			handleKeydown(event);
		};

		document.addEventListener('keydown', handleGlobalKeydown);

		return () => {
			document.removeEventListener('keydown', handleGlobalKeydown);
		};
	});
</script>

<!-- Main Gallery Container -->
<div
	bind:this={galleryContainer}
	class="space-y-4 focus:outline-none"
	role="region"
	aria-label="Product image gallery"
>
	<!-- Main Image Display -->
	<div class="relative bg-white {roundedClasses()} shadow-sm overflow-hidden group">
		<div
			class="aspect-square w-full relative cursor-pointer transition-all duration-500 ease-in-out"
			class:scale-110={isZoomed}
			onclick={toggleZoom}
			ontouchstart={handleTouchStart}
			ontouchend={handleTouchEnd}
			role="button"
			tabindex="0"
			aria-label={`${productName} main image. Click to ${isZoomed ? 'zoom out' : 'zoom in'}`}
			onkeydown={(e) => e.key === 'Enter' && toggleZoom()}
		>
			<!-- Image Stack with Smooth Transitions -->
			{#each imageSources() as source, index}
				<div
					class="absolute inset-0 transition-all duration-500 ease-in-out {
						index === currentImageIndex
							? 'opacity-100 scale-100'
							: 'opacity-0 scale-105'
					}"
					style="z-index: {index === currentImageIndex ? 10 : 1}"
				>
					{#if source.type === 'product'}
						<DynamicImageSingle
							slug={productSlug}
							index={source.index}
							alt={source.alt}
							className="w-full h-full"
							rounded={rounded}
						/>
					{:else}
						<img
							src={source.url}
							alt={source.alt}
							class="w-full h-full object-cover"
							loading="lazy"
						/>
					{/if}
				</div>
			{/each}
			
			<!-- Zoom Indicator -->
			<div class="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
				{#if isZoomed}
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10h-6"></path>
					</svg>
				{:else}
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
					</svg>
				{/if}
			</div>

			<!-- Navigation Arrows -->
			{#if hasMultipleImages}
				<!-- Previous Button -->
				<button
					class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-white"
					onclick={(e) => { e.stopPropagation(); previousImage(); }}
					aria-label="Previous image"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
					</svg>
				</button>

				<!-- Next Button -->
				<button
					class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-white"
					onclick={(e) => { e.stopPropagation(); nextImage(); }}
					aria-label="Next image"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
					</svg>
				</button>
			{/if}

			<!-- Image Counter -->
			{#if hasMultipleImages}
				<div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
					{currentImageIndex + 1} / {totalImages}
				</div>
			{/if}

			<!-- Image Indicators -->
			<div class="absolute bottom-4 right-4 flex space-x-1">
				{#each imageSources() as _, index}
					<button
						class="w-2 h-2 rounded-full transition-all duration-200 {
							index === currentImageIndex
								? 'bg-white scale-125'
								: 'bg-white bg-opacity-50 hover:bg-opacity-75'
						}"
						onclick={(e) => { e.stopPropagation(); selectImage(index); }}
						aria-label="Go to image {index + 1}"
					></button>
				{/each}
			</div>

			<!-- Loading Overlay -->
			{#if isTransitioning}
				<div class="absolute inset-0 bg-black bg-opacity-10 pointer-events-none"></div>
			{/if}
		</div>
	</div>
	
	<!-- Thumbnail Navigation -->
	{#if hasMultipleImages}
		<div class="flex space-x-2 overflow-x-auto pb-2" role="tablist" aria-label="Image thumbnails">
			{#each imageSources() as source, index}
				<button
					class="flex-shrink-0 w-20 h-20 {roundedClasses()} overflow-hidden border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
					class:border-blue-500={index === currentImageIndex}
					class:border-gray-200={index !== currentImageIndex}
					class:shadow-md={index === currentImageIndex}
					onclick={() => selectImage(index)}
					role="tab"
					aria-selected={index === currentImageIndex}
					aria-label={`View image ${index + 1}`}
				>
					<!-- Thumbnail using appropriate source -->
					{#if source.type === 'product'}
						<DynamicImageSingle
							slug={productSlug}
							index={source.index}
							alt={source.alt}
							className="w-full h-full transition-opacity duration-200 {index === currentImageIndex ? 'opacity-100' : 'opacity-50'}"
							rounded={rounded}
						/>
					{:else}
						<img
							src={source.url}
							alt={source.alt}
							class="w-full h-full object-cover transition-opacity duration-200 {index === currentImageIndex ? 'opacity-100' : 'opacity-50'}"
							loading="lazy"
						/>
					{/if}
				</button>
			{/each}
		</div>
	{/if}
</div>

<!-- Accessibility Instructions -->
<div class="sr-only" aria-live="polite">
	{#if isZoomed}
		Image is zoomed in. Press Escape to zoom out.
	{/if}
	Currently viewing image {currentImageIndex + 1} of {totalImages}.
	Use arrow keys to navigate between images, or swipe on touch devices.
</div>

<style>
	/* Smooth transitions for touch devices */
	@media (hover: none) {
		.group:hover .opacity-0 {
			opacity: 1;
		}
	}
</style>
