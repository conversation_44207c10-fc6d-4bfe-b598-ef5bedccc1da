<!--
@component ProductImageUpload
Comprehensive image upload component with optimization and dual aspect ratio generation
-->

<script lang="ts">
	import { config } from '$lib/config/env';
	import { generateProductSlug } from '$lib/api.js';
	
	interface Props {
		/** Product name for slug generation */
		productName: string;
		/** Starting index for image numbering */
		startIndex?: number;
		/** Maximum number of files to upload */
		maxFiles?: number;
		/** Callback when upload completes */
		onUploadComplete?: (results: UploadResult[]) => void;
		/** Callback for upload progress */
		onProgress?: (progress: number) => void;
	}

	interface UploadResult {
		success: boolean;
		filename?: string;
		original_size?: number;
		optimized_size?: number;
		compression_ratio?: number;
		original_format?: string;
		optimized_format?: string;
		space_saved_bytes?: number;
		space_saved_kb?: number;
		aspect_ratios: AspectRatioResult[];
		error?: string;
	}

	interface AspectRatioResult {
		aspect_ratio: string;
		filename: string;
		dimensions: [number, number];
		file_size: number;
		file_size_kb: number;
		compression_ratio: number;
		file_path: string;
	}

	interface UploadResponse {
		success: boolean;
		message: string;
		results: UploadResult[];
		total_processed: number;
		total_failed: number;
	}

	let {
		productName,
		startIndex = 1,
		maxFiles = 10,
		onUploadComplete,
		onProgress
	}: Props = $props();

	// State
	let files: FileList | null = $state(null);
	let dragActive = $state(false);
	let uploading = $state(false);
	let uploadProgress = $state(0);
	let uploadResults: UploadResult[] = $state([]);
	let previewImages: string[] = $state([]);
	let validationErrors: string[] = $state([]);
	let uploadError = $state('');
	let showUploadError = $state(false);

	// Derived values
	let productSlug = $derived(generateProductSlug(productName));
	let hasFiles = $derived(files && files.length > 0);
	let canUpload = $derived(hasFiles && !uploading && validationErrors.length === 0);
	let fileCount = $derived(files ? files.length : 0);

	// File input reference
	let fileInput: HTMLInputElement;

	/**
	 * Handle file selection from input or drag & drop
	 */
	function handleFiles(selectedFiles: FileList | null) {
		if (!selectedFiles) return;

		// Convert FileList to Array for easier manipulation
		const fileArray = Array.from(selectedFiles);
		
		// Validate files
		const errors = validateFiles(fileArray);
		validationErrors = errors;

		if (errors.length === 0) {
			files = selectedFiles;
			generatePreviews(fileArray);
		}
	}

	/**
	 * Validate selected files
	 */
	function validateFiles(fileArray: File[]): string[] {
		const errors: string[] = [];

		// Check file count
		if (fileArray.length > maxFiles) {
			errors.push(`Maximum ${maxFiles} files allowed`);
		}

		// Check each file
		fileArray.forEach((file, index) => {
			// Check file type
			if (!file.type.startsWith('image/')) {
				errors.push(`File ${index + 1}: Not an image file`);
				return;
			}

			// Check supported formats
			const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
			if (!supportedTypes.includes(file.type)) {
				errors.push(`File ${index + 1}: Unsupported format (${file.type})`);
			}

			// Check file size (10MB limit)
			if (file.size > 10 * 1024 * 1024) {
				errors.push(`File ${index + 1}: File too large (max 10MB)`);
			}

			// Check minimum dimensions (will be validated on backend too)
			const img = new Image();
			img.onload = () => {
				if (img.width < 400 || img.height < 400) {
					validationErrors = [...validationErrors, `File ${index + 1}: Image too small (min 400x400px)`];
				}
			};
			img.src = URL.createObjectURL(file);
		});

		return errors;
	}

	/**
	 * Generate preview images
	 */
	function generatePreviews(fileArray: File[]) {
		previewImages = [];
		
		fileArray.forEach(file => {
			const reader = new FileReader();
			reader.onload = (e) => {
				if (e.target?.result) {
					previewImages = [...previewImages, e.target.result as string];
				}
			};
			reader.readAsDataURL(file);
		});
	}

	/**
	 * Upload files to backend
	 */
	async function uploadFiles() {
		if (!files || !canUpload) return;

		uploading = true;
		uploadProgress = 0;
		uploadResults = [];
		uploadError = '';
		showUploadError = false;

		try {
			const formData = new FormData();
			formData.append('product_slug', productSlug);
			formData.append('start_index', startIndex.toString());

			// Add all files
			Array.from(files).forEach(file => {
				formData.append('images', file);
			});

			// Upload with progress tracking
			const response = await fetch(`${config.api.url}/uploads/products`, {
				method: 'POST',
				body: formData,
			});

			if (!response.ok) {
				const errorText = await response.text();
				let errorMessage = `Upload failed (${response.status})`;

				if (response.status === 400) {
					errorMessage = 'Invalid request. Please check your files and try again.';
				} else if (response.status === 413) {
					errorMessage = 'Files too large. Maximum size is 10MB per file.';
				} else if (response.status === 500) {
					errorMessage = 'Server error. Please try again later.';
				}

				throw new Error(errorMessage);
			}

			const result: UploadResponse = await response.json();
			uploadResults = result.results || [];

			// Check for processing errors
			if (result.total_failed > 0) {
				const failedResults = result.results.filter(r => !r.success);
				const errorMessages = failedResults.map(r => r.error).filter(Boolean);

				if (errorMessages.length > 0) {
					uploadError = `Some images failed to process:\n${errorMessages.join('\n')}`;
					showUploadError = true;
				}
			}

			// Update progress based on results
			uploadProgress = 100;

			// Call completion callback
			if (onUploadComplete) {
				onUploadComplete(uploadResults);
			}

			// Log summary
			console.log(`Upload completed: ${result.total_processed} processed, ${result.total_failed} failed`);

		} catch (error) {
			console.error('Upload failed:', error);
			uploadError = error instanceof Error ? error.message : 'Upload failed unexpectedly';
			showUploadError = true;

			uploadResults = [{
				success: false,
				error: uploadError,
				aspect_ratios: []
			}];
		} finally {
			uploading = false;
			uploadProgress = 100;
		}
	}

	/**
	 * Clear selected files and reset state
	 */
	function clearFiles() {
		files = null;
		previewImages = [];
		validationErrors = [];
		uploadResults = [];
		uploadProgress = 0;
		uploadError = '';
		showUploadError = false;
		if (fileInput) {
			fileInput.value = '';
		}
	}

	/**
	 * Close upload error notification
	 */
	function closeUploadError() {
		showUploadError = false;
		uploadError = '';
	}

	/**
	 * Handle drag events
	 */
	function handleDragOver(e: DragEvent) {
		e.preventDefault();
		dragActive = true;
	}

	function handleDragLeave(e: DragEvent) {
		e.preventDefault();
		dragActive = false;
	}

	function handleDrop(e: DragEvent) {
		e.preventDefault();
		dragActive = false;
		handleFiles(e.dataTransfer?.files || null);
	}

	/**
	 * Trigger file input click
	 */
	function triggerFileInput() {
		fileInput?.click();
	}
</script>

<div class="product-image-upload">
	<!-- Upload Area -->
	<div 
		class="upload-area {dragActive ? 'drag-active' : ''} {hasFiles ? 'has-files' : ''}"
		ondragover={handleDragOver}
		ondragleave={handleDragLeave}
		ondrop={handleDrop}
		onclick={triggerFileInput}
		role="button"
		tabindex="0"
		onkeydown={(e) => e.key === 'Enter' && triggerFileInput()}
	>
		<input
			bind:this={fileInput}
			type="file"
			multiple
			accept="image/*"
			onchange={(e) => handleFiles(e.currentTarget.files)}
			style="display: none;"
		/>

		{#if !hasFiles}
			<div class="upload-prompt">
				<svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
					<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
					<polyline points="7,10 12,15 17,10"/>
					<line x1="12" y1="15" x2="12" y2="3"/>
				</svg>
				<h3>Upload Product Images</h3>
				<p>Drag & drop images here or click to browse</p>
				<p class="upload-info">
					Supports: JPG, PNG, GIF, BMP, WebP<br>
					Max size: 10MB per file<br>
					Min dimensions: 400x400px<br>
					Max files: {maxFiles}
				</p>
			</div>
		{:else}
			<div class="file-info">
				<p>{fileCount} file{fileCount !== 1 ? 's' : ''} selected</p>
				<p class="product-info">Product: <strong>{productName}</strong> (slug: {productSlug})</p>
			</div>
		{/if}
	</div>

	<!-- Validation Errors -->
	{#if validationErrors.length > 0}
		<div class="validation-errors">
			<h4>Please fix the following issues:</h4>
			<ul>
				{#each validationErrors as error}
					<li>{error}</li>
				{/each}
			</ul>
		</div>
	{/if}

	<!-- Upload Error Notification -->
	{#if showUploadError && uploadError}
		<div class="upload-error-notification">
			<div class="error-content">
				<div class="error-icon">⚠️</div>
				<div class="error-details">
					<h4>Upload Failed</h4>
					<div class="error-message">
						{#each uploadError.split('\n') as line}
							<p>{line}</p>
						{/each}
					</div>
					<div class="error-suggestions">
						<p><strong>Common solutions:</strong></p>
						<ul>
							<li>Check that images are at least 400x400 pixels</li>
							<li>Ensure files are under 10MB each</li>
							<li>Use supported formats: JPG, PNG, GIF, BMP, WebP</li>
							<li>Try uploading fewer images at once</li>
							<li>Check your internet connection</li>
						</ul>
					</div>
				</div>
				<button class="close-error" onclick={closeUploadError} title="Close">×</button>
			</div>
		</div>
	{/if}

	<!-- Preview Images -->
	{#if previewImages.length > 0}
		<div class="preview-section">
			<h4>Preview ({previewImages.length} images)</h4>
			<div class="preview-grid">
				{#each previewImages as preview, index}
					<div class="preview-item">
						<img src={preview} alt="Preview {index + 1}" />
						<p>Image {startIndex + index}</p>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Upload Controls -->
	{#if hasFiles}
		<div class="upload-controls">
			<button 
				class="btn btn-primary" 
				onclick={uploadFiles}
				disabled={!canUpload}
			>
				{uploading ? 'Uploading...' : 'Upload & Optimize Images'}
			</button>
			<button 
				class="btn btn-secondary" 
				onclick={clearFiles}
				disabled={uploading}
			>
				Clear
			</button>
		</div>
	{/if}

	<!-- Upload Progress -->
	{#if uploading}
		<div class="upload-progress">
			<div class="progress-bar">
				<div class="progress-fill" style="width: {uploadProgress}%"></div>
			</div>
			<p>Processing images... {uploadProgress}%</p>
		</div>
	{/if}

	<!-- Upload Results -->
	{#if uploadResults.length > 0}
		<div class="upload-results">
			<h4>Upload Results</h4>
			{#each uploadResults as result, index}
				<div class="result-item {result.success ? 'success' : 'error'}">
					<h5>Image {startIndex + index}</h5>
					{#if result.success}
						<p class="success-message">✓ Successfully processed</p>
						{#if result.original_size && result.optimized_size}
							<div class="optimization-stats">
								<p class="compression-info">
									<strong>Size:</strong> {(result.original_size / 1024).toFixed(1)}KB →
									{(result.optimized_size / 1024).toFixed(1)}KB
									({result.compression_ratio?.toFixed(1)}% reduction)
								</p>
								{#if result.space_saved_kb}
									<p class="space-saved">
										<strong>Space saved:</strong> {result.space_saved_kb.toFixed(1)}KB
									</p>
								{/if}
								{#if result.original_format && result.optimized_format}
									<p class="format-info">
										<strong>Format:</strong> {result.original_format} → {result.optimized_format}
									</p>
								{/if}
							</div>
						{/if}
						{#if result.aspect_ratios.length > 0}
							<div class="aspect-ratios">
								<p><strong>Generated files:</strong></p>
								<ul>
									{#each result.aspect_ratios as aspect}
										<li>
											<strong>{aspect.aspect_ratio}:</strong> {aspect.filename}
											({aspect.dimensions[0]}×{aspect.dimensions[1]},
											{aspect.file_size_kb.toFixed(1)}KB,
											{aspect.compression_ratio.toFixed(1)}% compressed)
										</li>
									{/each}
								</ul>
							</div>
						{/if}
					{:else}
						<p class="error-message">✗ {result.error || 'Upload failed'}</p>
					{/if}
				</div>
			{/each}
		</div>
	{/if}
</div>

<style>
	.product-image-upload {
		max-width: 800px;
		margin: 0 auto;
		padding: 1rem;
	}

	.upload-area {
		border: 2px dashed #d1d5db;
		border-radius: 0.5rem;
		padding: 2rem;
		text-align: center;
		cursor: pointer;
		transition: all 0.2s ease;
		background: #f9fafb;
	}

	.upload-area:hover,
	.upload-area.drag-active {
		border-color: #3b82f6;
		background: #eff6ff;
	}

	.upload-area.has-files {
		border-color: #10b981;
		background: #f0fdf4;
	}

	.upload-icon {
		width: 3rem;
		height: 3rem;
		margin: 0 auto 1rem;
		color: #6b7280;
	}

	.upload-prompt h3 {
		margin: 0 0 0.5rem;
		color: #374151;
		font-size: 1.25rem;
		font-weight: 600;
	}

	.upload-prompt p {
		margin: 0.25rem 0;
		color: #6b7280;
	}

	.upload-info {
		font-size: 0.875rem;
		margin-top: 1rem;
		padding-top: 1rem;
		border-top: 1px solid #e5e7eb;
	}

	.file-info {
		color: #059669;
		font-weight: 500;
	}

	.product-info {
		margin-top: 0.5rem;
		font-size: 0.875rem;
		color: #6b7280;
	}

	.validation-errors {
		margin-top: 1rem;
		padding: 1rem;
		background: #fef2f2;
		border: 1px solid #fecaca;
		border-radius: 0.5rem;
		color: #dc2626;
	}

	.validation-errors h4 {
		margin: 0 0 0.5rem;
		font-size: 1rem;
		font-weight: 600;
	}

	.validation-errors ul {
		margin: 0;
		padding-left: 1.5rem;
	}

	.upload-error-notification {
		margin-top: 1rem;
		padding: 1rem;
		background: #fef2f2;
		border: 1px solid #fecaca;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
	}

	.error-content {
		display: flex;
		gap: 1rem;
		align-items: flex-start;
	}

	.error-icon {
		font-size: 1.5rem;
		flex-shrink: 0;
	}

	.error-details {
		flex: 1;
	}

	.error-details h4 {
		margin: 0 0 0.5rem;
		color: #dc2626;
		font-size: 1.125rem;
		font-weight: 600;
	}

	.error-message {
		margin-bottom: 1rem;
	}

	.error-message p {
		margin: 0.25rem 0;
		color: #dc2626;
		font-weight: 500;
	}

	.error-suggestions {
		padding: 0.75rem;
		background: #fef7f7;
		border-radius: 0.375rem;
		border: 1px solid #f3d4d4;
	}

	.error-suggestions p {
		margin: 0 0 0.5rem;
		color: #7f1d1d;
		font-size: 0.875rem;
		font-weight: 600;
	}

	.error-suggestions ul {
		margin: 0;
		padding-left: 1.25rem;
		color: #7f1d1d;
		font-size: 0.875rem;
	}

	.error-suggestions li {
		margin: 0.25rem 0;
	}

	.close-error {
		background: none;
		border: none;
		font-size: 1.5rem;
		color: #dc2626;
		cursor: pointer;
		padding: 0;
		width: 2rem;
		height: 2rem;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 0.25rem;
		flex-shrink: 0;
	}

	.close-error:hover {
		background: #fecaca;
	}

	.preview-section {
		margin-top: 1.5rem;
	}

	.preview-section h4 {
		margin: 0 0 1rem;
		color: #374151;
		font-size: 1.125rem;
		font-weight: 600;
	}

	.preview-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
		gap: 1rem;
	}

	.preview-item {
		text-align: center;
	}

	.preview-item img {
		width: 100%;
		height: 120px;
		object-fit: cover;
		border-radius: 0.5rem;
		border: 2px solid #e5e7eb;
	}

	.preview-item p {
		margin: 0.5rem 0 0;
		font-size: 0.875rem;
		color: #6b7280;
	}

	.upload-controls {
		margin-top: 1.5rem;
		display: flex;
		gap: 1rem;
		justify-content: center;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		border: none;
		font-size: 0.875rem;
	}

	.btn:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.btn-primary {
		background: #3b82f6;
		color: white;
	}

	.btn-primary:hover:not(:disabled) {
		background: #2563eb;
	}

	.btn-secondary {
		background: #6b7280;
		color: white;
	}

	.btn-secondary:hover:not(:disabled) {
		background: #4b5563;
	}

	.upload-progress {
		margin-top: 1.5rem;
		text-align: center;
	}

	.progress-bar {
		width: 100%;
		height: 0.5rem;
		background: #e5e7eb;
		border-radius: 0.25rem;
		overflow: hidden;
		margin-bottom: 0.5rem;
	}

	.progress-fill {
		height: 100%;
		background: #3b82f6;
		transition: width 0.3s ease;
	}

	.upload-results {
		margin-top: 1.5rem;
	}

	.upload-results h4 {
		margin: 0 0 1rem;
		color: #374151;
		font-size: 1.125rem;
		font-weight: 600;
	}

	.result-item {
		margin-bottom: 1rem;
		padding: 1rem;
		border-radius: 0.5rem;
		border: 1px solid;
	}

	.result-item.success {
		background: #f0fdf4;
		border-color: #bbf7d0;
	}

	.result-item.error {
		background: #fef2f2;
		border-color: #fecaca;
	}

	.result-item h5 {
		margin: 0 0 0.5rem;
		font-size: 1rem;
		font-weight: 600;
	}

	.success-message {
		color: #059669;
		font-weight: 500;
		margin: 0.25rem 0;
	}

	.error-message {
		color: #dc2626;
		font-weight: 500;
		margin: 0.25rem 0;
	}

	.optimization-stats {
		margin: 0.75rem 0;
		padding: 0.75rem;
		background: #f0fdf4;
		border: 1px solid #bbf7d0;
		border-radius: 0.375rem;
		font-size: 0.875rem;
	}

	.compression-info,
	.space-saved,
	.format-info {
		margin: 0.25rem 0;
		color: #059669;
	}

	.aspect-ratios {
		margin-top: 0.5rem;
		font-size: 0.875rem;
	}

	.aspect-ratios p {
		margin: 0 0 0.25rem;
		font-weight: 500;
		color: #374151;
	}

	.aspect-ratios ul {
		margin: 0;
		padding-left: 1.5rem;
		color: #6b7280;
	}

	.aspect-ratios li {
		margin: 0.125rem 0;
	}
</style>
