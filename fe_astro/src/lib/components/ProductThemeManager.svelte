<script>
	import { createEventDispatcher } from 'svelte';

	export let themes = [];
	export let productId = null;
	export let disabled = false;

	const dispatch = createEventDispatcher();

	let newTheme = {
		name: '',
		description: '',
		color_code: '#000000',
		price_modifier_type: 'addition',
		price_modifier_value: 0,
		is_default: false
	};

	let showAddForm = false;
	let editingTheme = null;
	let loading = false;

	const priceModifierTypes = [
		{ value: 'addition', label: 'Tambah Harga (+)' },
		{ value: 'subtraction', label: 'Kurangi Harga (-)' },
		{ value: 'replacement', label: 'Ganti Harga (=)' },
		{ value: 'percentage', label: 'Persentase (%)' }
	];

	function resetForm() {
		newTheme = {
			name: '',
			description: '',
			color_code: '#000000',
			price_modifier_type: 'addition',
			price_modifier_value: 0,
			is_default: false
		};
		editingTheme = null;
		showAddForm = false;
	}

	function startEdit(theme) {
		editingTheme = { ...theme };
		showAddForm = true;
	}

	function cancelEdit() {
		resetForm();
	}

	async function saveTheme() {
		if (!newTheme.name.trim()) {
			alert('Nama tema wajib diisi');
			return;
		}

		loading = true;
		try {
			const themeData = {
				...newTheme,
				name: newTheme.name.trim(),
				description: newTheme.description?.trim() || '',
				price_modifier_value: parseFloat(newTheme.price_modifier_value) || 0
			};

			if (editingTheme) {
				// Update existing theme
				dispatch('update', { id: editingTheme.id, data: themeData });
			} else {
				// Add new theme
				dispatch('add', themeData);
			}

			resetForm();
		} catch (error) {
			alert('Gagal menyimpan tema: ' + error.message);
		} finally {
			loading = false;
		}
	}

	function deleteTheme(theme) {
		if (confirm(`Hapus tema "${theme.name}"?`)) {
			dispatch('delete', theme.id);
		}
	}

	function setAsDefault(theme) {
		dispatch('setDefault', theme.id);
	}

	function formatPrice(value) {
		return new Intl.NumberFormat('id-ID').format(value);
	}

	function getPriceDisplay(theme) {
		const value = theme.price_modifier_value;
		switch (theme.price_modifier_type) {
			case 'addition':
				return value > 0 ? `+Rp ${formatPrice(value)}` : 'Gratis';
			case 'subtraction':
				return `-Rp ${formatPrice(value)}`;
			case 'replacement':
				return `Rp ${formatPrice(value)}`;
			case 'percentage':
				return `+${value}%`;
			default:
				return 'Gratis';
		}
	}
</script>

<div class="space-y-4">
	<div class="flex items-center justify-between">
		<h3 class="text-lg font-semibold text-gray-900">Warna/Tema Produk</h3>
		<button
			type="button"
			on:click={() => showAddForm = true}
			class="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
			{disabled}
		>
			<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
			</svg>
			Tambah Tema
		</button>
	</div>

	<!-- Existing Themes List -->
	{#if themes.length > 0}
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			{#each themes as theme (theme.id)}
				<div class="border border-gray-200 rounded-lg p-4 {theme.is_default ? 'ring-2 ring-blue-500 bg-blue-50' : 'bg-white'}">
					<div class="flex items-start justify-between">
						<div class="flex items-center space-x-3">
							<div 
								class="w-8 h-8 rounded-full border border-gray-300"
								style="background-color: {theme.color_code}"
							></div>
							<div>
								<h4 class="font-medium text-gray-900">
									{theme.name}
									{#if theme.is_default}
										<span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Default</span>
									{/if}
								</h4>
								{#if theme.description}
									<p class="text-sm text-gray-600">{theme.description}</p>
								{/if}
								<p class="text-sm font-medium text-green-600">{getPriceDisplay(theme)}</p>
							</div>
						</div>
						<div class="flex items-center space-x-2">
							{#if !theme.is_default}
								<button
									type="button"
									on:click={() => setAsDefault(theme)}
									class="text-xs text-blue-600 hover:text-blue-800"
									title="Set as default"
								>
									Set Default
								</button>
							{/if}
							<button
								type="button"
								on:click={() => startEdit(theme)}
								class="text-blue-600 hover:text-blue-800"
								title="Edit tema"
							>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
								</svg>
							</button>
							<button
								type="button"
								on:click={() => deleteTheme(theme)}
								class="text-red-600 hover:text-red-800"
								title="Hapus tema"
							>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
								</svg>
							</button>
						</div>
					</div>
				</div>
			{/each}
		</div>
	{:else}
		<div class="text-center py-8 text-gray-500">
			<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
			</svg>
			<p class="mt-2">Belum ada tema. Tambahkan tema pertama untuk produk ini.</p>
		</div>
	{/if}

	<!-- Add/Edit Theme Form -->
	{#if showAddForm}
		<div class="border border-gray-200 rounded-lg p-6 bg-gray-50">
			<h4 class="text-lg font-medium text-gray-900 mb-4">
				{editingTheme ? 'Edit Tema' : 'Tambah Tema Baru'}
			</h4>
			
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- Theme Name -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Nama Tema <span class="text-red-500">*</span>
					</label>
					<input
						type="text"
						bind:value={newTheme.name}
						placeholder="Contoh: Hitam Gola, Walnut Premium"
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						required
					>
				</div>

				<!-- Color Code -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Kode Warna
					</label>
					<div class="flex items-center space-x-2">
						<input
							type="color"
							bind:value={newTheme.color_code}
							class="w-12 h-10 border border-gray-300 rounded cursor-pointer"
						>
						<input
							type="text"
							bind:value={newTheme.color_code}
							placeholder="#000000"
							class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						>
					</div>
				</div>

				<!-- Description -->
				<div class="md:col-span-2">
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Deskripsi
					</label>
					<textarea
						bind:value={newTheme.description}
						placeholder="Deskripsi tema (opsional)"
						rows="2"
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					></textarea>
				</div>

				<!-- Price Modifier Type -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Jenis Modifikasi Harga
					</label>
					<select
						bind:value={newTheme.price_modifier_type}
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					>
						{#each priceModifierTypes as type}
							<option value={type.value}>{type.label}</option>
						{/each}
					</select>
				</div>

				<!-- Price Modifier Value -->
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Nilai Modifikasi
					</label>
					<input
						type="number"
						bind:value={newTheme.price_modifier_value}
						placeholder="0"
						min="0"
						step={newTheme.price_modifier_type === 'percentage' ? '1' : '1000'}
						class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					>
					<p class="mt-1 text-xs text-gray-500">
						{#if newTheme.price_modifier_type === 'percentage'}
							Masukkan persentase (contoh: 10 untuk 10%)
						{:else}
							Masukkan nilai dalam Rupiah
						{/if}
					</p>
				</div>

				<!-- Is Default -->
				<div class="md:col-span-2">
					<label class="flex items-center">
						<input
							type="checkbox"
							bind:checked={newTheme.is_default}
							class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
						>
						<span class="ml-2 text-sm text-gray-700">Set sebagai tema default</span>
					</label>
				</div>
			</div>

			<!-- Form Actions -->
			<div class="flex items-center justify-end space-x-3 mt-6">
				<button
					type="button"
					on:click={cancelEdit}
					class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
					disabled={loading}
				>
					Batal
				</button>
				<button
					type="button"
					on:click={saveTheme}
					class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50"
					disabled={loading || !newTheme.name.trim()}
				>
					{#if loading}
						<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
					{/if}
					{editingTheme ? 'Update' : 'Simpan'}
				</button>
			</div>
		</div>
	{/if}
</div>
