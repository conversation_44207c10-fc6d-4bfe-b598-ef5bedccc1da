<script lang="ts">
	import type { ProductTheme } from '$lib/types/product';
	import { formatPrice } from '$lib/api/products';

	interface Props {
		themes: ProductTheme[];
		selectedTheme?: ProductTheme;
		onSelect: (theme: ProductTheme | undefined) => void;
		basePrice: number;
	}

	let { themes, selectedTheme = $bindable(), onSelect, basePrice }: Props = $props();

	function handleThemeSelect(theme: ProductTheme) {
		if (selectedTheme?.id === theme.id) {
			// Deselect if clicking the same theme
			selectedTheme = undefined;
			onSelect(undefined);
		} else {
			selectedTheme = theme;
			onSelect(theme);
		}
	}

	function calculateDisplayPrice(theme: ProductTheme): number {
		switch (theme.price_modifier_type) {
			case 'addition':
				return basePrice + theme.price_modifier_value;
			case 'subtraction':
				return basePrice - theme.price_modifier_value;
			case 'replacement':
				return theme.price_modifier_value;
			case 'percentage':
				return basePrice + (basePrice * theme.price_modifier_value / 100);
			default:
				return basePrice;
		}
	}

	function getPriceModifierDisplay(theme: ProductTheme): string {
		if (theme.price_modifier_value === 0) {
			return 'Gratis';
		}

		switch (theme.price_modifier_type) {
			case 'addition':
				return `+${formatPrice(theme.price_modifier_value)}`;
			case 'subtraction':
				return `-${formatPrice(theme.price_modifier_value)}`;
			case 'replacement':
				return `${formatPrice(theme.price_modifier_value)}`;
			case 'percentage':
				return `+${theme.price_modifier_value}%`;
			default:
				return '';
		}
	}
</script>

<div class="theme-selector">
	<h3 class="text-lg font-semibold mb-4">Pilih Tema/Warna (Opsional)</h3>
	
	<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
		{#each themes as theme (theme.id)}
			<button
				class="theme-option {selectedTheme?.id === theme.id ? 'selected' : ''}"
				onclick={() => handleThemeSelect(theme)}
			>
				{#if theme.image_url}
					<div class="theme-image">
						<img src={theme.image_url} alt={theme.name} class="w-full h-20 object-cover rounded-md" />
					</div>
				{:else if theme.color_code}
					<div class="theme-color" style="background-color: {theme.color_code}"></div>
				{:else}
					<div class="theme-placeholder">
						<span class="text-2xl">🎨</span>
					</div>
				{/if}
				
				<div class="theme-info">
					<div class="theme-name font-medium text-sm">{theme.name}</div>
					{#if theme.description}
						<div class="theme-description text-xs text-gray-500 mt-1">{theme.description}</div>
					{/if}
				</div>
				
				<div class="theme-pricing mt-2">
					{#if theme.is_free}
						<span class="price-free text-green-600 text-xs font-medium">Gratis</span>
					{:else}
						<span class="price-modifier text-xs {theme.price_modifier_value > 0 ? 'text-orange-600' : 'text-green-600'}">
							{getPriceModifierDisplay(theme)}
						</span>
					{/if}
				</div>
			</button>
		{/each}
	</div>

	{#if selectedTheme}
		<div class="selected-theme-info mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
			<div class="flex items-center justify-between">
				<div>
					<span class="font-medium text-blue-900">Tema Terpilih: {selectedTheme.name}</span>
					{#if selectedTheme.description}
						<div class="text-sm text-blue-700 mt-1">{selectedTheme.description}</div>
					{/if}
				</div>
				<div class="text-right">
					{#if selectedTheme.is_free}
						<span class="text-green-600 font-medium">Gratis</span>
					{:else}
						<span class="text-blue-600 font-medium">{getPriceModifierDisplay(selectedTheme)}</span>
					{/if}
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.theme-selector {
		@apply mb-6;
	}

	.theme-option {
		@apply border-2 border-gray-200 rounded-lg p-3 text-left transition-all duration-200 hover:border-blue-300 hover:shadow-md;
	}

	.theme-option.selected {
		@apply border-blue-500 bg-blue-50 shadow-md;
	}

	.theme-color {
		@apply w-full h-20 rounded-md border border-gray-200;
	}

	.theme-placeholder {
		@apply w-full h-20 rounded-md border border-gray-200 flex items-center justify-center bg-gray-50;
	}

	.theme-info {
		@apply mt-2;
	}

	.theme-name {
		@apply text-gray-900;
	}
</style>
