<script lang="ts">
	/**
	 * QuantitySelector Component
	 * Provides increment/decrement controls and direct input for quantity selection
	 * 
	 * @component
	 * @example
	 * <QuantitySelector 
	 *   quantity={currentQuantity} 
	 *   maxQuantity={100}
	 *   onQuantityChange={handleQuantityChange}
	 * />
	 */
	
	// Props interface
	interface Props {
		/** Current quantity value */
		quantity: number;
		/** Maximum allowed quantity (optional) */
		maxQuantity?: number;
		/** Minimum allowed quantity (default: 1) */
		minQuantity?: number;
		/** Callback function when quantity changes */
		onQuantityChange: (quantity: number) => void;
		/** Whether the component is disabled */
		disabled?: boolean;
		/** Size variant of the component */
		size?: 'sm' | 'md' | 'lg';
	}
	
	let { 
		quantity, 
		maxQuantity = 99, 
		minQuantity = 1, 
		onQuantityChange, 
		disabled = false,
		size = 'md'
	}: Props = $props();
	
	// Component state
	let inputElement: HTMLInputElement;
	let isInputFocused = $state(false);
	let inputValue = $state(quantity.toString());
	
	// Update input value when quantity prop changes
	$effect(() => {
		inputValue = quantity.toString();
	});
	
	// Derived values
	let canDecrease = $derived(() => !disabled && quantity > minQuantity);
	let canIncrease = $derived(() => !disabled && quantity < maxQuantity);
	
	/**
	 * Handle quantity change with validation
	 * @param newQuantity - New quantity value
	 */
	function handleQuantityChange(newQuantity: number) {
		const validQuantity = Math.max(minQuantity, Math.min(newQuantity, maxQuantity));
		if (validQuantity !== quantity) {
			onQuantityChange(validQuantity);
		}
	}
	
	/**
	 * Increment quantity by 1
	 */
	function increment() {
		if (canIncrease) {
			handleQuantityChange(quantity + 1);
		}
	}
	
	/**
	 * Decrement quantity by 1
	 */
	function decrement() {
		if (canDecrease) {
			handleQuantityChange(quantity - 1);
		}
	}
	
	/**
	 * Handle direct input change
	 * @param event - Input event
	 */
	function handleInputChange(event: Event) {
		const target = event.target as HTMLInputElement;
		inputValue = target.value;
		
		// Only update quantity if input is valid
		const numValue = parseInt(inputValue);
		if (!isNaN(numValue) && numValue >= minQuantity && numValue <= maxQuantity) {
			handleQuantityChange(numValue);
		}
	}
	
	/**
	 * Handle input blur - validate and correct value
	 */
	function handleInputBlur() {
		isInputFocused = false;
		const numValue = parseInt(inputValue);
		
		if (isNaN(numValue) || numValue < minQuantity || numValue > maxQuantity) {
			// Reset to current valid quantity
			inputValue = quantity.toString();
		} else {
			handleQuantityChange(numValue);
		}
	}
	
	/**
	 * Handle input focus
	 */
	function handleInputFocus() {
		isInputFocused = true;
		// Select all text for easy editing
		setTimeout(() => {
			if (inputElement) {
				inputElement.select();
			}
		}, 0);
	}
	
	/**
	 * Handle keyboard shortcuts
	 * @param event - Keyboard event
	 */
	function handleKeydown(event: KeyboardEvent) {
		switch (event.key) {
			case 'ArrowUp':
				event.preventDefault();
				increment();
				break;
			case 'ArrowDown':
				event.preventDefault();
				decrement();
				break;
			case 'Enter':
				event.preventDefault();
				handleInputBlur();
				break;
			case 'Escape':
				event.preventDefault();
				inputValue = quantity.toString();
				inputElement?.blur();
				break;
		}
	}
	
	/**
	 * Get size-specific classes
	 * @param componentSize - Size variant
	 * @returns CSS classes string
	 */
	function getSizeClasses(componentSize: string): { button: string; input: string; container: string } {
		switch (componentSize) {
			case 'sm':
				return {
					button: 'w-8 h-8 text-sm',
					input: 'w-12 h-8 text-sm px-2',
					container: 'space-x-1'
				};
			case 'lg':
				return {
					button: 'w-12 h-12 text-lg',
					input: 'w-16 h-12 text-lg px-3',
					container: 'space-x-3'
				};
			default: // md
				return {
					button: 'w-10 h-10 text-base',
					input: 'w-14 h-10 text-base px-2',
					container: 'space-x-2'
				};
		}
	}
	
	// Get current size classes
	const sizeClasses = getSizeClasses(size);
</script>

<!-- Quantity Selector Container -->
<div 
	class="flex items-center {sizeClasses.container}"
	role="group"
	aria-label="Quantity selector"
>
	<!-- Decrease Button -->
	<button
		class="flex items-center justify-center rounded-full border border-gray-300 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:border-gray-300 transition-all duration-200 {sizeClasses.button}"
		onclick={decrement}
		disabled={!canDecrease}
		aria-label="Decrease quantity"
		title="Decrease quantity"
	>
		<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
		</svg>
	</button>
	
	<!-- Quantity Input -->
	<div class="relative">
		<input
			bind:this={inputElement}
			type="number"
			min={minQuantity}
			max={maxQuantity}
			value={inputValue}
			class="text-center border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 transition-all duration-200 {sizeClasses.input}"
			class:ring-2={isInputFocused}
			class:ring-blue-500={isInputFocused}
			class:border-blue-500={isInputFocused}
			oninput={handleInputChange}
			onblur={handleInputBlur}
			onfocus={handleInputFocus}
			onkeydown={handleKeydown}
			disabled={disabled}
			aria-label="Product quantity"
			aria-describedby="quantity-help"
		/>
		
		<!-- Input Validation Indicator -->
		{#if isInputFocused && inputValue !== quantity.toString()}
			{@const numValue = parseInt(inputValue)}
			{@const isValid = !isNaN(numValue) && numValue >= minQuantity && numValue <= maxQuantity}
			
			<div class="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs whitespace-nowrap">
				{#if isValid}
					<span class="text-green-600">✓ Valid</span>
				{:else}
					<span class="text-red-600">
						{#if isNaN(numValue)}
							Invalid number
						{:else if numValue < minQuantity}
							Min: {minQuantity}
						{:else if numValue > maxQuantity}
							Max: {maxQuantity}
						{/if}
					</span>
				{/if}
			</div>
		{/if}
	</div>
	
	<!-- Increase Button -->
	<button
		class="flex items-center justify-center rounded-full border border-gray-300 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:border-gray-300 transition-all duration-200 {sizeClasses.button}"
		onclick={increment}
		disabled={!canIncrease}
		aria-label="Increase quantity"
		title="Increase quantity"
	>
		<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
		</svg>
	</button>
</div>

<!-- Help Text -->
<div id="quantity-help" class="sr-only">
	Use the minus and plus buttons or type directly to change quantity. 
	Minimum: {minQuantity}, Maximum: {maxQuantity}. 
	Current quantity: {quantity}.
</div>

<!-- Quantity Limits Display -->
{#if size === 'lg'}
	<div class="mt-2 text-xs text-gray-500 text-center">
		Range: {minQuantity} - {maxQuantity}
	</div>
{/if}

<!-- Accessibility Live Region -->
<div class="sr-only" aria-live="polite">
	{#if quantity !== parseInt(inputValue) && isInputFocused}
		{@const numValue = parseInt(inputValue)}
		{#if !isNaN(numValue)}
			{#if numValue < minQuantity}
				Quantity below minimum of {minQuantity}
			{:else if numValue > maxQuantity}
				Quantity above maximum of {maxQuantity}
			{:else}
				Quantity will be set to {numValue}
			{/if}
		{:else}
			Invalid quantity entered
		{/if}
	{/if}
</div>

<style>
	/* Remove number input spinners */
	input[type="number"]::-webkit-outer-spin-button,
	input[type="number"]::-webkit-inner-spin-button {
		-webkit-appearance: none;
		margin: 0;
	}
	
	input[type="number"] {
		-moz-appearance: textfield;
	}
	
	/* Custom focus styles for better accessibility */
	button:focus-visible,
	input:focus-visible {
		outline: 2px solid #3B82F6;
		outline-offset: 2px;
	}
	
	/* Smooth transitions for all interactive elements */
	button, input {
		transition-property: all;
		transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	}
	
	/* Ensure proper contrast for accessibility */
	@media (prefers-reduced-motion: reduce) {
		* {
			transition-duration: 0.01ms !important;
			animation-duration: 0.01ms !important;
		}
	}
	
	/* High contrast mode support */
	@media (prefers-contrast: high) {
		button, input {
			border-width: 2px;
		}
		
		button:disabled {
			opacity: 0.7;
		}
	}
	
	/* Touch-friendly sizing for mobile */
	@media (max-width: 640px) {
		button {
			min-width: 44px;
			min-height: 44px;
		}
	}
</style>
