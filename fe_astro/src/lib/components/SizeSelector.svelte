<script lang="ts">
	/**
	 * SizeSelector Component
	 * Displays size options with dimensions and pricing in a card-based layout
	 * 
	 * @component
	 * @example
	 * <SizeSelector 
	 *   sizes={productSizes} 
	 *   selectedSize="2x3m" 
	 *   onSizeChange={handleSizeChange}
	 * />
	 */
	
	// Size interface
	interface Size {
		id: string;
		label: string;
		dimensions: {
			width: number;
			height: number;
			unit: string;
		};
		price: number;
		isDefault?: boolean;
	}
	
	// Props interface
	interface Props {
		/** Array of available sizes */
		sizes: Size[];
		/** Currently selected size ID */
		selectedSize: string;
		/** Callback function when size changes */
		onSizeChange: (size: Size) => void;
	}
	
	let { sizes, selectedSize, onSizeChange }: Props = $props();
	
	// Component state
	let hoveredSize = $state<string | null>(null);
	let focusedSize = $state<string | null>(null);
	
	/**
	 * Handle size selection
	 * @param size - Selected size object
	 */
	function selectSize(size: Size) {
		onSizeChange(size);
	}
	
	/**
	 * <PERSON>le keyboard navigation
	 * @param event - Keyboard event
	 * @param size - Size object
	 */
	function handleKeydown(event: KeyboardEvent, size: Size) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			selectSize(size);
		}
	}
	
	/**
	 * Format price to Indonesian Rupiah
	 * @param price - Price in number format
	 * @returns Formatted price string
	 */
	function formatPrice(price: number): string {
		return new Intl.NumberFormat('id-ID').format(price);
	}
	
	/**
	 * Format dimensions display
	 * @param size - Size object
	 * @returns Formatted dimensions string
	 */
	function formatDimensions(size: Size): string {
		const { width, height, unit } = size.dimensions;
		return `${width} × ${height} ${unit}`;
	}
	
	/**
	 * Get card styling classes based on state
	 * @param size - Size object
	 * @param isSelected - Whether size is selected
	 * @param isHovered - Whether size is hovered
	 * @param isFocused - Whether size is focused
	 * @returns CSS classes string
	 */
	function getCardClasses(size: Size, isSelected: boolean, isHovered: boolean, isFocused: boolean): string {
		let classes = 'relative p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer focus:outline-none ';
		
		if (isSelected) {
			classes += 'border-blue-500 bg-blue-50 shadow-md ring-2 ring-blue-200 ';
		} else if (isHovered || isFocused) {
			classes += 'border-gray-400 bg-gray-50 shadow-sm ';
		} else {
			classes += 'border-gray-200 bg-white hover:border-gray-300 ';
		}
		
		return classes;
	}
	
	/**
	 * Get text color classes based on selection state
	 * @param isSelected - Whether size is selected
	 * @returns CSS classes string
	 */
	function getTextClasses(isSelected: boolean): string {
		return isSelected ? 'text-blue-900' : 'text-gray-900';
	}
	
	/**
	 * Get price color classes based on selection state
	 * @param isSelected - Whether size is selected
	 * @returns CSS classes string
	 */
	function getPriceClasses(isSelected: boolean): string {
		return isSelected ? 'text-blue-700' : 'text-gray-700';
	}
</script>

<!-- Size Selector Container -->
<div class="space-y-3" role="radiogroup" aria-label="Select product size">
	<!-- Size Options Grid -->
	<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
		{#each sizes as size (size.id)}
			{@const isSelected = selectedSize === size.id}
			{@const isHovered = hoveredSize === size.id}
			{@const isFocused = focusedSize === size.id}
			
			<!-- Size Card -->
			<button
				class={getCardClasses(size, isSelected, isHovered, isFocused)}
				onclick={() => selectSize(size)}
				onkeydown={(e) => handleKeydown(e, size)}
				onmouseenter={() => hoveredSize = size.id}
				onmouseleave={() => hoveredSize = null}
				onfocus={() => focusedSize = size.id}
				onblur={() => focusedSize = null}
				role="radio"
				aria-checked={isSelected}
				aria-label={`Select size ${size.label}, dimensions ${formatDimensions(size)}, price ${formatPrice(size.price)} rupiah`}
				tabindex={isSelected ? 0 : -1}
			>
				<!-- Selected Indicator -->
				{#if isSelected}
					<div class="absolute top-2 right-2">
						<div class="w-6 h-6 bg-blue-500/5 rounded-full flex items-center justify-center">
							<svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
							</svg>
						</div>
					</div>
				{/if}
				
				<!-- Size Content -->
				<div class="space-y-2">
					<!-- Size Label -->
					<div class="text-left">
						<h4 class="text-lg font-semibold {getTextClasses(isSelected)}">
							{size.label}
						</h4>
						
						<!-- Dimensions -->
						<!-- <p class="text-sm text-gray-600">
							{formatDimensions(size)}
						</p> -->
					</div>
					
					<!-- Price -->
					<!-- <div class="text-left">
						<div class="flex items-baseline space-x-1">
							<span class="text-xs {getPriceClasses(isSelected)}">Rp</span>
							<span class="text-xl font-bold {getPriceClasses(isSelected)}">
								{formatPrice(size.price)}
							</span>
						</div>
					</div> -->
					
					<!-- Default Badge -->
					{#if size.isDefault}
						<div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
							Default
						</div>
					{/if}
				</div>
				
				<!-- Hover Effect Overlay -->
				{#if isHovered && !isSelected}
					<div class="absolute inset-0 bg-blue-500/15 rounded-lg pointer-events-none outline-blue-500 outline-2 outline-offset-2 transition-all duration-200		"></div>
				{/if}
			</button>
		{/each}
	</div>
	
	<!-- Selected Size Summary -->
	<!-- {#if selectedSize}
		{@const currentSize = sizes.find(s => s.id === selectedSize)}
		{#if currentSize}
			<div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
				<div class="flex items-center space-x-3">
					<div class="w-2 h-2 bg-blue-500 rounded-full"></div>
					<div>
						<span class="text-sm font-medium text-blue-900">Selected Size:</span>
						<span class="text-sm text-blue-700 ml-2">
							{currentSize.label} ({formatDimensions(currentSize)})
						</span>
					</div>
				</div>
				<div class="text-right">
					<div class="flex items-baseline space-x-1">
						<span class="text-xs text-blue-700">Rp</span>
						<span class="text-lg font-bold text-blue-900">
							{formatPrice(currentSize.price)}
						</span>
					</div>
				</div>
			</div>
		{/if}
	{/if} -->
	
	<!-- Size Comparison Helper -->
	{#if sizes.length > 1}
		<div class="text-xs text-gray-500 space-y-1">
			<p>💡 <strong>Size Guide:</strong></p>
			<ul class="list-disc list-inside space-y-1 ml-4">
				<li>Prices may vary based on dimensions and materials</li>
				<li>All measurements are approximate</li>
				<li>Custom sizes available upon request</li>
			</ul>
		</div>
	{/if}
</div>

<!-- Accessibility Instructions -->
<div class="sr-only" aria-live="polite">
	{#if selectedSize}
		{@const currentSize = sizes.find(s => s.id === selectedSize)}
		{#if currentSize}
			Size {currentSize.label} selected. Dimensions: {formatDimensions(currentSize)}. Price: {formatPrice(currentSize.price)} rupiah.
		{/if}
	{/if}
</div>

<style>
	/* Custom focus styles for better accessibility */
	button:focus-visible {
		outline: 2px solid #3B82F6;
		outline-offset: 2px;
	}
	
	/* Smooth transitions for all interactive elements */
	button {
		transition-property: all;
		transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	}
	
	/* Ensure proper contrast for accessibility */
	@media (prefers-reduced-motion: reduce) {
		* {
			transition-duration: 0.01ms !important;
			animation-duration: 0.01ms !important;
		}
	}
	
	/* High contrast mode support */
	@media (prefers-contrast: high) {
		button {
			border-width: 3px;
		}
		
		.ring-2 {
			ring-width: 4px;
		}
	}
	
	/* Responsive adjustments */
	@media (max-width: 640px) {
		.grid {
			grid-template-columns: repeat(1, minmax(0, 1fr));
		}
	}
	
	@media (min-width: 641px) and (max-width: 1024px) {
		.grid {
			grid-template-columns: repeat(2, minmax(0, 1fr));
		}
	}
</style>
