/**
 * Environment configuration utility
 * Centralizes all environment variable access
 */

import { env } from '$env/dynamic/public';

export const config = {
	// Application configuration
	app: {
		name: env.PUBLIC_APP_NAME || 'Astro Works',
		version: env.PUBLIC_APP_VERSION || '1.0.0',
		environment: env.PUBLIC_ENVIRONMENT || 'development'
	},

	// Frontend configuration
	frontend: {
		url: env.PUBLIC_FE_URL || 'http://localhost:5173'
	},

	// API configuration
	api: {
		baseUrl: env.PUBLIC_API_BASE_URL || 'http://localhost:7998',
		version: env.PUBLIC_API_VERSION || 'v1',
		url: env.PUBLIC_API_URL || 'http://localhost:7998/api/v1',
		staticUrl: env.PUBLIC_STATIC_URL || 'http://localhost:7998/static',
		uploadsUrl: env.PUBLIC_UPLOADS_URL || 'http://localhost:7998/static/uploads',
		imagesUrl: env.PUBLIC_IMAGES_URL || 'http://localhost:7998/static/images'
	},

	// Business configuration
	whatsapp: {
		phoneNumber: env.PUBLIC_WHATSAPP_PHONE_NUMBER || '***********',
		baseUrl: env.PUBLIC_WHATSAPP_BASE_URL || 'https://wa.me/'
	},

	company: {
		name: env.PUBLIC_COMPANY_NAME || 'Astro Works Indonesia',
		email: env.PUBLIC_COMPANY_EMAIL || '<EMAIL>',
		address: env.PUBLIC_COMPANY_ADDRESS || 'Jakarta, Indonesia'
	},

	bank: {
		name: env.PUBLIC_BANK_NAME || 'BCA',
		accountNumber: env.PUBLIC_BANK_ACCOUNT_NUMBER || '**********',
		accountName: env.PUBLIC_BANK_ACCOUNT_NAME || 'Astro Works Indonesia PT'
	},

	// UI/UX configuration
	image: {
		quality: parseInt(env.PUBLIC_IMAGE_QUALITY || '85'),
		lazyLoading: env.PUBLIC_IMAGE_LAZY_LOADING === 'true',
		placeholder: env.PUBLIC_IMAGE_PLACEHOLDER === 'true'
	},

	cart: {
		storageKey: env.PUBLIC_CART_STORAGE_KEY || 'astro_cart',
		expiryHours: parseInt(env.PUBLIC_CART_EXPIRY_HOURS || '24')
	},

	pagination: {
		productsPerPage: parseInt(env.PUBLIC_PRODUCTS_PER_PAGE || '12'),
		categoriesPerPage: parseInt(env.PUBLIC_CATEGORIES_PER_PAGE || '20')
	},

	// Feature flags
	features: {
		analytics: env.PUBLIC_ENABLE_ANALYTICS === 'true',
		chat: env.PUBLIC_ENABLE_CHAT !== 'false', // default true
		reviews: env.PUBLIC_ENABLE_REVIEWS === 'true',
		wishlist: env.PUBLIC_ENABLE_WISHLIST === 'true'
	},

	// Development configuration
	dev: {
		debugMode: env.PUBLIC_DEBUG_MODE === 'true',
		showConsoleLogs: env.PUBLIC_SHOW_CONSOLE_LOGS === 'true',
		mockApi: env.PUBLIC_MOCK_API === 'true'
	},

	// Performance configuration
	performance: {
		enablePwa: env.PUBLIC_ENABLE_PWA === 'true',
		enableServiceWorker: env.PUBLIC_ENABLE_SERVICE_WORKER === 'true'
	},

	// Fallback Unsplash images for product gallery
	unsplash: {
		fallbackImages: [
			'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',
			'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800&h=600&fit=crop',
			'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&h=600&fit=crop',
			'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&auto=format&q=80',
			'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&auto=format&q=80'
		]
	}
};

/**
 * Get API endpoint URL
 */
export function getApiUrl(endpoint: string): string {
	return `${config.api.url}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
}

/**
 * Get static file URL
 */
export function getStaticUrl(path: string): string {
	return `${config.api.staticUrl}${path.startsWith('/') ? path : `/${path}`}`;
}

/**
 * Get upload file URL
 */
export function getUploadUrl(path: string): string {
	return `${config.api.uploadsUrl}${path.startsWith('/') ? path : `/${path}`}`;
}

/**
 * Get WhatsApp URL with message
 */
export function getWhatsAppUrl(message: string = ''): string {
	const encodedMessage = message ? `?text=${encodeURIComponent(message)}` : '';
	return `${config.whatsapp.baseUrl}${config.whatsapp.phoneNumber}${encodedMessage}`;
}

/**
 * Fetch configuration from backend API
 */
export async function fetchBackendConfig(): Promise<any> {
	try {
		const response = await fetch(`${config.api.url}/config`);
		if (response.ok) {
			return await response.json();
		}
	} catch (error) {
		console.error('Failed to fetch backend config:', error);
	}
	return null;
}

/**
 * Get WhatsApp URL using backend configuration
 */
export async function getWhatsAppUrlFromBackend(message: string = ''): Promise<string> {
	const backendConfig = await fetchBackendConfig();
	if (backendConfig?.whatsapp) {
		const encodedMessage = message ? `?text=${encodeURIComponent(message)}` : '';
		return `${backendConfig.whatsapp.base_url}${backendConfig.whatsapp.phone_number}${encodedMessage}`;
	}
	// Fallback to environment config
	return getWhatsAppUrl(message);
}
