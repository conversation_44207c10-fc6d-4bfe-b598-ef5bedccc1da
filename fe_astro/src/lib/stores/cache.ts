import { writable } from 'svelte/store';
import type { Product, ProductDimension, ProductTheme } from '$lib/types/product';

interface CacheEntry<T> {
	data: T;
	timestamp: number;
	ttl: number; // Time to live in milliseconds
}

class CacheManager {
	private cache = new Map<string, CacheEntry<any>>();
	private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

	set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
		this.cache.set(key, {
			data,
			timestamp: Date.now(),
			ttl
		});
	}

	get<T>(key: string): T | null {
		const entry = this.cache.get(key);
		if (!entry) return null;

		const now = Date.now();
		if (now - entry.timestamp > entry.ttl) {
			this.cache.delete(key);
			return null;
		}

		return entry.data as T;
	}

	has(key: string): boolean {
		return this.get(key) !== null;
	}

	delete(key: string): void {
		this.cache.delete(key);
	}

	clear(): void {
		this.cache.clear();
	}

	// Clean expired entries
	cleanup(): void {
		const now = Date.now();
		for (const [key, entry] of this.cache.entries()) {
			if (now - entry.timestamp > entry.ttl) {
				this.cache.delete(key);
			}
		}
	}
}

export const cacheManager = new CacheManager();

// Cleanup expired entries every 5 minutes
if (typeof window !== 'undefined') {
	setInterval(() => {
		cacheManager.cleanup();
	}, 5 * 60 * 1000);
}

// Cached API functions
export async function getCachedProduct(id: string): Promise<Product | null> {
	const cacheKey = `product:${id}`;
	const cached = cacheManager.get<Product>(cacheKey);
	
	if (cached) {
		return cached;
	}

	try {
		const response = await fetch(`/api/v1/products/${id}/pricing`);
		if (!response.ok) return null;
		
		const product = await response.json();
		cacheManager.set(cacheKey, product, 10 * 60 * 1000); // Cache for 10 minutes
		return product;
	} catch (error) {
		console.error('Failed to fetch product:', error);
		return null;
	}
}

export async function getCachedProductDimensions(productId: string): Promise<ProductDimension[]> {
	const cacheKey = `dimensions:${productId}`;
	const cached = cacheManager.get<ProductDimension[]>(cacheKey);
	
	if (cached) {
		return cached;
	}

	try {
		const response = await fetch(`/api/v1/products/${productId}/dimensions`);
		if (!response.ok) return [];
		
		const dimensions = await response.json();
		cacheManager.set(cacheKey, dimensions, 15 * 60 * 1000); // Cache for 15 minutes
		return dimensions;
	} catch (error) {
		console.error('Failed to fetch dimensions:', error);
		return [];
	}
}

export async function getCachedProductThemes(productId: string): Promise<ProductTheme[]> {
	const cacheKey = `themes:${productId}`;
	const cached = cacheManager.get<ProductTheme[]>(cacheKey);
	
	if (cached) {
		return cached;
	}

	try {
		const response = await fetch(`/api/v1/products/${productId}/themes`);
		if (!response.ok) return [];
		
		const themes = await response.json();
		cacheManager.set(cacheKey, themes, 15 * 60 * 1000); // Cache for 15 minutes
		return themes;
	} catch (error) {
		console.error('Failed to fetch themes:', error);
		return [];
	}
}

export async function getCachedAccessories(): Promise<Product[]> {
	const cacheKey = 'accessories';
	const cached = cacheManager.get<Product[]>(cacheKey);
	
	if (cached) {
		return cached;
	}

	try {
		const response = await fetch('/api/v1/products/accessories');
		if (!response.ok) return [];
		
		const accessories = await response.json();
		cacheManager.set(cacheKey, accessories, 30 * 60 * 1000); // Cache for 30 minutes
		return accessories;
	} catch (error) {
		console.error('Failed to fetch accessories:', error);
		return [];
	}
}

// Cache invalidation functions
export function invalidateProductCache(productId: string): void {
	cacheManager.delete(`product:${productId}`);
	cacheManager.delete(`dimensions:${productId}`);
	cacheManager.delete(`themes:${productId}`);
}

export function invalidateAllCache(): void {
	cacheManager.clear();
}
