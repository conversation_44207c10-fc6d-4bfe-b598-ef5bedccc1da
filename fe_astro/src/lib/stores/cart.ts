import { writable } from 'svelte/store';
import type { CartItem, Product, ProductConfiguration } from '$lib/types/product';

interface CartState {
	items: CartItem[];
	total: number;
	itemCount: number;
}

function createCartStore() {
	const { subscribe, set, update } = writable<CartState>({
		items: [],
		total: 0,
		itemCount: 0
	});

	// Load cart from localStorage on initialization
	if (typeof window !== 'undefined') {
		const savedCart = localStorage.getItem('astro_cart');
		if (savedCart) {
			try {
				const cartData = JSON.parse(savedCart);
				set(cartData);
			} catch (error) {
				console.error('Failed to load cart from localStorage:', error);
			}
		}
	}

	function calculateTotals(items: CartItem[]): { total: number; itemCount: number } {
		const total = items.reduce((sum, item) => sum + item.total_price, 0);
		const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
		return { total, itemCount };
	}

	function saveToLocalStorage(state: CartState) {
		if (typeof window !== 'undefined') {
			localStorage.setItem('astro_cart', JSON.stringify(state));
		}
	}

	return {
		subscribe,
		
		addItem: (product: Product, configuration: ProductConfiguration, quantity: number = 1) => {
			update(state => {
				const existingItemIndex = state.items.findIndex(item => 
					item.product.id === product.id &&
					item.configuration.selectedDimension?.id === configuration.selectedDimension?.id &&
					item.configuration.selectedTheme?.id === configuration.selectedTheme?.id &&
					JSON.stringify(item.configuration.selectedAccessories) === JSON.stringify(configuration.selectedAccessories)
				);

				if (existingItemIndex >= 0) {
					// Update existing item
					state.items[existingItemIndex].quantity += quantity;
					state.items[existingItemIndex].total_price = 
						state.items[existingItemIndex].quantity * configuration.totalPrice;
				} else {
					// Add new item
					const newItem: CartItem = {
						product,
						configuration,
						quantity,
						total_price: quantity * configuration.totalPrice
					};
					state.items.push(newItem);
				}

				const { total, itemCount } = calculateTotals(state.items);
				const newState = { ...state, total, itemCount };
				saveToLocalStorage(newState);
				return newState;
			});
		},

		removeItem: (itemIndex: number) => {
			update(state => {
				state.items.splice(itemIndex, 1);
				const { total, itemCount } = calculateTotals(state.items);
				const newState = { ...state, total, itemCount };
				saveToLocalStorage(newState);
				return newState;
			});
		},

		updateQuantity: (itemIndex: number, quantity: number) => {
			update(state => {
				if (quantity <= 0) {
					state.items.splice(itemIndex, 1);
				} else {
					state.items[itemIndex].quantity = quantity;
					state.items[itemIndex].total_price = 
						quantity * state.items[itemIndex].configuration.totalPrice;
				}

				const { total, itemCount } = calculateTotals(state.items);
				const newState = { ...state, total, itemCount };
				saveToLocalStorage(newState);
				return newState;
			});
		},

		clear: () => {
			const newState = { items: [], total: 0, itemCount: 0 };
			set(newState);
			saveToLocalStorage(newState);
		},

		// Generate order data from cart
		generateOrderData: (customerInfo: any) => {
			let orderItems: any[] = [];
			let cartState: CartState;
			
			// Get current cart state
			const unsubscribe = subscribe(state => {
				cartState = state;
			});
			unsubscribe();

			for (const item of cartState!.items) {
				const orderItem = {
					product_id: item.product.id,
					quantity: item.quantity,
					unit_price: item.configuration.totalPrice,
					dimension_id: item.configuration.selectedDimension?.id,
					theme_id: item.configuration.selectedTheme?.id,
					configuration_details: {
						dimension: item.configuration.selectedDimension,
						theme: item.configuration.selectedTheme,
						accessories: Array.from(item.configuration.selectedAccessories.values())
					}
				};
				orderItems.push(orderItem);
			}

			return {
				...customerInfo,
				order_items: orderItems
			};
		}
	};
}

export const cart = createCartStore();
