export interface Product {
	id: string;
	name: string;
	slug: string;
	description?: string;
	short_description?: string;
	sku?: string;
	price: number;
	unit: string;
	minimum_quantity: number;
	specifications?: Record<string, any>;
	dimensions?: Record<string, any>;
	weight?: number;
	is_active: boolean;
	is_featured: boolean;
	sort_order: number;
	meta_title?: string;
	meta_description?: string;
	created_at: string;
	updated_at: string;
	images?: ProductImage[];
	categories?: Category[];
	product_dimensions?: ProductDimension[];
	product_themes?: ProductTheme[];
	default_configuration?: ProductConfiguration;
	available_accessories?: Product[];
}

export interface ProductImage {
	id: string;
	product_id: string;
	image_url: string;
	alt_text?: string;
	is_primary: boolean;
	sort_order: number;
	created_at: string;
	updated_at: string;
}

export interface Category {
	id: string;
	name: string;
	slug: string;
	description?: string;
	parent_id?: string;
	is_accessory: boolean;
	sort_order: number;
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

export interface ProductDimension {
	id: string;
	product_id: string;
	name: string;
	width: number;
	height: number;
	depth?: number;
	unit: string;
	price_modifier_type: PriceModifierType;
	price_modifier_value: number;
	is_default: boolean;
	sort_order: number;
	is_active: boolean;
	created_at: string;
	updated_at: string;
	calculated_price?: number;
	display_name: string;
}

export interface ProductTheme {
	id: string;
	product_id: string;
	name: string;
	description?: string;
	color_code?: string;
	image_url?: string;
	price_modifier_type: PriceModifierType;
	price_modifier_value: number;
	is_default: boolean;
	sort_order: number;
	is_active: boolean;
	created_at: string;
	updated_at: string;
	calculated_price?: number;
	is_free: boolean;
}

export interface ProductConfiguration {
	id: string;
	product_id: string;
	dimension_id?: string;
	theme_id?: string;
	configuration_name: string;
	sku?: string;
	final_price: number;
	is_available: boolean;
	stock_quantity?: number;
	lead_time_days?: number;
	created_at: string;
	updated_at: string;
	dimension?: ProductDimension;
	theme?: ProductTheme;
}

export type PriceModifierType = 'addition' | 'subtraction' | 'replacement' | 'percentage';

export interface AccessorySelection {
	product_id: string;
	quantity: number;
}

export interface CalculatePriceRequest {
	product_id: string;
	dimension_id?: string;
	theme_id?: string;
	accessories?: AccessorySelection[];
}

export interface PriceCalculationResponse {
	base_price: number;
	dimension_modifier?: number;
	theme_modifier?: number;
	accessories_total: number;
	subtotal: number;
	final_price: number;
	breakdown: PriceBreakdown;
}

export interface PriceBreakdown {
	base_product: PriceItem;
	dimension?: PriceItem;
	theme?: PriceItem;
	accessories: PriceItem[];
}

export interface PriceItem {
	name: string;
	price: number;
	quantity?: number;
	total: number;
}

export interface ProductConfiguration {
	selectedDimension?: ProductDimension;
	selectedTheme?: ProductTheme;
	selectedAccessories: Map<string, AccessorySelection>;
	totalPrice: number;
}

export interface CartItem {
	product: Product;
	configuration: ProductConfiguration;
	quantity: number;
	total_price: number;
}
