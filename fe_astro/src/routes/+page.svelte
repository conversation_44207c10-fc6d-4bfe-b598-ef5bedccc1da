<script lang="ts">
	/**
	 * Homepage Component
	 * Dynamic product showcase that integrates with backend API
	 * Features category-based product display with proper routing
	 */

	import { onMount } from 'svelte';
	import Header from '$lib/components/Header.svelte';
	import ProductCard from '$lib/components/ProductCard.svelte';
	import EmptyState from '$lib/components/EmptyState.svelte';
	import ErrorBoundary from '$lib/components/ErrorBoundary.svelte';

	// Get data from page load function
	let { data }: { data: any } = $props();

	// Define TypeScript interfaces
	interface ProductCardData {
		id: string;
		name: string;
		slug?: string;
		category: string;
		image: string;
		price: number;
		description?: string;
		shortDescription?: string;
		isActive: boolean;
	}

	interface Category {
		id: string;
		name: string;
		products: ProductCardData[];
	}

	// Component state
	let loading = $state(false);
	let error = $state<string | null>(null);
	let categories = $state<Category[]>([]);
	let products = $state<ProductCardData[]>([]);

	// Initialize data from page load
	onMount(() => {
		if (data.categories && data.products) {
			categories = data.categories;
			products = data.products;
			loading = false;
		} else if (data.error) {
			error = data.error;
			loading = false;
		}
	});
</script>

<svelte:head>
	<title>Astro Works - Premium Furniture Solutions</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta name="description" content="Astro Works - Premium kitchen cabinets, wardrobes, and custom furniture solutions" />
</svelte:head>

<!-- Main Layout -->
<div class="bg-[#5F44F0] lg:bg-gray-50 ">
	<!-- Header -->
	<Header />
	
	
  <!-- Main Content -->
  <main class="h-[calc(100vh-160px)] lg:min-h-screen lg:container bg-gray-50 rounded-t-3xl lg:px-8 py-8 lg:py-32 mx-auto lg:mt-8 flex flex-col overflow-hidden">
    <div class="overflow-y-auto ">
		{#if loading}
			<div class="container mx-auto px-4">
				<div class="text-center py-12">
					<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
					<p class="mt-4 text-gray-600">Loading products...</p>
				</div>
			</div>
		{:else if error}
			<!-- Error State -->
			<div class="container mx-auto px-4">
				<div class="text-center py-12">
					<div class="text-red-600 mb-4">
						<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
						</svg>
					</div>
					<h2 class="text-xl font-semibold text-gray-900 mb-2">Failed to Load Products</h2>
					<p class="text-gray-600 mb-4">{error}</p>
					<button
						class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
						onclick={() => window.location.reload()}
					>
						Try Again
					</button>
				</div>
			</div>
		{:else if categories.length === 0 || data.isEmpty}
			<!-- Empty State -->
			<div class="container mx-auto px-4">
				<EmptyState
					title="No Products Available"
					description={data.message || "We're currently updating our product catalog. Please check back soon for new arrivals!"}
					icon="products"
					showRefresh={true}
					onRefresh={() => window.location.reload()}
				/>
			</div>
		{:else}
			<!-- Category Sections -->
			{#each categories as category}
				<section class="container mx-auto px-4 mb-12">
					<!-- Category Header -->
					<div class="flex items-center justify-between mb-6">
						<h2 class="text-2xl lg:text-3xl font-bold text-gray-900">
							{category.name}
						</h2>
						<!-- <div class="text-sm text-gray-500">
							{category.products.length} products
						</div> -->
					</div>

					<!-- Products Grid -->
					{#if category.products.length > 0}
						<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
							{#each category.products as product}
								<ProductCard
									id={product.id}
									name={product.name}
									slug={product.slug}
									price={product.price}
									category={category.name}
									shortDescription={product.description}
								/>
							{/each}
						</div>
					{:else}
						<!-- No Products in Category -->
						<div class="text-center py-8">
							<div class="text-gray-400 mb-2">
								<svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
								</svg>
							</div>
							<p class="text-gray-500">No products available in this category</p>
						</div>
					{/if}
				</section>
			{/each}
		{/if}
		</div>
	</main>
</div>
