import type { PageLoad } from './$types';
import { generateProductSlug } from '$lib/api.js';

// Data interfaces
interface ProductCard {
	id: string;
	name: string;
	category: string;
	slug?: string; // Optional slug for image loading, will be generated from name if not provided
	price: number;
	shortDescription?: string;
	isActive: boolean;
}

interface Category {
	id: string;
	name: string;
	products: ProductCard[];
}

export const load: PageLoad = async ({ fetch }) => {
	try {
		// Try to fetch data from backend API
		// Use a large per_page value to get all products
		const [productsResponse, categoriesResponse] = await Promise.all([
			fetch('/api/v1/products?per_page=1000'),
			fetch('/api/v1/categories')
		]);
		
		if (productsResponse.ok && categoriesResponse.ok) {
			const products = await productsResponse.json();
			const categoriesData = await categoriesResponse.json();

			// Transform backend data to frontend format
			const transformedProducts: ProductCard[] = products.map((product: any) => ({
				id: product.id,
				name: product.name,
				category: "General", // Default category, will be updated below
				slug: generateProductSlug(product.name),
				price: parseFloat(product.base_price),
				shortDescription: product.description,
				isActive: product.is_active
			}));

			// Create categories with products
			const categoriesMap = new Map<string, Category>();

			// Initialize categories from backend
			categoriesData.forEach((cat: any) => {
				categoriesMap.set(cat.name, {
					id: cat.id,
					name: cat.name,
					products: []
				});
			});

			// Add a default category if none exist
			if (categoriesMap.size === 0) {
				categoriesMap.set("General", {
					id: "general",
					name: "General",
					products: []
				});
			}

			// Distribute products to categories based on product names
			transformedProducts.forEach(product => {
				let assigned = false;
				const productNameLower = product.name.toLowerCase();

				// Match products to categories by name patterns
				for (const [categoryName, category] of categoriesMap) {
					const categoryNameLower = categoryName.toLowerCase();

					// TV Cabinet category
					if (categoryNameLower.includes("tv") &&
						(productNameLower.includes("tv") || productNameLower.includes("cabinet"))) {
						product.category = categoryName;
						category.products.push(product);
						assigned = true;
						break;
					}

					// Kitchen category
					if (categoryNameLower.includes("kitchen") &&
						(productNameLower.includes("kitchen") || productNameLower.includes("pantry"))) {
						product.category = categoryName;
						category.products.push(product);
						assigned = true;
						break;
					}

					// Accessories category - check for accessory items
					if (categoryNameLower.includes("accessories") &&
						(productNameLower.includes("led") || productNameLower.includes("tandem") ||
						 productNameLower.includes("box") || productNameLower.includes("hinge") ||
						 productNameLower.includes("basket") || productNameLower.includes("lighting") ||
						 productNameLower.includes("glass") || productNameLower.includes("door") ||
						 productNameLower.includes("gola") || productNameLower.includes("soft") ||
						 productNameLower.includes("close") || productNameLower.includes("pull"))) {
						product.category = categoryName;
						category.products.push(product);
						assigned = true;
						break;
					}

					// Test Category - match products with "test" in name
					if (categoryNameLower.includes("test") &&
						productNameLower.includes("test")) {
						product.category = categoryName;
						category.products.push(product);
						assigned = true;
						break;
					}

					// Office category
					if (categoryNameLower.includes("office") &&
						(productNameLower.includes("office") || productNameLower.includes("desk"))) {
						product.category = categoryName;
						category.products.push(product);
						assigned = true;
						break;
					}

					// Wardrobe category
					if (categoryNameLower.includes("wardrobe") &&
						(productNameLower.includes("wardrobe") || productNameLower.includes("closet"))) {
						product.category = categoryName;
						category.products.push(product);
						assigned = true;
						break;
					}
				}

				// If not assigned to any specific category, put in General category
				if (!assigned) {
					console.log(`Product "${product.name}" not assigned to any category, adding to General`);
					// Find or create General category
					let generalCategory = categoriesMap.get("General");
					if (!generalCategory) {
						generalCategory = {
							id: "general",
							name: "General",
							products: []
						};
						categoriesMap.set("General", generalCategory);
					}
					product.category = "General";
					generalCategory.products.push(product);
				}
			});

			// Return ONLY categories that have products (hide empty categories)
			const categories = Array.from(categoriesMap.values()).filter(cat => cat.products.length > 0);

			return {
				products: transformedProducts,
				categories,
				error: null
			};
		}
		
		// If API fails, return mock data for demo
		console.log('API not available, using mock data for homepage');
		
		const mockProducts: ProductCard[] = [
			{
				id: "fantasy-tv-cabinet",
				name: "Fantasy TV Cabinet",
				category: "TV Cabinet",
				slug: "fantasy-tv-cabinet",
				price: 1590000,
				shortDescription: "Modern TV cabinet with premium finish and ample storage space",
				isActive: true
			},
			{
				id: "modern-kitchen-set",
				name: "Modern Kitchen Set",
				category: "Kitchen",
				slug: "modern-kitchen-set",
				price: 2500000,
				shortDescription: "Complete kitchen set with premium materials and modern design",
				isActive: true
			},
			{
				id: "luxury-wardrobe",
				name: "Luxury Wardrobe",
				category: "Wardrobe",
				slug: "luxury-wardrobe",
				price: 3200000,
				shortDescription: "Spacious wardrobe with sliding doors and premium finish",
				isActive: true
			},
			{
				id: "executive-office-desk",
				name: "Executive Office Desk",
				category: "Office",
				slug: "executive-office-desk",
				price: 1800000,
				shortDescription: "Professional office desk with built-in storage and cable management",
				isActive: true
			},
			{
				id: "dining-table-set",
				name: "Dining Table Set",
				category: "Dining",
				slug: "dining-table-set",
				price: 2200000,
				shortDescription: "Elegant dining table set for 6 people with matching chairs",
				isActive: true
			},
			{
				id: "bedroom-set-premium",
				name: "Premium Bedroom Set",
				category: "Bedroom",
				slug: "premium-bedroom-set",
				price: 4500000,
				shortDescription: "Complete bedroom set with king size bed, nightstands, and dresser",
				isActive: true
			},
			{
				id: "minimalist-bookshelf",
				name: "Minimalist Bookshelf",
				category: "Storage",
				slug: "minimalist-bookshelf",
				price: 950000,
				shortDescription: "Clean and modern bookshelf design with multiple compartments",
				isActive: true
			},
			{
				id: "corner-sofa-set",
				name: "Corner Sofa Set",
				category: "Living Room",
				slug: "corner-sofa-set",
				price: 3800000,
				shortDescription: "Comfortable L-shaped sofa perfect for family gatherings",
				isActive: true
			},
			{
				id: "study-desk-compact",
				name: "Compact Study Desk",
				category: "Office",
				slug: "compact-study-desk",
				price: 1200000,
				shortDescription: "Space-saving study desk perfect for small rooms",
				isActive: true
			},
			{
				id: "kitchen-island-marble",
				name: "Marble Kitchen Island",
				category: "Kitchen",
				slug: "marble-kitchen-island",
				price: 5200000,
				shortDescription: "Luxury kitchen island with marble top and built-in storage",
				isActive: true
			},
			{
				id: "walk-in-closet",
				name: "Walk-in Closet System",
				category: "Wardrobe",
				slug: "walk-in-closet-system",
				price: 6800000,
				shortDescription: "Complete walk-in closet system with premium organization features",
				isActive: true
			},
			{
				id: "entertainment-center",
				name: "Entertainment Center",
				category: "TV Cabinet",
				slug: "entertainment-center",
				price: 2800000,
				shortDescription: "Complete entertainment center with TV mount and media storage",
				isActive: true
			},
			// Accessories Category
			{
				id: "led-gola",
				name: "Led Gola",
				category: "Accessories",
				slug: "led-gola",
				price: 250000,
				shortDescription: "LED strip lighting for ambient illumination and modern aesthetics",
				isActive: true
			},
			{
				id: "tandem-box",
				name: "Tandem Box",
				category: "Accessories",
				slug: "tandem-box",
				price: 180000,
				shortDescription: "Soft-close drawer system for smooth and quiet operation",
				isActive: true
			},
			{
				id: "glass-door",
				name: "Glass Door",
				category: "Accessories",
				slug: "glass-door",
				price: 320000,
				shortDescription: "Tempered glass door panels for elegant cabinet fronts",
				isActive: true
			},
			{
				id: "soft-close-hinges",
				name: "Soft Close Hinges",
				category: "Accessories",
				slug: "soft-close-hinges",
				price: 150000,
				shortDescription: "Premium soft-close hinges for cabinet doors",
				isActive: true
			},
			{
				id: "pull-out-basket",
				name: "Pull Out Basket",
				category: "Accessories",
				slug: "pull-out-basket",
				price: 280000,
				shortDescription: "Wire basket organizer with smooth sliding mechanism",
				isActive: true
			},
			{
				id: "cabinet-lighting",
				name: "Cabinet Lighting",
				category: "Accessories",
				slug: "cabinet-lighting",
				price: 350000,
				shortDescription: "Under-cabinet LED lighting system with dimmer control",
				isActive: true
			}
		];
		
		// Group products by category
		const categoriesMap = new Map<string, Category>();
		
		mockProducts.forEach(product => {
			if (!categoriesMap.has(product.category)) {
				categoriesMap.set(product.category, {
					id: product.category.toLowerCase().replace(/\s+/g, '-'),
					name: product.category,
					products: []
				});
			}
			categoriesMap.get(product.category)!.products.push(product);
		});
		
		const categories = Array.from(categoriesMap.values());

		// Check if we have any data
		if (categories.length === 0 || mockProducts.length === 0) {
			return {
				products: [],
				categories: [],
				error: null,
				isEmpty: true,
				message: "No products are currently available. We're working on adding new products to our catalog."
			};
		}

		return {
			products: mockProducts,
			categories,
			error: null,
			isEmpty: false
		};
		
	} catch (error) {
		console.error('Error loading homepage data:', error);
		
		return {
			products: [],
			categories: [],
			error: error instanceof Error ? error.message : 'Failed to load homepage data'
		};
	}
};
