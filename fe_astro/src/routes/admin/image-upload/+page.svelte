<script lang="ts">
	import ProductImageUpload from '$lib/components/ProductImageUpload.svelte';
	import ImageCleanup from '$lib/components/ImageCleanup.svelte';

	let selectedProduct = $state('Kitchen Island Premium');
	let uploadResults: any[] = $state([]);
	let activeTab = $state('upload');

	const sampleProducts = [
		'Kitchen Island Premium',
		'Modern Kitchen Set',
		'Fantasy TV Cabinet',
		'Led Gola',
		'Tandem Box',
		'Pantry Cabinet Large',
		'Pull Out Basket',
		'Cabinet Lighting',
		'Glass Door',
		'Soft Close Hinges'
	];

	function handleUploadComplete(results: any[]) {
		uploadResults = results;
		console.log('Upload completed:', results);
	}

	function handleProgress(progress: number) {
		console.log('Upload progress:', progress);
	}

	function handleCleanupComplete(result: any) {
		console.log('Cleanup completed:', result);
	}
</script>

<svelte:head>
	<title>Image Upload - Astro Works Admin</title>
</svelte:head>

<div class="admin-page">
	<div class="header">
		<h1>Product Image Management</h1>
		<p>Upload, optimize, and manage product images with comprehensive tools</p>
	</div>

	<!-- Tab Navigation -->
	<div class="tab-navigation">
		<button
			class="tab-button {activeTab === 'upload' ? 'active' : ''}"
			onclick={() => activeTab = 'upload'}
		>
			📤 Upload & Optimize
		</button>
		<button
			class="tab-button {activeTab === 'cleanup' ? 'active' : ''}"
			onclick={() => activeTab = 'cleanup'}
		>
			🧹 Cleanup Legacy Files
		</button>
	</div>

	<!-- Upload Tab -->
	{#if activeTab === 'upload'}
		<div class="tab-content">
			<div class="product-selector">
				<label for="product-select">Select Product:</label>
				<select id="product-select" bind:value={selectedProduct}>
					{#each sampleProducts as product}
						<option value={product}>{product}</option>
					{/each}
				</select>
			</div>

			<div class="upload-section">
				<ProductImageUpload
					productName={selectedProduct}
					startIndex={1}
					maxFiles={5}
					onUploadComplete={handleUploadComplete}
					onProgress={handleProgress}
				/>
			</div>
		</div>
	{/if}

	<!-- Cleanup Tab -->
	{#if activeTab === 'cleanup'}
		<div class="tab-content">
			<ImageCleanup
				onCleanupComplete={handleCleanupComplete}
			/>
		</div>
	{/if}
	
	{#if uploadResults.length > 0}
		<div class="results-summary">
			<h2>Upload Summary</h2>
			<div class="summary-stats">
				<div class="stat">
					<span class="stat-label">Total Images:</span>
					<span class="stat-value">{uploadResults.length}</span>
				</div>
				<div class="stat">
					<span class="stat-label">Successful:</span>
					<span class="stat-value success">{uploadResults.filter(r => r.success).length}</span>
				</div>
				<div class="stat">
					<span class="stat-label">Failed:</span>
					<span class="stat-value error">{uploadResults.filter(r => !r.success).length}</span>
				</div>
			</div>
		</div>
	{/if}
	
	<div class="info-section">
		<h2>Image Optimization Features</h2>
		<div class="features-grid">
			<div class="feature">
				<h3>🔧 Automatic Optimization</h3>
				<p>Images are automatically converted to WebP format with 85% quality for optimal web performance</p>
			</div>
			<div class="feature">
				<h3>📱 Dual Aspect Ratios</h3>
				<p>Generates both mobile (4:5) and desktop (16:9) versions for responsive design</p>
			</div>
			<div class="feature">
				<h3>🗜️ Size Reduction</h3>
				<p>Removes metadata and compresses images to target &lt;500KB while maintaining quality</p>
			</div>
			<div class="feature">
				<h3>📏 Smart Resizing</h3>
				<p>Automatically crops and resizes images to maintain aspect ratios and optimal dimensions</p>
			</div>
			<div class="feature">
				<h3>🔒 Security</h3>
				<p>Validates file types, sizes, and dimensions with comprehensive error handling</p>
			</div>
			<div class="feature">
				<h3>⚡ Performance</h3>
				<p>Optimized for fast uploads with progress tracking and batch processing</p>
			</div>
		</div>
	</div>
</div>

<style>
	.admin-page {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
	
	.header {
		text-align: center;
		margin-bottom: 2rem;
		padding-bottom: 2rem;
		border-bottom: 1px solid #e5e7eb;
	}
	
	.header h1 {
		margin: 0 0 0.5rem;
		color: #111827;
		font-size: 2.5rem;
		font-weight: 700;
	}
	
	.header p {
		margin: 0;
		color: #6b7280;
		font-size: 1.125rem;
	}

	.tab-navigation {
		display: flex;
		justify-content: center;
		margin-bottom: 2rem;
		border-bottom: 1px solid #e5e7eb;
	}

	.tab-button {
		padding: 1rem 2rem;
		border: none;
		background: none;
		cursor: pointer;
		font-size: 1rem;
		font-weight: 500;
		color: #6b7280;
		border-bottom: 2px solid transparent;
		transition: all 0.2s ease;
	}

	.tab-button:hover {
		color: #374151;
		background: #f9fafb;
	}

	.tab-button.active {
		color: #3b82f6;
		border-bottom-color: #3b82f6;
		background: #eff6ff;
	}

	.tab-content {
		animation: fadeIn 0.3s ease;
	}

	@keyframes fadeIn {
		from { opacity: 0; transform: translateY(10px); }
		to { opacity: 1; transform: translateY(0); }
	}
	
	.product-selector {
		margin-bottom: 2rem;
		text-align: center;
	}
	
	.product-selector label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 600;
		color: #374151;
	}
	
	.product-selector select {
		padding: 0.75rem 1rem;
		border: 1px solid #d1d5db;
		border-radius: 0.5rem;
		font-size: 1rem;
		background: white;
		min-width: 250px;
	}
	
	.upload-section {
		margin-bottom: 3rem;
	}
	
	.results-summary {
		margin-bottom: 3rem;
		padding: 1.5rem;
		background: #f9fafb;
		border-radius: 0.75rem;
		border: 1px solid #e5e7eb;
	}
	
	.results-summary h2 {
		margin: 0 0 1rem;
		color: #111827;
		font-size: 1.5rem;
		font-weight: 600;
	}
	
	.summary-stats {
		display: flex;
		gap: 2rem;
		justify-content: center;
	}
	
	.stat {
		text-align: center;
	}
	
	.stat-label {
		display: block;
		font-size: 0.875rem;
		color: #6b7280;
		margin-bottom: 0.25rem;
	}
	
	.stat-value {
		display: block;
		font-size: 1.5rem;
		font-weight: 700;
		color: #111827;
	}
	
	.stat-value.success {
		color: #059669;
	}
	
	.stat-value.error {
		color: #dc2626;
	}
	
	.info-section {
		margin-top: 3rem;
	}
	
	.info-section h2 {
		margin: 0 0 2rem;
		text-align: center;
		color: #111827;
		font-size: 1.875rem;
		font-weight: 600;
	}
	
	.features-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 1.5rem;
	}
	
	.feature {
		padding: 1.5rem;
		background: white;
		border-radius: 0.75rem;
		border: 1px solid #e5e7eb;
		box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
	}
	
	.feature h3 {
		margin: 0 0 0.75rem;
		color: #111827;
		font-size: 1.125rem;
		font-weight: 600;
	}
	
	.feature p {
		margin: 0;
		color: #6b7280;
		line-height: 1.6;
	}
	
	@media (max-width: 768px) {
		.admin-page {
			padding: 1rem;
		}
		
		.header h1 {
			font-size: 2rem;
		}
		
		.summary-stats {
			flex-direction: column;
			gap: 1rem;
		}
		
		.features-grid {
			grid-template-columns: 1fr;
		}
	}
</style>
