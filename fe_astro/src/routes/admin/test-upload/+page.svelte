<script lang="ts">
	import { uploadProductImages, getUploadStats, cleanupLegacyImages } from '$lib/api';
	
	let selectedFiles: FileList | null = $state(null);
	let productSlug = $state('test-product');
	let uploading = $state(false);
	let uploadResult: any = $state(null);
	let uploadStats: any = $state(null);
	let cleanupResult: any = $state(null);
	
	// Load stats on mount
	$effect(() => {
		loadStats();
	});
	
	async function loadStats() {
		try {
			uploadStats = await getUploadStats();
		} catch (error) {
			console.error('Failed to load stats:', error);
		}
	}
	
	async function handleUpload() {
		if (!selectedFiles || selectedFiles.length === 0) return;
		
		uploading = true;
		uploadResult = null;
		
		try {
			const result = await uploadProductImages(productSlug, selectedFiles, 1);
			uploadResult = result;
			console.log('Upload result:', result);
			
			// Reload stats after upload
			await loadStats();
		} catch (error) {
			console.error('Upload failed:', error);
			uploadResult = { success: false, message: error.message };
		} finally {
			uploading = false;
		}
	}
	
	async function handleCleanup(dryRun: boolean = true) {
		try {
			cleanupResult = await cleanupLegacyImages(dryRun);
			console.log('Cleanup result:', cleanupResult);
			
			// Reload stats after cleanup
			if (!dryRun) {
				await loadStats();
			}
		} catch (error) {
			console.error('Cleanup failed:', error);
		}
	}
	
	function handleFileSelect(event: Event) {
		const target = event.target as HTMLInputElement;
		selectedFiles = target.files;
	}
</script>

<div class="test-upload-page">
	<h1>Image Upload Test Page</h1>
	
	<!-- Upload Stats -->
	{#if uploadStats}
		<div class="stats-section">
			<h2>Current Upload Statistics</h2>
			<div class="stats-grid">
				<div class="stat-item">
					<strong>Total Files:</strong> {uploadStats.stats?.total_files || 0}
				</div>
				<div class="stat-item">
					<strong>Total Size:</strong> {((uploadStats.stats?.total_size || 0) / 1024 / 1024).toFixed(2)} MB
				</div>
				<div class="stat-item">
					<strong>WebP Files:</strong> {uploadStats.stats?.webp_files || 0}
				</div>
				<div class="stat-item">
					<strong>Legacy Files:</strong> {uploadStats.stats?.legacy_files || 0}
				</div>
			</div>
		</div>
	{/if}
	
	<!-- Upload Test -->
	<div class="upload-section">
		<h2>Test Image Upload</h2>
		<div class="form-group">
			<label for="product-slug">Product Slug:</label>
			<input 
				id="product-slug"
				type="text" 
				bind:value={productSlug}
				placeholder="Enter product slug"
			/>
		</div>
		
		<div class="form-group">
			<label for="file-input">Select Images:</label>
			<input 
				id="file-input"
				type="file" 
				multiple 
				accept="image/*"
				on:change={handleFileSelect}
			/>
		</div>
		
		{#if selectedFiles && selectedFiles.length > 0}
			<div class="selected-files">
				<p><strong>Selected files:</strong> {selectedFiles.length}</p>
				<ul>
					{#each Array.from(selectedFiles) as file}
						<li>{file.name} ({(file.size / 1024).toFixed(1)} KB)</li>
					{/each}
				</ul>
			</div>
		{/if}
		
		<button 
			class="btn btn-primary"
			on:click={handleUpload}
			disabled={uploading || !selectedFiles || selectedFiles.length === 0}
		>
			{uploading ? 'Uploading...' : 'Upload Images'}
		</button>
	</div>
	
	<!-- Upload Results -->
	{#if uploadResult}
		<div class="results-section">
			<h2>Upload Results</h2>
			<div class="result-summary">
				<p><strong>Success:</strong> {uploadResult.success}</p>
				<p><strong>Message:</strong> {uploadResult.message}</p>
				<p><strong>Processed:</strong> {uploadResult.total_processed || 0}</p>
				<p><strong>Failed:</strong> {uploadResult.total_failed || 0}</p>
			</div>
			
			{#if uploadResult.results}
				<div class="detailed-results">
					<h3>Detailed Results</h3>
					{#each uploadResult.results as result, index}
						<div class="result-item {result.success ? 'success' : 'error'}">
							<h4>Image {index + 1}: {result.filename || 'Unknown'}</h4>
							{#if result.success}
								<p><strong>Original Size:</strong> {((result.original_size || 0) / 1024).toFixed(1)} KB</p>
								<p><strong>Optimized Size:</strong> {((result.optimized_size || 0) / 1024).toFixed(1)} KB</p>
								<p><strong>Compression:</strong> {(result.compression_ratio || 0).toFixed(1)}%</p>
								<p><strong>Space Saved:</strong> {(result.space_saved_kb || 0).toFixed(1)} KB</p>
								<p><strong>Format:</strong> {result.original_format} → {result.optimized_format}</p>
								
								{#if result.aspect_ratios}
									<div class="aspect-ratios">
										<strong>Generated Files:</strong>
										<ul>
											{#each result.aspect_ratios as aspect}
												<li>
													{aspect.aspect_ratio}: {aspect.filename} 
													({aspect.dimensions[0]}×{aspect.dimensions[1]}, 
													{aspect.file_size_kb.toFixed(1)}KB)
												</li>
											{/each}
										</ul>
									</div>
								{/if}
							{:else}
								<p class="error"><strong>Error:</strong> {result.error}</p>
							{/if}
						</div>
					{/each}
				</div>
			{/if}
		</div>
	{/if}
	
	<!-- Cleanup Test -->
	<div class="cleanup-section">
		<h2>Test Image Cleanup</h2>
		<div class="cleanup-buttons">
			<button 
				class="btn btn-secondary"
				on:click={() => handleCleanup(true)}
			>
				Preview Cleanup (Dry Run)
			</button>
			<button 
				class="btn btn-danger"
				on:click={() => handleCleanup(false)}
			>
				Perform Cleanup
			</button>
		</div>
		
		{#if cleanupResult}
			<div class="cleanup-result">
				<h3>Cleanup Result</h3>
				<p><strong>Success:</strong> {cleanupResult.success}</p>
				<p><strong>Message:</strong> {cleanupResult.message}</p>
				<p><strong>Files Removed:</strong> {cleanupResult.total_removed || 0}</p>
				<p><strong>Dry Run:</strong> {cleanupResult.dry_run}</p>
				
				{#if cleanupResult.files_removed && cleanupResult.files_removed.length > 0}
					<div class="removed-files">
						<strong>Files:</strong>
						<ul>
							{#each cleanupResult.files_removed.slice(0, 5) as file}
								<li>{file}</li>
							{/each}
							{#if cleanupResult.files_removed.length > 5}
								<li>... and {cleanupResult.files_removed.length - 5} more</li>
							{/if}
						</ul>
					</div>
				{/if}
			</div>
		{/if}
	</div>
	
	<!-- Refresh Stats -->
	<div class="refresh-section">
		<button class="btn btn-primary" on:click={loadStats}>
			Refresh Statistics
		</button>
	</div>
</div>

<style>
	.test-upload-page {
		max-width: 1000px;
		margin: 0 auto;
		padding: 2rem;
	}
	
	h1, h2, h3 {
		color: #1f2937;
		margin-bottom: 1rem;
	}
	
	.stats-section,
	.upload-section,
	.results-section,
	.cleanup-section,
	.refresh-section {
		margin-bottom: 2rem;
		padding: 1.5rem;
		background: #f9fafb;
		border-radius: 0.5rem;
		border: 1px solid #e5e7eb;
	}
	
	.stats-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 1rem;
	}
	
	.stat-item {
		padding: 1rem;
		background: white;
		border-radius: 0.375rem;
		border: 1px solid #d1d5db;
	}
	
	.form-group {
		margin-bottom: 1rem;
	}
	
	label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 500;
		color: #374151;
	}
	
	input[type="text"],
	input[type="file"] {
		width: 100%;
		padding: 0.5rem;
		border: 1px solid #d1d5db;
		border-radius: 0.375rem;
		font-size: 0.875rem;
	}
	
	.selected-files {
		margin: 1rem 0;
		padding: 1rem;
		background: #f0fdf4;
		border-radius: 0.375rem;
		border: 1px solid #bbf7d0;
	}
	
	.btn {
		padding: 0.75rem 1.5rem;
		border-radius: 0.375rem;
		font-weight: 500;
		cursor: pointer;
		border: none;
		margin-right: 0.5rem;
		margin-bottom: 0.5rem;
	}
	
	.btn:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}
	
	.btn-primary {
		background: #3b82f6;
		color: white;
	}
	
	.btn-secondary {
		background: #6b7280;
		color: white;
	}
	
	.btn-danger {
		background: #ef4444;
		color: white;
	}
	
	.result-item {
		margin-bottom: 1rem;
		padding: 1rem;
		border-radius: 0.375rem;
		border: 1px solid #d1d5db;
	}
	
	.result-item.success {
		background: #f0fdf4;
		border-color: #bbf7d0;
	}
	
	.result-item.error {
		background: #fef2f2;
		border-color: #fecaca;
	}
	
	.error {
		color: #dc2626;
	}
	
	ul {
		margin: 0.5rem 0;
		padding-left: 1.5rem;
	}
	
	li {
		margin: 0.25rem 0;
	}
</style>
