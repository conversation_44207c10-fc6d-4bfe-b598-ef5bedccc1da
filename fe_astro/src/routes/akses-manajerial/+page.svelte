<script>
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';
	import { config } from '$lib/config/env';

	let email = '';
	let password = '';
	let loading = false;
	let error = '';
	let attempts = 0;
	let isRateLimited = false;

	// Check if already logged in
	onMount(() => {
		if (browser) {
			const token = localStorage.getItem('admin_token');
			if (token) {
				// Verify token is still valid
				verifyToken(token);
			}
		}
	});

	async function verifyToken(token) {
		try {
			const response = await fetch(`${config.api.url}/auth/me`, {
				headers: {
					'Authorization': `Bearer ${token}`
				}
			});

			if (response.ok) {
				// Token is valid, redirect to admin dashboard
				goto('/manajemen');
			} else {
				// Token is invalid, remove it
				localStorage.removeItem('admin_token');
				localStorage.removeItem('admin_user');
			}
		} catch (err) {
			// Network error, remove token to be safe
			localStorage.removeItem('admin_token');
			localStorage.removeItem('admin_user');
		}
	}

	async function handleLogin(event) {
		event.preventDefault();
		
		if (loading || isRateLimited) return;

		// Basic validation
		if (!email.trim() || !password.trim()) {
			error = 'Email dan password harus diisi.';
			return;
		}

		loading = true;
		error = '';

		try {
			const response = await fetch(`${config.api.url}/auth/login`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					email: email.trim(),
					password: password
				})
			});

			if (!response.ok) {
				// Handle non-200 responses
				let errorMessage = 'Login failed';
				try {
					const errorData = await response.json();
					errorMessage = errorData.message || errorMessage;
				} catch {
					// If response is not JSON, use status text
					errorMessage = response.statusText || errorMessage;
				}

				attempts++;
				if (response.status === 429) {
					error = errorMessage;
					isRateLimited = true;
					setTimeout(() => {
						isRateLimited = false;
						attempts = 0;
					}, 15 * 60 * 1000);
				} else {
					error = 'Email atau password salah.';
					if (attempts >= 3) {
						error += ' Hati-hati, akun akan diblokir sementara setelah 5 percobaan gagal.';
					}
				}
				password = '';
				return;
			}

			const data = await response.json();

			if (data.token) {
				// Store token and user info
				localStorage.setItem('admin_token', data.token);
				localStorage.setItem('admin_user', JSON.stringify(data.admin_user));

				// Reset attempts on successful login
				attempts = 0;

				// Redirect to admin dashboard
				goto('/manajemen');
			} else {
				// Handle case where response is OK but no token (shouldn't happen)
				error = 'Login failed: No token received';
				password = '';
			}
		} catch (err) {
			error = 'Terjadi kesalahan koneksi. Silakan coba lagi.';
			console.error('Login error:', err);
		} finally {
			loading = false;
		}
	}

	function handleKeyPress(event) {
		if (event.key === 'Enter' && !loading && !isRateLimited) {
			handleLogin(event);
		}
	}
</script>

<svelte:head>
	<title>Admin Login - Astro Works</title>
	<meta name="robots" content="noindex, nofollow">
</svelte:head>

<div class="login-container">
	<div class="login-card">
		<div class="logo">
			<span class="logo-icon">🚀</span>
			<h1>Astro Works</h1>
			<p>Admin Dashboard</p>
		</div>

		{#if error}
			<div class="error-message" role="alert">
				{error}
			</div>
		{/if}

		<form on:submit={handleLogin} class="login-form">
			<div class="form-group">
				<label for="email">Email</label>
				<input
					type="email"
					id="email"
					bind:value={email}
					disabled={loading || isRateLimited}
					required
					autocomplete="email"
					on:keypress={handleKeyPress}
				/>
			</div>

			<div class="form-group">
				<label for="password">Password</label>
				<input
					type="password"
					id="password"
					bind:value={password}
					disabled={loading || isRateLimited}
					required
					autocomplete="current-password"
					on:keypress={handleKeyPress}
				/>
			</div>

			<button
				type="submit"
				class="login-button"
				disabled={loading || isRateLimited || !email.trim() || !password.trim()}
			>
				{#if loading}
					<span class="spinner"></span>
					Masuk...
				{:else if isRateLimited}
					Diblokir Sementara
				{:else}
					Masuk
				{/if}
			</button>
		</form>

		<div class="footer">
			<p>Astro Works E-commerce Platform</p>
		</div>
	</div>
</div>

<style>
	.login-container {
		min-height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 1rem;
	}

	.login-card {
		background: white;
		padding: 2.5rem;
		border-radius: 12px;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
		width: 100%;
		max-width: 400px;
	}

	.logo {
		text-align: center;
		margin-bottom: 2rem;
	}

	.logo-icon {
		font-size: 3rem;
		display: block;
		margin-bottom: 0.5rem;
	}

	.logo h1 {
		color: #333;
		font-size: 2rem;
		margin-bottom: 0.5rem;
		font-weight: 700;
	}

	.logo p {
		color: #666;
		font-size: 1rem;
	}

	.error-message {
		background: #fee;
		color: #c33;
		padding: 1rem;
		border-radius: 8px;
		margin-bottom: 1.5rem;
		border: 1px solid #fcc;
		font-size: 0.9rem;
		line-height: 1.4;
	}

	.login-form {
		margin-bottom: 2rem;
	}

	.form-group {
		margin-bottom: 1.5rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		color: #333;
		font-weight: 500;
	}

	.form-group input {
		width: 100%;
		padding: 0.75rem;
		border: 2px solid #e1e5e9;
		border-radius: 8px;
		font-size: 1rem;
		transition: border-color 0.2s ease;
	}

	.form-group input:focus {
		outline: none;
		border-color: #667eea;
		box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
	}

	.form-group input:disabled {
		background-color: #f5f5f5;
		cursor: not-allowed;
	}

	.login-button {
		width: 100%;
		padding: 0.875rem;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		border: none;
		border-radius: 8px;
		font-size: 1rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
	}

	.login-button:hover:not(:disabled) {
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
	}

	.login-button:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
		box-shadow: none;
	}

	.spinner {
		width: 16px;
		height: 16px;
		border: 2px solid transparent;
		border-top: 2px solid currentColor;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		to {
			transform: rotate(360deg);
		}
	}

	.footer {
		text-align: center;
		color: #666;
		font-size: 0.85rem;
	}

	@media (max-width: 480px) {
		.login-card {
			padding: 2rem;
		}

		.logo h1 {
			font-size: 1.75rem;
		}
	}
</style>
