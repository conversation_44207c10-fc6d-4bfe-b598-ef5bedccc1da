<script lang="ts">
	/**
	 * Checkout Page Component
	 * Handles cart management, order processing, and WhatsApp integration
	 * Groups products and accessories by category as specified
	 */
	
	import { onMount } from 'svelte';
	import Header from '$lib/components/Header.svelte';
	import { browser } from '$app/environment';
	import { getWhatsAppUrlFromBackend, config } from '$lib/config/env';
	
	// Interfaces
	interface CartItem {
		id: string;
		name: string;
		category: string;
		price: number;
		quantity: number;
		selectedTheme?: string;
		selectedSize?: string;
		accessories?: CartAccessory[];
		isStandalone?: boolean;
		// Enhanced data for offline functionality
		image?: string;
		description?: string;
		selectedThemeData?: {
			id: string;
			name: string;
			color: string;
			isDefault?: boolean;
		};
		selectedSizeData?: {
			id: string;
			label: string;
			price: number;
			dimensions: {
				width: number;
				height: number;
				unit: string;
			};
			isDefault?: boolean;
		};
		productData?: {
			baseName: string;
			shortDescription?: string;
			longDescription?: string;
			images: string[];
			themes: any[];
			sizes: any[];
			availableAccessories: any[];
			specifications: any[];
		};
	}
	
	interface CartAccessory {
		id: string;
		name: string;
		price: number;
		quantity: number;
		selectedOptions?: string;
		// Enhanced data for offline functionality
		description?: string;
		image?: string;
	}
	
	interface GroupedCart {
		[category: string]: CartItem[];
	}
	
	// Component state
	let cartItems = $state<CartItem[]>([]);
	let loading = $state(true);
	let orderProcessing = $state(false);
	let orderId = $state<string>('');
	
	// Derived values
	let groupedCart = $derived(() => {
		const grouped: GroupedCart = {};
		const standaloneAccessories: CartItem[] = [];

		cartItems.forEach(item => {
			if (item.isStandalone && item.category === 'Accessories') {
				standaloneAccessories.push(item);
			} else {
				if (!grouped[item.category]) {
					grouped[item.category] = [];
				}
				grouped[item.category].push(item);
			}
		});

		// Add standalone accessories as a separate category if they exist
		if (standaloneAccessories.length > 0) {
			grouped['Accessories'] = standaloneAccessories;
		}

		return grouped;
	});

	// Get all non-standalone items for continuous numbering
	let mainProducts = $derived(() => {
		return cartItems.filter(item => !item.isStandalone);
	});

	// Create a map for item numbering
	let itemNumbers = $derived(() => {
		const numbers = new Map();
		let counter = 1;
		mainProducts().forEach(item => {
			numbers.set(item.id, counter++);
		});
		return numbers;
	});
	
	let totalAmount = $derived(() => {
		return cartItems.reduce((total, item) => {
			let itemTotal = item.price * item.quantity;
			
			// Add accessories total
			if (item.accessories) {
				itemTotal += item.accessories.reduce((accTotal, acc) => {
					return accTotal + (acc.price * acc.quantity * item.quantity);
				}, 0);
			}
			
			return total + itemTotal;
		}, 0);
	});
	
	/**
	 * Format price to Indonesian Rupiah
	 */
	function formatPrice(price: number): string {
		return new Intl.NumberFormat('id-ID').format(price);
	}
	
	/**
	 * Enhanced cart loading with API fallback
	 */
	async function loadCartWithDetails() {
		if (!browser) return;

		loading = true;

		try {
			const savedCart = localStorage.getItem('astro-works-cart');
			if (!savedCart) {
				cartItems = [];
				console.log('No cart found in localStorage, starting with empty cart');
				loading = false;
				return;
			}

			const parsedCart = JSON.parse(savedCart);
			cartItems = Array.isArray(parsedCart) ? parsedCart : [];
			console.log('Cart loaded from localStorage:', $state.snapshot(cartItems));

			// Try to enrich cart data with fresh API data
			for (let i = 0; i < cartItems.length; i++) {
				const item = cartItems[i];
				try {
					// Try to fetch fresh product data
					const response = await fetch(`/api/v1/products/${item.id}`);
					if (response.ok) {
						const freshData = await response.json();
						// Merge fresh API data with cart data, preserving user selections
						cartItems[i] = {
							...item,
							// Update with fresh data but keep user selections
							name: item.name || freshData.name,
							category: item.category || freshData.category,
							image: item.image || freshData.image_url || '/images/placeholder.jpg',
							description: item.description || freshData.description || freshData.short_description
						};
						console.log(`Updated product ${item.id} with fresh API data`);
					}
				} catch (error) {
					console.warn(`API unavailable for product ${item.id}, using localStorage data`);
					// Validate that we have minimum required data
					if (!item.name || !item.price) {
						console.error(`Incomplete cart data for product ${item.id}:`, item);
						// Provide fallback values
						cartItems[i] = {
							...item,
							name: item.name || 'Unknown Product',
							image: item.image || '/images/placeholder.jpg',
							description: item.description || 'Product description not available',
							category: item.category || 'Furniture'
						};
					}
				}
			}

			// Filter out any items that are still invalid
			cartItems = cartItems.filter(item => item.name && item.price && item.quantity > 0);

		} catch (error) {
			console.error('Error loading cart:', error);
			cartItems = [];
		} finally {
			loading = false;
		}
	}

	/**
	 * Add a test item to cart for debugging
	 */
	function addTestItem() {
		const testItem = {
			id: "test-product",
			name: "Test Product",
			category: "Test Category",
			price: 1000000,
			quantity: 1,
			selectedTheme: "Test Theme",
			selectedSize: "Test Size",
			accessories: []
		};

		cartItems = [testItem];
		saveCart();
		console.log('Test item added to cart:', testItem);
	}

	/**
	 * Load demo cart data for testing (call manually if needed)
	 */
	function loadDemoCart() {
		cartItems = [
			{
				id: "fantasy-tv-cabinet",
				name: "Fantasy TV Cabinet",
				category: "TV Cabinet",
				price: 1590000,
				quantity: 1,
				selectedTheme: "Hitam",
				selectedSize: "2.4x2.7m",
				// Enhanced data for offline functionality
				image: "/images/products/fantasy-tv-cabinet.jpg",
				description: "Modern TV cabinet with premium finish and ample storage space",
				isStandalone: false,
				selectedThemeData: {
					id: "theme-black",
					name: "Hitam",
					color: "#000000",
					isDefault: true
				},
				selectedSizeData: {
					id: "size-240x270",
					label: "2.4x2.7m",
					price: 1590000,
					dimensions: { width: 240, height: 270, unit: "cm" },
					isDefault: true
				},
				accessories: [
					{
						id: "led-gola",
						name: "Led Gola",
						price: 3000000,
						quantity: 2,
						selectedOptions: "2 METER",
						description: "LED strip lighting for cabinet interior",
						image: "/images/accessories/led-gola.jpg"
					},
					{
						id: "tandem-box",
						name: "Tandem Box",
						price: 1500000,
						quantity: 3,
						description: "Pull-out storage drawer system",
						image: "/images/accessories/tandem-box.jpg"
					}
				],
				productData: {
					baseName: "Fantasy TV Cabinet",
					shortDescription: "Modern TV cabinet with premium finish",
					longDescription: "A sophisticated TV cabinet designed for modern living spaces with ample storage and premium materials.",
					images: ["/images/products/fantasy-tv-cabinet.jpg"],
					themes: [{ id: "theme-black", name: "Hitam", color: "#000000", isDefault: true }],
					sizes: [{ id: "size-240x270", label: "2.4x2.7m", price: 1590000, dimensions: { width: 240, height: 270, unit: "cm" }, isDefault: true }],
					availableAccessories: [],
					specifications: []
				}
			},
			{
				id: "modern-kitchen-set",
				name: "Kitchen Set Astro",
				category: "Kitchen",
				price: 1200000,
				quantity: 1,
				selectedTheme: "Putih",
				selectedSize: "2x3m",
				// Enhanced data for offline functionality
				image: "/images/products/modern-kitchen-set.jpg",
				description: "Complete kitchen set with modern design and functionality",
				isStandalone: false,
				selectedThemeData: {
					id: "theme-white",
					name: "Putih",
					color: "#FFFFFF",
					isDefault: false
				},
				selectedSizeData: {
					id: "size-200x300",
					label: "2x3m",
					price: 1200000,
					dimensions: { width: 200, height: 300, unit: "cm" },
					isDefault: true
				},
				accessories: [
					{
						id: "led-ambalan",
						name: "Led Ambalan",
						price: 1000000,
						quantity: 1,
						selectedOptions: "10 METER",
						description: "Under-cabinet LED lighting system",
						image: "/images/accessories/led-ambalan.jpg"
					}
				],
				productData: {
					baseName: "Kitchen Set Astro",
					shortDescription: "Complete kitchen set with modern design",
					longDescription: "A comprehensive kitchen solution featuring modern design, quality materials, and optimal functionality for contemporary homes.",
					images: ["/images/products/modern-kitchen-set.jpg"],
					themes: [{ id: "theme-white", name: "Putih", color: "#FFFFFF", isDefault: false }],
					sizes: [{ id: "size-200x300", label: "2x3m", price: 1200000, dimensions: { width: 200, height: 300, unit: "cm" }, isDefault: true }],
					availableAccessories: [],
					specifications: []
				}
			},
			{
				id: "led-strip",
				name: "Led Strip",
				category: "Accessories",
				price: 1000000,
				quantity: 5,
				isStandalone: true,
				// Enhanced data for offline functionality
				image: "/images/accessories/led-strip.jpg",
				description: "High-quality LED strip lighting for various applications",
				productData: {
					baseName: "Led Strip",
					shortDescription: "High-quality LED strip lighting",
					longDescription: "Versatile LED strip lighting solution perfect for accent lighting, under-cabinet illumination, and decorative purposes.",
					images: ["/images/accessories/led-strip.jpg"],
					themes: [],
					sizes: [],
					availableAccessories: [],
					specifications: []
				}
			}
		];
		saveCart();
		console.log('Demo cart loaded with enhanced data');
	}
	
	/**
	 * Save cart to localStorage
	 */
	function saveCart() {
		if (!browser) return;
		localStorage.setItem('astro-works-cart', JSON.stringify(cartItems));
	}
	
	/**
	 * Update item quantity
	 */
	function updateQuantity(itemId: string, newQuantity: number) {
		cartItems = cartItems.map(item => {
			if (item.id === itemId) {
				return { ...item, quantity: Math.max(0, newQuantity) };
			}
			return item;
		}).filter(item => item.quantity > 0);

		saveCart();
	}

	/**
	 * Update accessory quantity within a product
	 */
	function updateAccessoryQuantity(productId: string, accessoryId: string, newQuantity: number) {
		cartItems = cartItems.map(item => {
			if (item.id === productId && item.accessories) {
				const updatedAccessories = item.accessories.map(acc => {
					if (acc.id === accessoryId) {
						return { ...acc, quantity: Math.max(0, newQuantity) };
					}
					return acc;
				}).filter(acc => acc.quantity > 0);

				return { ...item, accessories: updatedAccessories };
			}
			return item;
		});

		saveCart();
	}

	/**
	 * Remove item from cart
	 */
	function removeItem(itemId: string) {
		cartItems = cartItems.filter(item => item.id !== itemId);
		saveCart();
	}
	
	/**
	 * Generate order ID
	 */
	function generateOrderId(): string {
		const now = new Date();
		const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
		const timeStr = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
		return `INV-${dateStr}-${timeStr}`;
	}
	
	/**
	 * Generate WhatsApp message
	 */
	function generateWhatsAppMessage(): string {
		const currentOrderId = orderId || generateOrderId();
		
		let message = `Halo, saya telah melakukan pemesanan dengan detail berikut:\n\n`;
		message += `🧾 ID Pesanan: ${currentOrderId}\n`;
		message += `📦 Rincian Pesanan:\n\n`;
		
		let itemNumber = 1;
		
		// Group items by category (excluding standalone accessories)
		Object.entries(groupedCart()).forEach(([category, items]: [string, CartItem[]]) => {
			if (category === 'Accessories') return; // Handle separately

			items.forEach((item: CartItem) => {
				if (item.isStandalone) return; // Skip standalone accessories here

				message += `${itemNumber}. ${item.name}`;
				if (item.selectedTheme) message += ` (${item.selectedTheme}`;
				if (item.selectedSize) message += `, ${item.selectedSize}`;
				if (item.selectedTheme || item.selectedSize) message += `)`;
				message += ` x${item.quantity} — Rp ${formatPrice(item.price * item.quantity)}\n`;

				// Add accessories for this item
				if (item.accessories && item.accessories.length > 0) {
					item.accessories.forEach((acc: CartAccessory) => {
						const accTotal = acc.price * acc.quantity * item.quantity;
						message += `   ├─ ${acc.name}`;
						if (acc.selectedOptions) message += ` (${acc.selectedOptions})`;
						message += ` x${acc.quantity * item.quantity} — Rp ${formatPrice(accTotal)}\n`;
					});
				}

				message += `\n`;
				itemNumber++;
			});
		});
		
		// Add standalone accessories
		const standaloneAccessories = cartItems.filter(item => item.isStandalone);
		if (standaloneAccessories.length > 0) {
			message += `${itemNumber}. Accessories:\n`;
			standaloneAccessories.forEach(item => {
				const itemTotal = item.price * item.quantity;
				message += `   ├─ ${item.name} x${item.quantity} — Rp ${formatPrice(itemTotal)}\n`;
			});
			message += `\n`;
		}
		
		message += `💰 Total Tagihan: Rp ${formatPrice(totalAmount())}\n\n`;
		message += `Silakan 	nsfer ke:\n`;
		message += `🏦 Bank BCA\n`;
		message += `🔸 Astro Works Indonesia PT\n`;
		message += `🔸 **********\n\n`;
		message += `📄 Unduh Nota:\n`;
		message += `${config.frontend.url}/invoice/${currentOrderId}.pdf`;
		
		return message;
	}
	
	/**
	 * Process order and generate WhatsApp link
	 */
	async function processOrder() {
		if (cartItems.length === 0) return;
		
		orderProcessing = true;
		
		try {
			// Generate order ID
			const currentOrderId = generateOrderId();
			orderId = currentOrderId;
			
			// Prepare order data
			const orderData = {
				orderId: currentOrderId,
				items: cartItems,
				total: totalAmount,
				timestamp: new Date().toISOString(),
				status: 'pending'
			};
			
			// Save transaction (would normally be sent to backend)
			console.log('Order data:', orderData);
			
			// Generate WhatsApp message
			const message = generateWhatsAppMessage();
			const whatsappUrl = await getWhatsAppUrlFromBackend(message);
			
			// Open WhatsApp
			window.open(whatsappUrl, '_blank');
			
			// Clear cart after successful order
			cartItems = [];
			saveCart();
			
		} catch (error) {
			console.error('Error processing order:', error);
			alert('Terjadi kesalahan saat memproses pesanan. Silakan coba lagi.');
		} finally {
			orderProcessing = false;
		}
	}
	
	// Load cart on mount
	onMount(() => {
		loadCartWithDetails();
	});
</script>

<svelte:head>
	<title>Checkout - Astro Works</title>
	<meta name="description" content="Complete your order with Astro Works" />
</svelte:head>

<!-- Checkout Page -->
<div class="min-h-screen bg-gray-50">
	
	<Header />
	<!-- Header -->
	<nav class="bg-white py-8">
		<div class="container mx-auto px-4 py-4">
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-4">
					<a href="/" class="text-blue-600 hover:text-blue-700" aria-label="Back to homepage">
						<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
						</svg>
					</a>
					<h1 class="text-2xl font-bold text-gray-900">Keranjang Belanjaf</h1>
				</div>
				<!-- <div class="text-sm text-gray-500">
					{cartItems.length} item{cartItems.length !== 1 ? 's' : ''}
				</div> -->
			</div>
		</div>
	</nav>

	<!-- Main Content -->
	<main class="container mx-auto px-4 py-8">
		{#if loading}
			<!-- Loading State -->
			<div class="text-center py-12">
				<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
				<p class="mt-4 text-gray-600">Loading cart...</p>
			</div>
		{:else if cartItems.length === 0}
			<!-- Empty Cart -->
			<div class="text-center py-12">
				<div class="text-gray-400 mb-4">
					<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
					</svg>
				</div>
				<h2 class="text-xl font-semibold text-gray-900 mb-2">Your cart is empty</h2>
				<p class="text-gray-600 mb-4">Add some products to get started</p>
				<div class="space-y-3">
					<a href="/" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-block">
						Continue Shopping
					</a>
					<button
						onclick={addTestItem}
						class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors block mx-auto"
					>
						Add Test Item
					</button>
					<button
						onclick={loadDemoCart}
						class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors block mx-auto"
					>
						Load Demo Cart (for testing)
					</button>
				</div>
			</div>
		{:else}
			<!-- Cart Content -->
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
				<!-- Cart Items -->
				<div class="lg:col-span-2">
					<!-- Shopping Cart Header -->
					<div class="bg-white rounded-lg  mb-6">

						<!-- Cart Items List -->
							{#each Object.entries(groupedCart()) as [category, items]}
								<!-- Category Section -->
								<div class="space-y-6">
									{#each items as item}
										<!-- Product Item -->
										<div class="space-y-4">
											<!-- Product Header -->
											<div class="flex items-start justify-between">
												<div class="flex-1">
													<h3 class="text-lg font-bold text-gray-900 mb-2">
														{#if !item.isStandalone}
															{itemNumbers().get(item.id) || 1}. {item.name.toUpperCase()}
														{:else}
															{item.name.toUpperCase()}
														{/if}
													</h3>
												</div>
											</div>

										<!-- Main Product -->
										<div class="flex items-center space-x-4 py-4">
											<!-- Product Image -->
											<div class="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
												{#if item.image && item.image !== '/images/placeholder.jpg'}
													<img
														src={item.image}
														alt={item.name}
														class="w-full h-full object-cover"
														onerror={() => {
															// Handle image load error by hiding image and showing placeholder
															console.log(`Failed to load image for ${item.name}`);
														}}
													/>
												{:else}
													<svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
													</svg>
												{/if}
											</div>

											<!-- Product Details -->
											<div class="flex-1">
												<h4 class="font-bold text-gray-900 uppercase text-lg">{item.name}</h4>
												{#if item.selectedTheme || item.selectedSize}
													<p class="text-gray-600 mt-1">
														{#if item.selectedTheme}{item.selectedTheme}{/if}
														{#if item.selectedTheme && item.selectedSize} {item.selectedSize}{/if}
														{#if !item.selectedTheme && item.selectedSize}{item.selectedSize}{/if}
													</p>
												{/if}
												<p class="text-lg font-bold text-gray-900 mt-2">
													Rp {formatPrice(item.price)}
												</p>
											</div>

											<!-- Quantity Controls -->
											<div class="flex items-center space-x-3">
												<button
													class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors"
													onclick={() => updateQuantity(item.id, item.quantity - 1)}
													aria-label="Decrease quantity"
												>
													<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
													</svg>
												</button>
												<span class="w-8 text-center font-semibold text-lg">{item.quantity}</span>
												<button
													class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors"
													onclick={() => updateQuantity(item.id, item.quantity + 1)}
													aria-label="Increase quantity"
												>
													<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
													</svg>
												</button>
											</div>

											<!-- Remove Button -->
											<button
												class="text-red-500 hover:text-red-700 p-2 transition-colors"
												onclick={() => removeItem(item.id)}
												aria-label="Remove item"
											>
												<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
												</svg>
											</button>
										</div>

										<!-- Accessories -->
										{#if item.accessories && item.accessories.length > 0}
											<div class="ml-24 space-y-4  pl-6">
												{#each item.accessories as accessory}
													<div class="flex items-center space-x-4 py-3">
														<!-- Accessory Image -->
														<div class="w-16 h-16 bg-gray-50 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
															<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
															</svg>
														</div>

														<!-- Accessory Details -->
														<div class="flex-1">
															<h5 class="font-semibold text-gray-800">{accessory.name}</h5>
															{#if accessory.selectedOptions}
																<p class="text-gray-600">{accessory.selectedOptions}</p>
															{/if}
															<p class="text-lg font-bold text-gray-900 mt-1">
																Rp {formatPrice(accessory.price)}
															</p>
														</div>

														<!-- Accessory Quantity -->
														<div class="flex items-center space-x-3">
															<button
																class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors"
																onclick={() => updateAccessoryQuantity(item.id, accessory.id, accessory.quantity - 1)}
																aria-label="Decrease accessory quantity"
															>
																<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																	<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
																</svg>
															</button>
															<span class="w-8 text-center font-semibold text-lg">{accessory.quantity}</span>
															<button
																class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors"
																onclick={() => updateAccessoryQuantity(item.id, accessory.id, accessory.quantity + 1)}
																aria-label="Increase accessory quantity"
															>
																<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																	<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
																</svg>
															</button>
														</div>
													</div>
												{/each}
											</div>
										{/if}
									</div>
								{/each}
							</div>
						{/each}
					</div>
				</div>
				
				<!-- Order Summary -->
				<div class="lg:col-span-1">
					<div class="bg-white rounded-lg sticky top-4">
						<!-- Total Section -->
						<div class="p-6">
							<div class="text-right">
								<p class="text-sm text-gray-600 mb-1">TOTAL TAGIHAN</p>
								<p class="text-3xl font-bold text-gray-900">{formatPrice(totalAmount())}</p>
							</div>
						</div>

						<!-- Transfer Information -->
						<div class="p-6">
							<h4 class="font-semibold text-gray-900 mb-3">Transfer</h4>
							<div class="flex  items-center gap-6 justify-between">
								<div class="space-y-2 text-sm">
									<p class="text-gray-700">Bank BCA</p>
									<p class="text-gray-700">Astro Works Indonesia PT</p>
									<p class="font-mono text-gray-900">**********</p>
								</div>
								<div class="">

								<button
									class=" w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
									onclick={processOrder}
									disabled={orderProcessing || cartItems.length === 0}
								>
									{#if orderProcessing}
										<div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
										<span>Processing...</span>
									{:else}
										<!-- <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
											<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.595z"/>
										</svg> -->
										<span>Konfirmasi Wa </span>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
	<path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3" />
	</svg>

									{/if}
								</button>
								</div>
							</div>
						</div>

						<!-- Confirmation Button -->
						<div class="p-6">
						</div>
					</div>
				</div>
			</div>
		{/if}
	</main>
</div>
