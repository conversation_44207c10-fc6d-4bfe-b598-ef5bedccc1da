<script>
	import { config } from '$lib/config/env';
	import { env } from '$env/dynamic/public';

	function testApi() {
		const resultDiv = document.getElementById('api-result');
		if (!resultDiv) return;

		resultDiv.innerHTML = 'Testing...';

		fetch(`${config.api.url}/health`)
			.then(response => {
				resultDiv.innerHTML = `<span class="text-green-600">✅ Success: ${response.status}</span>`;
			})
			.catch(error => {
				resultDiv.innerHTML = `<span class="text-red-600">❌ Error: ${error.message}</span>`;
			});
	}
</script>

<div class="p-8">
	<h1 class="text-2xl font-bold mb-4">Environment Debug</h1>
	
	<div class="mb-6">
		<h2 class="text-xl font-semibold mb-2">Config Object:</h2>
		<pre class="bg-gray-100 p-4 rounded overflow-auto">{JSON.stringify(config, null, 2)}</pre>
	</div>
	
	<div class="mb-6">
		<h2 class="text-xl font-semibold mb-2">Raw Environment Variables:</h2>
		<pre class="bg-gray-100 p-4 rounded overflow-auto">{JSON.stringify(env, null, 2)}</pre>
	</div>
	
	<div class="mb-6">
		<h2 class="text-xl font-semibold mb-2">API Test:</h2>
		<p>API URL: <code class="bg-gray-200 px-2 py-1 rounded">{config.api.url}</code></p>
		<button
			class="bg-blue-500 text-white px-4 py-2 rounded mt-2"
			onclick={testApi}
		>
			Test API Connection
		</button>
		<div id="api-result" class="mt-2"></div>
	</div>
</div>


