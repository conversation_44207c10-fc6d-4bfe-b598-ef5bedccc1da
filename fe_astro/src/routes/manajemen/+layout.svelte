<script>
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';
	import { config } from '$lib/config/env';

	// State management
	let sidebarOpen = false;
	let isMobile = false;
	let adminUser = null;
	let loading = true;
	let authChecked = false;
	let showUserMenu = false;

	// Navigation items
	const navItems = [
		{
			href: '/manajemen',
			label: 'Dashboard',
			icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z'
		},
		{
			href: '/manajemen/produk',
			label: 'Products',
			icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4'
		},
		{
			href: '/manajemen/kategori',
			label: 'Categories',
			icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10'
		},
		{
			href: '/manajemen/pesanan',
			label: 'Orders',
			icon: 'M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z'
		}
	];

	// Functions
	function toggleSidebar() {
		sidebarOpen = !sidebarOpen;
	}

	function closeSidebar() {
		sidebarOpen = false;
	}

	function handleKeydown(event) {
		if (event.key === 'Escape') {
			closeSidebar();
		}
	}

	function checkMobileView() {
		isMobile = window.innerWidth < 768;
		if (!isMobile && sidebarOpen) {
			sidebarOpen = false;
		}
	}

	function isActiveRoute(href) {
		if (href === '/manajemen') {
			return $page.url.pathname === '/manajemen';
		}
		return $page.url.pathname.startsWith(href);
	}

	// Reactive statements
	$: if ($page.url.pathname && isMobile) {
		sidebarOpen = false;
	}

	// Authentication check
	async function checkAuthentication() {
		if (!browser) return;

		const token = localStorage.getItem('admin_token');
		if (!token) {
			goto('/akses-manajerial');
			return;
		}

		try {
			const response = await fetch(`${config.api.url}/auth/me`, {
				headers: {
					'Authorization': `Bearer ${token}`
				}
			});

			if (response.ok) {
				const userData = await response.json();
				adminUser = userData;
				authChecked = true;
			} else {
				// Token is invalid, remove it and redirect
				localStorage.removeItem('admin_token');
				localStorage.removeItem('admin_user');
				goto('/akses-manajerial');
			}
		} catch (error) {
			console.error('Auth check failed:', error);
			localStorage.removeItem('admin_token');
			localStorage.removeItem('admin_user');
			goto('/akses-manajerial');
		} finally {
			loading = false;
		}
	}

	// Logout function
	async function handleLogout() {
		localStorage.removeItem('admin_token');
		localStorage.removeItem('admin_user');
		goto('/akses-manajerial');
	}

	// Lifecycle
	onMount(() => {
		checkMobileView();
		checkAuthentication();

		const handleResize = () => checkMobileView();
		const handleKeydown = (e) => {
			if (e.key === 'Escape' && sidebarOpen) {
				closeSidebar();
			}
		};

		window.addEventListener('resize', handleResize);
		document.addEventListener('keydown', handleKeydown);

		return () => {
			window.removeEventListener('resize', handleResize);
			document.removeEventListener('keydown', handleKeydown);
		};
	});
</script>

<svelte:head>
	<title>Admin Management - Astro Works</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
</svelte:head>

{#if loading}
	<!-- Loading state -->
	<div class="min-h-screen bg-gray-50 flex items-center justify-center">
		<div class="text-center">
			<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
			<p class="text-gray-600">Checking authentication...</p>
		</div>
	</div>
{:else}
<div class="min-h-screen bg-gray-50 flex">
	<!-- Mobile Overlay -->
	{#if sidebarOpen && isMobile}
		<div 
			class="fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300"
			on:click={closeSidebar}
			on:keydown={handleKeydown}
			role="button"
			tabindex="0"
			aria-label="Close sidebar"
		></div>
	{/if}

	<!-- Sidebar -->
	<aside 
		class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out
		       md:relative md:translate-x-0 md:shadow-none md:border-r md:border-gray-200
		       {sidebarOpen ? 'translate-x-0' : '-translate-x-full'}"
	>
		<!-- Sidebar Header -->
		<div class="flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-white">
			<div class="flex items-center">
				<span class="text-2xl mr-2">🚀</span>
				<h1 class="text-xl font-bold text-gray-800">Admin Panel</h1>
			</div>
			
			<!-- Close button (mobile only) -->
			<button 
				class="md:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
				on:click={closeSidebar}
				aria-label="Close sidebar"
			>
				<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
				</svg>
			</button>
		</div>

		<!-- Navigation -->
		<nav class="flex-1 px-4 py-6 overflow-y-auto">
			<div class="space-y-1">
				{#each navItems as item}
					<a 
						href={item.href}
						class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200
						       {isActiveRoute(item.href) 
						         ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-500 shadow-sm' 
						         : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
						       }"
						on:click={() => isMobile && closeSidebar()}
					>
						<svg class="w-5 h-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={item.icon} />
						</svg>
						<span class="truncate">{item.label}</span>
					</a>
				{/each}
			</div>

			<!-- Divider & Additional Links -->
			<div class="mt-8 pt-6 border-t border-gray-200">
				<a 
					href="/" 
					class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-all duration-200"
					on:click={() => isMobile && closeSidebar()}
				>
					<svg class="w-5 h-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
					</svg>
					<span class="truncate">Back to Store</span>
				</a>
			</div>
		</nav>
	</aside>

	<!-- Main Content Area -->
	<div class="flex-1 flex flex-col min-h-screen md:ml-0">
		<!-- Top Header -->
		<header class="bg-white shadow-sm border-b border-gray-200 z-30">
			<div class="flex items-center justify-between h-16 px-4 sm:px-6">
				<!-- Mobile menu button -->
				<button 
					class="md:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
					on:click={toggleSidebar}
					aria-label="Open sidebar"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
					</svg>
				</button>

				<!-- Page title (hidden on mobile when sidebar button is shown) -->
				<div class="hidden md:block">
					<h2 class="text-lg font-semibold text-gray-800">
						{#if $page.url.pathname === '/manajemen'}
							Dashboard
						{:else if $page.url.pathname.startsWith('/manajemen/produk')}
							Products Management
						{:else if $page.url.pathname.startsWith('/manajemen/kategori')}
							Categories Management
						{:else if $page.url.pathname.startsWith('/manajemen/pesanan')}
							Orders Management
						{:else}
							Admin Panel
						{/if}
					</h2>
				</div>

				<!-- User info -->
				<div class="flex items-center space-x-3">
					{#if adminUser}
						<div class="hidden sm:block text-right">
							<p class="text-sm font-medium text-gray-700">{adminUser.name}</p>
							<p class="text-xs text-gray-500">{adminUser.email}</p>
						</div>
						<div class="relative">
							<button
								class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow"
								on:click={() => showUserMenu = !showUserMenu}
								aria-label="User menu"
							>
								<span class="text-white text-sm font-bold">{adminUser.name.charAt(0).toUpperCase()}</span>
							</button>

							{#if showUserMenu}
								<div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
									<button
										class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
										on:click={handleLogout}
									>
										<svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
										</svg>
										Logout
									</button>
								</div>
							{/if}
						</div>
					{:else}
						<div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center animate-pulse">
							<span class="text-gray-500 text-sm">...</span>
						</div>
					{/if}
				</div>
			</div>
		</header>

		<!-- Main Content -->
		<main class="flex-1 p-4 sm:p-6 lg:p-8 overflow-auto">
			<div class="max-w-7xl mx-auto">
				<slot />
			</div>
		</main>
	</div>
</div>

<style>
	/* Custom scrollbar for sidebar */
	nav::-webkit-scrollbar {
		width: 4px;
	}
	
	nav::-webkit-scrollbar-track {
		background: transparent;
	}
	
	nav::-webkit-scrollbar-thumb {
		background: #cbd5e0;
		border-radius: 2px;
	}
	
	nav::-webkit-scrollbar-thumb:hover {
		background: #a0aec0;
	}

	/* Smooth transitions */
	* {
		transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
		transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
		transition-duration: 150ms;
	}
</style>
{/if}