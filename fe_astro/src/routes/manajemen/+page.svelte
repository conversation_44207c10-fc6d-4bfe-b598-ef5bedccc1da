<script>
	import { onMount } from 'svelte';
	import { config } from '$lib/config/env';

	let stats = {
		products: 0,
		categories: 0,
		orders: 0,
		revenue: 0
	};

	let loading = true;
	let error = null;

	onMount(async () => {
		try {
			// Fetch dashboard statistics
			const [productsRes, categoriesRes] = await Promise.all([
				fetch(`${config.api.url}/products`),
				fetch(`${config.api.url}/categories`)
			]);

			if (productsRes.ok) {
				const products = await productsRes.json();
				stats.products = products.length;
			}

			if (categoriesRes.ok) {
				const categories = await categoriesRes.json();
				stats.categories = categories.length;
			}

			// Mock data for orders and revenue (implement when available)
			stats.orders = 0;
			stats.revenue = 0;

		} catch (err) {
			error = err.message;
		} finally {
			loading = false;
		}
	});
</script>

<svelte:head>
	<title>Admin Dashboard - Astro Works</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
			<p class="text-gray-600 mt-1">Welcome to your admin panel</p>
		</div>
		<div class="text-sm text-gray-500">
			Last updated: {new Date().toLocaleString()}
		</div>
	</div>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
			<span class="ml-3 text-gray-600">Loading dashboard...</span>
		</div>
	{:else if error}
		<div class="bg-red-50 border border-red-200 rounded-lg p-4">
			<div class="flex">
				<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
				</svg>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-red-800">Error loading dashboard</h3>
					<p class="text-sm text-red-700 mt-1">{error}</p>
				</div>
			</div>
		</div>
	{:else}
		<!-- Statistics Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
							<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">Total Products</p>
						<p class="text-2xl font-bold text-gray-900">{stats.products}</p>
						{#if stats.products === 0}
							<p class="text-xs text-red-500 mt-1">No products yet</p>
						{/if}
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
							<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">Categories</p>
						<p class="text-2xl font-bold text-gray-900">{stats.categories}</p>
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
							<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">Total Orders</p>
						<p class="text-2xl font-bold text-gray-900">{stats.orders}</p>
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
							<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">Revenue</p>
						<p class="text-2xl font-bold text-gray-900">Rp {stats.revenue.toLocaleString()}</p>
					</div>
				</div>
			</div>
		</div>

		{#if stats.products === 0}
			<!-- Empty State for No Products -->
			<div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg border border-blue-200 p-8 text-center">
				<div class="mx-auto w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mb-6">
					<svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
					</svg>
				</div>
				<h3 class="text-xl font-semibold text-gray-900 mb-2">Ready to Start Selling?</h3>
				<p class="text-gray-600 mb-6 max-w-md mx-auto">
					Your store is set up and ready to go! Add your first product to start building your catalog and begin selling to customers.
				</p>

				<div class="flex flex-col sm:flex-row gap-4 justify-center mb-6">
					<a
						href="/manajemen/produk/add"
						class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
					>
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
						</svg>
						Add Your First Product
					</a>
					<a
						href="/manajemen/kategori"
						class="inline-flex items-center px-6 py-3 border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors duration-200 font-medium"
					>
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
						</svg>
						Setup Categories First
					</a>
				</div>

				<div class="bg-white rounded-lg p-4 max-w-lg mx-auto">
					<h4 class="font-medium text-gray-900 mb-2 flex items-center justify-center">
						<svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
						Quick Tip
					</h4>
					<p class="text-sm text-gray-600">
						Start by setting up your product categories, then add products with detailed descriptions and high-quality images for the best customer experience.
					</p>
				</div>
			</div>
		{:else}
			<!-- Quick Actions -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
				<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
					<a
						href="/manajemen/produk/add"
						class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
					>
						<div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
							<svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
							</svg>
						</div>
						<div>
							<h3 class="font-medium text-gray-900">Add New Product</h3>
							<p class="text-sm text-gray-600">Create a new product listing</p>
						</div>
					</a>

					<a
						href="/manajemen/produk"
						class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
					>
						<div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
							<svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
							</svg>
						</div>
						<div>
							<h3 class="font-medium text-gray-900">Manage Products</h3>
							<p class="text-sm text-gray-600">View and edit existing products</p>
						</div>
					</a>

					<a
						href="{config.api.url}/products"
						target="_blank"
						class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
					>
						<div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
							<svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
						</svg>
					</div>
					<div>
						<h3 class="font-medium text-gray-900">View API</h3>
						<p class="text-sm text-gray-600">Check API endpoints</p>
					</div>
				</a>
			</div>
		</div>
		{/if}
	{/if}
</div>
