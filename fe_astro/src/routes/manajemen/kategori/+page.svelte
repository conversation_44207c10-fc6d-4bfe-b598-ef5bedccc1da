<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { categoryApi, formatDate } from '$lib/api.js';
	import EmptyState from '$lib/components/EmptyState.svelte';
	import ErrorBoundary from '$lib/components/ErrorBoundary.svelte';

	let categories = [];
	let loading = true;
	let error = null;
	let searchQuery = '';
	let filteredCategories = [];
	let successMessage = '';

	// Check for success message in URL params
	$: if ($page.url.searchParams.get('success')) {
		const success = $page.url.searchParams.get('success');
		if (success === 'created') {
			successMessage = 'Category created successfully!';
		} else if (success === 'updated') {
			successMessage = 'Category updated successfully!';
		} else if (success === 'deleted') {
			successMessage = 'Category deleted successfully!';
		}

		// Clear the success message after 5 seconds
		setTimeout(() => {
			successMessage = '';
		}, 5000);
	}

	// Pagination
	let currentPage = 1;
	const itemsPerPage = 10;

	// Load categories on component mount
	onMount(async () => {
		await loadCategories();
	});

	async function loadCategories() {
		try {
			loading = true;
			categories = await categoryApi.getAll();
			filterCategories();
		} catch (err) {
			error = err.message;
		} finally {
			loading = false;
		}
	}

	function filterCategories() {
		if (searchQuery.trim() === '') {
			filteredCategories = categories;
		} else {
			const query = searchQuery.toLowerCase();
			filteredCategories = categories.filter(category => 
				category.name.toLowerCase().includes(query) ||
				(category.description && category.description.toLowerCase().includes(query))
			);
		}
		currentPage = 1; // Reset to first page when filtering
	}

	// Watch for search query changes
	$: if (searchQuery !== undefined) {
		filterCategories();
	}

	// Calculate pagination
	$: totalPages = Math.ceil(filteredCategories.length / itemsPerPage);
	$: startIndex = (currentPage - 1) * itemsPerPage;
	$: endIndex = Math.min(startIndex + itemsPerPage, filteredCategories.length);

	// Get categories for current page
	$: paginatedCategories = filteredCategories.slice(
		(currentPage - 1) * itemsPerPage,
		currentPage * itemsPerPage
	);

	async function deleteCategory(id, name) {
		if (!confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
			return;
		}

		try {
			await categoryApi.delete(id);
			await loadCategories(); // Reload the list
		} catch (err) {
			alert(`Failed to delete category: ${err.message}`);
		}
	}

	function goToPage(page) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
		}
	}
</script>

<svelte:head>
	<title>Category Management - Astro Works</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Category Management</h1>
			<p class="text-gray-600 mt-1">Manage your product categories</p>
		</div>
		<a 
			href="/manajemen/kategori/add" 
			class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center"
		>
			<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
			</svg>
			Add Category
		</a>
	</div>

	<!-- Success Message -->
	{#if successMessage}
		<div class="bg-green-50 border border-green-200 rounded-lg p-4">
			<div class="flex">
				<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
				</svg>
				<div class="ml-3">
					<p class="text-sm font-medium text-green-800">{successMessage}</p>
				</div>
				<div class="ml-auto pl-3">
					<button
						on:click={() => successMessage = ''}
						class="text-green-400 hover:text-green-600"
						aria-label="Close success message"
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
						</svg>
					</button>
				</div>
			</div>
		</div>
	{/if}

	<!-- Search and Filters -->
	<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
		<div class="flex items-center space-x-4">
			<div class="flex-1">
				<label for="search" class="sr-only">Search categories</label>
				<div class="relative">
					<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
						<svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
					<input 
						id="search"
						type="text" 
						bind:value={searchQuery}
						placeholder="Search categories..." 
						class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
					>
				</div>
			</div>
			<button 
				on:click={loadCategories}
				class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center"
			>
				<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
				</svg>
				Refresh
			</button>
		</div>
	</div>

	<!-- Categories Table -->
	<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
		<ErrorBoundary {error} {loading} onRetry={loadCategories}>
			{#snippet children()}
				{#if filteredCategories.length === 0}
					{#if searchQuery}
						<EmptyState
							title="No categories found"
							description="No categories match '{searchQuery}'. Try adjusting your search terms."
							icon="search"
							showRefresh={true}
							onRefresh={loadCategories}
						/>
					{:else}
						<EmptyState
							title="Welcome to Category Management!"
							description="You haven't created any categories yet. Start organizing your products by creating your first category."
							icon="categories"
							actionText="Add Your First Category"
							actionHref="/manajemen/kategori/add"
							showRefresh={true}
							onRefresh={loadCategories}
						/>
					{/if}
				{:else}
			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Category
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Description
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Status
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Created
							</th>
							<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
								Actions
							</th>
						</tr>
					</thead>
					<tbody class="bg-white divide-y divide-gray-200">
						{#each paginatedCategories as category}
							<tr class="hover:bg-gray-50">
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="flex items-center">
										<div class="flex-shrink-0 h-10 w-10">
											<div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
												<svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
												</svg>
											</div>
										</div>
										<div class="ml-4">
											<div class="text-sm font-medium text-gray-900">{category.name}</div>
											<div class="text-sm text-gray-500">ID: {category.id}</div>
										</div>
									</div>
								</td>
								<td class="px-6 py-4">
									<div class="text-sm text-gray-900">
										{category.description || 'No description'}
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {category.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
										{category.is_active ? 'Active' : 'Inactive'}
									</span>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
									{category.created_at ? formatDate(category.created_at) : 'Unknown'}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
									<div class="flex items-center justify-end space-x-2">
										<a 
											href="/manajemen/kategori/edit/{category.id}" 
											class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
											aria-label="Edit {category.name}"
											title="Edit category"
										>
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
											</svg>
										</a>
										<button 
											on:click={() => deleteCategory(category.id, category.name)}
											class="text-red-600 hover:text-red-900 transition-colors duration-200"
											aria-label="Delete {category.name}"
											title="Delete category"
										>
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
											</svg>
										</button>
									</div>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>

			<!-- Pagination -->
			{#if totalPages > 1}
				<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
					<div class="flex-1 flex justify-between sm:hidden">
						<button 
							on:click={() => goToPage(currentPage - 1)}
							disabled={currentPage === 1}
							class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							Previous
						</button>
						<button 
							on:click={() => goToPage(currentPage + 1)}
							disabled={currentPage === totalPages}
							class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							Next
						</button>
					</div>
					<div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
						<div>
							<p class="text-sm text-gray-700">
								Showing <span class="font-medium">{startIndex + 1}</span> to <span class="font-medium">{endIndex}</span> of <span class="font-medium">{filteredCategories.length}</span> results
							</p>
						</div>
						<div>
							<nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
								<button 
									on:click={() => goToPage(currentPage - 1)}
									disabled={currentPage === 1}
									class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<span class="sr-only">Previous</span>
									<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
									</svg>
								</button>
								
								{#each Array(totalPages) as _, i}
									<button 
										on:click={() => goToPage(i + 1)}
										class="relative inline-flex items-center px-4 py-2 border text-sm font-medium {currentPage === i + 1 ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}"
									>
										{i + 1}
									</button>
								{/each}
								
								<button 
									on:click={() => goToPage(currentPage + 1)}
									disabled={currentPage === totalPages}
									class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<span class="sr-only">Next</span>
									<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
									</svg>
								</button>
							</nav>
						</div>
					</div>
				</div>
			{/if}
				{/if}
			{/snippet}
		</ErrorBoundary>
	</div>
</div>
