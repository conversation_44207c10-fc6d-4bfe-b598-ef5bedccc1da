<script>
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { categoryApi } from '$lib/api.js';

	let categoryId = $page.params.id;

	let formData = {
		name: '',
		description: '',
		is_active: true
	};

	let loading = true;
	let saving = false;
	let error = null;
	let validationErrors = {};
	let categoryNotFound = false;

	// Load category on component mount
	onMount(async () => {
		await loadCategory();
	});

	async function loadCategory() {
		try {
			console.log('Loading category with ID:', categoryId);
			const category = await categoryApi.getById(categoryId);
			console.log('Category loaded:', category);
			formData = {
				name: category.name || '',
				description: category.description || '',
				is_active: category.is_active !== false // Default to true if undefined
			};
		} catch (err) {
			if (err.message.includes('404')) {
				categoryNotFound = true;
			} else {
				error = err.message;
			}
		} finally {
			loading = false;
		}
	}

	function validateForm() {
		validationErrors = {};

		if (!formData.name.trim()) {
			validationErrors.name = 'Category name is required';
		}

		return Object.keys(validationErrors).length === 0;
	}

	async function handleSubmit() {
		if (!validateForm()) {
			return;
		}

		try {
			saving = true;
			error = null;

			const categoryData = {
				name: formData.name.trim(),
				description: formData.description.trim() || null,
				is_active: formData.is_active
			};

			await categoryApi.update(categoryId, categoryData);
			
			// Redirect to category list with success message
			goto('/manajemen/kategori?success=updated');
		} catch (err) {
			error = err.message;
		} finally {
			saving = false;
		}
	}

	async function handleDelete() {
		if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
			return;
		}

		try {
			saving = true;
			await categoryApi.delete(categoryId);
			goto('/manajemen/kategori?success=deleted');
		} catch (err) {
			error = err.message;
			saving = false;
		}
	}

	function handleCancel() {
		goto('/manajemen/kategori');
	}
</script>

<svelte:head>
	<title>Edit Category - Astro Works</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Edit Category</h1>
			<p class="text-gray-600 mt-1">Update category information</p>
		</div>
		<button 
			on:click={handleCancel}
			class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center"
		>
			<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
			</svg>
			Back to Categories
		</button>
	</div>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
			<span class="ml-3 text-gray-600">Loading category...</span>
		</div>
	{:else if categoryNotFound}
		<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
			<div class="flex">
				<svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
				</svg>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-yellow-800">Category not found</h3>
					<p class="text-sm text-yellow-700 mt-1">The category you're looking for doesn't exist or has been deleted.</p>
					<div class="mt-4">
						<button 
							on:click={handleCancel}
							class="bg-yellow-100 text-yellow-800 px-4 py-2 rounded-lg hover:bg-yellow-200 transition-colors duration-200"
						>
							Back to Categories
						</button>
					</div>
				</div>
			</div>
		</div>
	{:else}
		<!-- Error Message -->
		{#if error}
			<div class="bg-red-50 border border-red-200 rounded-lg p-4">
				<div class="flex">
					<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
					</svg>
					<div class="ml-3">
						<h3 class="text-sm font-medium text-red-800">Error updating category</h3>
						<p class="text-sm text-red-700 mt-1">{error}</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Form -->
		<form on:submit|preventDefault={handleSubmit} class="space-y-6">
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<h2 class="text-lg font-semibold text-gray-900 mb-6">Category Information</h2>
				
				<div class="grid grid-cols-1 gap-6">
					<!-- Category Name -->
					<div>
						<label for="name" class="block text-sm font-medium text-gray-700 mb-2">
							Category Name *
						</label>
						<input 
							id="name"
							type="text" 
							bind:value={formData.name}
							placeholder="Enter category name"
							class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 {validationErrors.name ? 'border-red-500' : ''}"
							required
						>
						{#if validationErrors.name}
							<p class="mt-1 text-sm text-red-600">{validationErrors.name}</p>
						{/if}
					</div>

					<!-- Description -->
					<div>
						<label for="description" class="block text-sm font-medium text-gray-700 mb-2">
							Description
						</label>
						<textarea 
							id="description"
							bind:value={formData.description}
							placeholder="Enter category description (optional)"
							rows="4"
							class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
						></textarea>
						<p class="mt-1 text-sm text-gray-500">Optional description for this category</p>
					</div>

					<!-- Status -->
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
						<div class="flex items-center space-x-6">
							<label class="flex items-center">
								<input 
									type="radio" 
									bind:group={formData.is_active} 
									value={true}
									class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
								>
								<span class="ml-2 text-sm text-gray-700">Active</span>
							</label>
							<label class="flex items-center">
								<input 
									type="radio" 
									bind:group={formData.is_active} 
									value={false}
									class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
								>
								<span class="ml-2 text-sm text-gray-700">Inactive</span>
							</label>
						</div>
						<p class="mt-1 text-sm text-gray-500">Active categories will be visible to customers</p>
					</div>
				</div>
			</div>

			<!-- Form Actions -->
			<div class="flex items-center justify-between">
				<button 
					type="button"
					on:click={handleDelete}
					class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
					disabled={saving}
				>
					<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
					</svg>
					Delete Category
				</button>

				<div class="flex items-center space-x-4">
					<button 
						type="button"
						on:click={handleCancel}
						class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200"
						disabled={saving}
					>
						Cancel
					</button>
					<button 
						type="submit"
						class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
						disabled={saving}
					>
						{#if saving}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
							Saving...
						{:else}
							<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
							</svg>
							Update Category
						{/if}
					</button>
				</div>
			</div>
		</form>
	{/if}
</div>
