<script>
	import { onMount } from 'svelte';
	import { orderApi, formatDate, formatCurrency } from '$lib/api.js';

	let orders = [];
	let loading = true;
	let error = null;
	let searchQuery = '';
	let filteredOrders = [];
	let statusFilter = 'all';

	// Pagination
	let currentPage = 1;
	const itemsPerPage = 10;

	// Order statuses
	const orderStatuses = [
		{ value: 'all', label: 'All Orders' },
		{ value: 'pending', label: 'Pending' },
		{ value: 'confirmed', label: 'Confirmed' },
		{ value: 'processing', label: 'Processing' },
		{ value: 'shipped', label: 'Shipped' },
		{ value: 'delivered', label: 'Delivered' },
		{ value: 'cancelled', label: 'Cancelled' }
	];

	// Load orders on component mount
	onMount(async () => {
		await loadOrders();
	});

	async function loadOrders() {
		try {
			loading = true;
			// For now, use mock data since order API might not be fully implemented
			orders = [
				{
					id: 'ord_001',
					customer_name: '<PERSON>',
					customer_phone: '+62812345678',
					total_amount: 2500000,
					status: 'pending',
					created_at: new Date().toISOString(),
					items: [
						{ name: 'Product 1', quantity: 2, price: 1000000 },
						{ name: 'Product 2', quantity: 1, price: 500000 }
					]
				},
				{
					id: 'ord_002',
					customer_name: 'Jane Smith',
					customer_phone: '+62887654321',
					total_amount: 1500000,
					status: 'confirmed',
					created_at: new Date(Date.now() - 86400000).toISOString(),
					items: [
						{ name: 'Product 3', quantity: 1, price: 1500000 }
					]
				}
			];
			filterOrders();
		} catch (err) {
			error = err.message;
		} finally {
			loading = false;
		}
	}

	function filterOrders() {
		let filtered = orders;

		// Filter by status
		if (statusFilter !== 'all') {
			filtered = filtered.filter(order => order.status === statusFilter);
		}

		// Filter by search query
		if (searchQuery.trim() !== '') {
			const query = searchQuery.toLowerCase();
			filtered = filtered.filter(order => 
				order.id.toLowerCase().includes(query) ||
				order.customer_name.toLowerCase().includes(query) ||
				order.customer_phone.toLowerCase().includes(query)
			);
		}

		filteredOrders = filtered;
		currentPage = 1; // Reset to first page when filtering
	}

	// Watch for filter changes
	$: if (searchQuery !== undefined || statusFilter !== undefined) {
		filterOrders();
	}

	// Calculate pagination
	$: totalPages = Math.ceil(filteredOrders.length / itemsPerPage);
	$: startIndex = (currentPage - 1) * itemsPerPage;
	$: endIndex = Math.min(startIndex + itemsPerPage, filteredOrders.length);

	// Get orders for current page
	$: paginatedOrders = filteredOrders.slice(
		(currentPage - 1) * itemsPerPage,
		currentPage * itemsPerPage
	);

	function getStatusColor(status) {
		const colors = {
			pending: 'bg-yellow-100 text-yellow-800',
			confirmed: 'bg-blue-100 text-blue-800',
			processing: 'bg-purple-100 text-purple-800',
			shipped: 'bg-indigo-100 text-indigo-800',
			delivered: 'bg-green-100 text-green-800',
			cancelled: 'bg-red-100 text-red-800'
		};
		return colors[status] || 'bg-gray-100 text-gray-800';
	}

	function goToPage(page) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
		}
	}
</script>

<svelte:head>
	<title>Order Management - Astro Works</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Order Management</h1>
			<p class="text-gray-600 mt-1">Manage customer orders and track status</p>
		</div>
		<div class="flex items-center space-x-4">
			<button 
				on:click={loadOrders}
				class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center"
			>
				<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
				</svg>
				Refresh
			</button>
		</div>
	</div>

	<!-- Search and Filters -->
	<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			<div>
				<label for="search" class="sr-only">Search orders</label>
				<div class="relative">
					<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
						<svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
					<input 
						id="search"
						type="text" 
						bind:value={searchQuery}
						placeholder="Search by order ID, customer name, or phone..." 
						class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
					>
				</div>
			</div>
			<div>
				<label for="status-filter" class="sr-only">Filter by status</label>
				<select 
					id="status-filter"
					bind:value={statusFilter}
					class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
				>
					{#each orderStatuses as status}
						<option value={status.value}>{status.label}</option>
					{/each}
				</select>
			</div>
		</div>
	</div>

	<!-- Orders Table -->
	<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
		{#if loading}
			<div class="flex items-center justify-center py-12">
				<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
				<span class="ml-3 text-gray-600">Loading orders...</span>
			</div>
		{:else if error}
			<div class="bg-red-50 border border-red-200 rounded-lg p-4">
				<div class="flex">
					<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
					</svg>
					<div class="ml-3">
						<h3 class="text-sm font-medium text-red-800">Error loading orders</h3>
						<p class="text-sm text-red-700 mt-1">{error}</p>
					</div>
				</div>
			</div>
		{:else if filteredOrders.length === 0}
			<div class="text-center py-12">
				<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
				</svg>
				<h3 class="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
				<p class="mt-1 text-sm text-gray-500">
					{searchQuery || statusFilter !== 'all' ? 'Try adjusting your search or filter criteria.' : 'No orders have been placed yet.'}
				</p>
			</div>
		{:else}
			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Order
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Customer
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Total
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Status
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Date
							</th>
							<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
								Actions
							</th>
						</tr>
					</thead>
					<tbody class="bg-white divide-y divide-gray-200">
						{#each paginatedOrders as order}
							<tr class="hover:bg-gray-50">
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="flex items-center">
										<div class="flex-shrink-0 h-10 w-10">
											<div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
												<svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
												</svg>
											</div>
										</div>
										<div class="ml-4">
											<div class="text-sm font-medium text-gray-900">#{order.id.slice(0, 8)}</div>
											<div class="text-sm text-gray-500">{order.items?.length || 0} items</div>
										</div>
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="text-sm font-medium text-gray-900">{order.customer_name}</div>
									<div class="text-sm text-gray-500">{order.customer_phone}</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="text-sm font-medium text-gray-900">{formatCurrency(order.total_amount)}</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getStatusColor(order.status)}">
										{order.status}
									</span>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
									{order.created_at ? formatDate(order.created_at) : 'Unknown'}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
									<a 
										href="/manajemen/pesanan/detail/{order.id}" 
										class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
										aria-label="View order {order.id}"
										title="View order details"
									>
										<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
										</svg>
									</a>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>

			<!-- Pagination -->
			{#if totalPages > 1}
				<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
					<div class="flex-1 flex justify-between sm:hidden">
						<button 
							on:click={() => goToPage(currentPage - 1)}
							disabled={currentPage === 1}
							class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							Previous
						</button>
						<button 
							on:click={() => goToPage(currentPage + 1)}
							disabled={currentPage === totalPages}
							class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							Next
						</button>
					</div>
					<div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
						<div>
							<p class="text-sm text-gray-700">
								Showing <span class="font-medium">{startIndex + 1}</span> to <span class="font-medium">{endIndex}</span> of <span class="font-medium">{filteredOrders.length}</span> results
							</p>
						</div>
						<div>
							<nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
								<button 
									on:click={() => goToPage(currentPage - 1)}
									disabled={currentPage === 1}
									class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<span class="sr-only">Previous</span>
									<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
									</svg>
								</button>
								
								{#each Array(totalPages) as _, i}
									<button 
										on:click={() => goToPage(i + 1)}
										class="relative inline-flex items-center px-4 py-2 border text-sm font-medium {currentPage === i + 1 ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}"
									>
										{i + 1}
									</button>
								{/each}
								
								<button 
									on:click={() => goToPage(currentPage + 1)}
									disabled={currentPage === totalPages}
									class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<span class="sr-only">Next</span>
									<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
									</svg>
								</button>
							</nav>
						</div>
					</div>
				</div>
			{/if}
		{/if}
	</div>
</div>
