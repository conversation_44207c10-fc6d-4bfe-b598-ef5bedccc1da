<script>
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { orderApi, formatDate, formatCurrency } from '$lib/api.js';

	let orderId = $page.params.id;
	let order = null;
	let loading = true;
	let error = null;
	let orderNotFound = false;

	// Order statuses for status update
	const orderStatuses = [
		{ value: 'pending', label: 'Pending' },
		{ value: 'confirmed', label: 'Confirmed' },
		{ value: 'processing', label: 'Processing' },
		{ value: 'shipped', label: 'Shipped' },
		{ value: 'delivered', label: 'Delivered' },
		{ value: 'cancelled', label: 'Cancelled' }
	];

	// Load order on component mount
	onMount(async () => {
		await loadOrder();
	});

	async function loadOrder() {
		try {
			console.log('Loading order with ID:', orderId);
			
			// Mock order data for now
			const mockOrders = {
				'ord_001': {
					id: 'ord_001',
					customer_name: '<PERSON>',
					customer_phone: '+62812345678',
					customer_email: '<EMAIL>',
					customer_address: 'Jl. Sudirman No. 123, Jakarta Pusat',
					total_amount: 2500000,
					status: 'pending',
					created_at: new Date().toISOString(),
					notes: 'Please deliver in the morning',
					items: [
						{ 
							id: 'item_1',
							name: 'Modern TV Cabinet',
							quantity: 1,
							price: 2000000,
							total: 2000000,
							image: '/static/placeholder-image.svg'
						},
						{ 
							id: 'item_2',
							name: 'LED Strip Lighting',
							quantity: 2,
							price: 250000,
							total: 500000,
							image: '/static/placeholder-image.svg'
						}
					]
				},
				'ord_002': {
					id: 'ord_002',
					customer_name: 'Jane Smith',
					customer_phone: '+62887654321',
					customer_email: '<EMAIL>',
					customer_address: 'Jl. Thamrin No. 456, Jakarta Selatan',
					total_amount: 1500000,
					status: 'confirmed',
					created_at: new Date(Date.now() - 86400000).toISOString(),
					notes: '',
					items: [
						{ 
							id: 'item_3',
							name: 'Kitchen Cabinet Set',
							quantity: 1,
							price: 1500000,
							total: 1500000,
							image: '/static/placeholder-image.svg'
						}
					]
				}
			};

			order = mockOrders[orderId];
			if (!order) {
				orderNotFound = true;
			}
			
			console.log('Order loaded:', order);
		} catch (err) {
			if (err.message.includes('404')) {
				orderNotFound = true;
			} else {
				error = err.message;
			}
		} finally {
			loading = false;
		}
	}

	async function updateOrderStatus(newStatus) {
		if (!confirm(`Are you sure you want to change the order status to "${newStatus}"?`)) {
			return;
		}

		try {
			// Mock status update for now
			order.status = newStatus;
			alert(`Order status updated to "${newStatus}"`);
		} catch (err) {
			error = err.message;
		}
	}

	function getStatusColor(status) {
		const colors = {
			pending: 'bg-yellow-100 text-yellow-800',
			confirmed: 'bg-blue-100 text-blue-800',
			processing: 'bg-purple-100 text-purple-800',
			shipped: 'bg-indigo-100 text-indigo-800',
			delivered: 'bg-green-100 text-green-800',
			cancelled: 'bg-red-100 text-red-800'
		};
		return colors[status] || 'bg-gray-100 text-gray-800';
	}

	function handleBack() {
		goto('/manajemen/pesanan');
	}
</script>

<svelte:head>
	<title>Order Details - Astro Works</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Order Details</h1>
			<p class="text-gray-600 mt-1">View and manage order information</p>
		</div>
		<button 
			on:click={handleBack}
			class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center"
		>
			<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
			</svg>
			Back to Orders
		</button>
	</div>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
			<span class="ml-3 text-gray-600">Loading order...</span>
		</div>
	{:else if orderNotFound}
		<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
			<div class="flex">
				<svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
				</svg>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-yellow-800">Order not found</h3>
					<p class="text-sm text-yellow-700 mt-1">The order you're looking for doesn't exist or has been deleted.</p>
					<div class="mt-4">
						<button 
							on:click={handleBack}
							class="bg-yellow-100 text-yellow-800 px-4 py-2 rounded-lg hover:bg-yellow-200 transition-colors duration-200"
						>
							Back to Orders
						</button>
					</div>
				</div>
			</div>
		</div>
	{:else if error}
		<div class="bg-red-50 border border-red-200 rounded-lg p-4">
			<div class="flex">
				<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
				</svg>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-red-800">Error loading order</h3>
					<p class="text-sm text-red-700 mt-1">{error}</p>
				</div>
			</div>
		</div>
	{:else if order}
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Order Information -->
			<div class="lg:col-span-2 space-y-6">
				<!-- Order Header -->
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<div class="flex items-center justify-between mb-4">
						<h2 class="text-lg font-semibold text-gray-900">Order #{order.id}</h2>
						<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {getStatusColor(order.status)}">
							{order.status}
						</span>
					</div>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
						<div>
							<span class="text-gray-500">Order Date:</span>
							<span class="ml-2 text-gray-900">{formatDate(order.created_at)}</span>
						</div>
						<div>
							<span class="text-gray-500">Total Amount:</span>
							<span class="ml-2 text-gray-900 font-semibold">{formatCurrency(order.total_amount)}</span>
						</div>
					</div>
				</div>

				<!-- Order Items -->
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<h3 class="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
					<div class="space-y-4">
						{#each order.items as item}
							<div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
								<div class="flex-shrink-0">
									<img 
										src={item.image} 
										alt={item.name}
										class="w-16 h-16 object-cover rounded-lg"
									>
								</div>
								<div class="flex-1">
									<h4 class="text-sm font-medium text-gray-900">{item.name}</h4>
									<p class="text-sm text-gray-500">Quantity: {item.quantity}</p>
									<p class="text-sm text-gray-500">Price: {formatCurrency(item.price)}</p>
								</div>
								<div class="text-right">
									<p class="text-sm font-medium text-gray-900">{formatCurrency(item.total)}</p>
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>

			<!-- Sidebar -->
			<div class="space-y-6">
				<!-- Customer Information -->
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
					<div class="space-y-3 text-sm">
						<div>
							<span class="text-gray-500">Name:</span>
							<p class="text-gray-900 font-medium">{order.customer_name}</p>
						</div>
						<div>
							<span class="text-gray-500">Phone:</span>
							<p class="text-gray-900">{order.customer_phone}</p>
						</div>
						<div>
							<span class="text-gray-500">Email:</span>
							<p class="text-gray-900">{order.customer_email}</p>
						</div>
						<div>
							<span class="text-gray-500">Address:</span>
							<p class="text-gray-900">{order.customer_address}</p>
						</div>
						{#if order.notes}
							<div>
								<span class="text-gray-500">Notes:</span>
								<p class="text-gray-900">{order.notes}</p>
							</div>
						{/if}
					</div>
				</div>

				<!-- Status Management -->
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<h3 class="text-lg font-semibold text-gray-900 mb-4">Update Status</h3>
					<div class="space-y-2">
						{#each orderStatuses as status}
							<button 
								on:click={() => updateOrderStatus(status.value)}
								class="w-full text-left px-3 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200 {order.status === status.value ? 'bg-blue-50 border-blue-200' : ''}"
								disabled={order.status === status.value}
							>
								<span class="text-sm font-medium {order.status === status.value ? 'text-blue-700' : 'text-gray-700'}">
									{status.label}
								</span>
								{#if order.status === status.value}
									<span class="text-xs text-blue-600 ml-2">(Current)</span>
								{/if}
							</button>
						{/each}
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>
