<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { productApi, formatCurrency, formatDate, generateProductSlug } from '$lib/api.js';
	import DynamicImage from '$lib/components/DynamicImage.svelte';
	import ErrorBoundary from '$lib/components/ErrorBoundary.svelte';

	let products = [];
	let loading = true;
	let error = null;
	let searchQuery = '';
	let filteredProducts = [];
	let successMessage = '';

	// Check for success message in URL params
	$: if ($page.url.searchParams.get('success')) {
		const success = $page.url.searchParams.get('success');
		if (success === 'created') {
			successMessage = 'Product created successfully!';
		} else if (success === 'updated') {
			successMessage = 'Product updated successfully!';
		} else if (success === 'deleted') {
			successMessage = 'Product deleted successfully!';
		}

		// Clear the success message after 5 seconds
		setTimeout(() => {
			successMessage = '';
		}, 5000);
	}

	// Pagination
	let currentPage = 1;
	let itemsPerPage = 10;
	let totalPages = 1;

	// Load products on component mount
	onMount(async () => {
		await loadProducts();
	});

	async function loadProducts() {
		try {
			loading = true;
			error = null;
			products = await productApi.getAll();
			filterProducts();
		} catch (err) {
			error = err.message;
		} finally {
			loading = false;
		}
	}

	function filterProducts() {
		if (searchQuery.trim() === '') {
			filteredProducts = products;
		} else {
			const query = searchQuery.toLowerCase();
			filteredProducts = products.filter(product => 
				product.name.toLowerCase().includes(query) ||
				(product.description && product.description.toLowerCase().includes(query))
			);
		}
		
		// Update pagination
		totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
		currentPage = Math.min(currentPage, totalPages || 1);
	}

	// Reactive statement to filter products when search query changes
	$: if (searchQuery !== undefined) {
		filterProducts();
	}

	// Get products for current page
	$: paginatedProducts = filteredProducts.slice(
		(currentPage - 1) * itemsPerPage,
		currentPage * itemsPerPage
	);

	async function deleteProduct(id, name) {
		if (!confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
			return;
		}

		try {
			await productApi.delete(id);
			await loadProducts(); // Reload the list
		} catch (err) {
			alert(`Failed to delete product: ${err.message}`);
		}
	}

	function goToPage(page) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
		}
	}
</script>

<svelte:head>
	<title>Product Management - Astro Works</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Product Management</h1>
			<p class="text-gray-600 mt-1">Manage your product catalog</p>
		</div>
		<a 
			href="/manajemen/produk/add" 
			class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
		>
			<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
			</svg>
			Add New Product
		</a>
	</div>

	<!-- Success Message -->
	{#if successMessage}
		<div class="bg-green-50 border border-green-200 rounded-lg p-4">
			<div class="flex">
				<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
				</svg>
				<div class="ml-3">
					<p class="text-sm font-medium text-green-800">{successMessage}</p>
				</div>
				<div class="ml-auto pl-3">
					<button
						on:click={() => successMessage = ''}
						class="text-green-400 hover:text-green-600"
						aria-label="Close success message"
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
						</svg>
					</button>
				</div>
			</div>
		</div>
	{/if}

	<!-- Search and Filters -->
	<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
		<div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
			<div class="flex-1 max-w-md">
				<label for="search" class="sr-only">Search products</label>
				<div class="relative">
					<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
						<svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
					<input 
						id="search"
						type="text" 
						bind:value={searchQuery}
						placeholder="Search products by name or description..."
						class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
					>
				</div>
			</div>
			<div class="flex items-center space-x-4 text-sm text-gray-600">
				<span>Total: {filteredProducts.length} products</span>
				<button 
					on:click={loadProducts}
					class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
				>
					<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
					</svg>
					Refresh
				</button>
			</div>
		</div>
	</div>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
			<span class="ml-3 text-gray-600">Loading products...</span>
		</div>
	{:else if error}
		<div class="bg-red-50 border border-red-200 rounded-lg p-4">
			<div class="flex">
				<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
				</svg>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-red-800">Error loading products</h3>
					<p class="text-sm text-red-700 mt-1">{error}</p>
					<button 
						on:click={loadProducts}
						class="mt-2 text-sm text-red-800 underline hover:text-red-900"
					>
						Try again
					</button>
				</div>
			</div>
		</div>
	{:else if filteredProducts.length === 0}
		{#if searchQuery}
			<!-- Search Results Empty State -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
				<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
				</svg>
				<h3 class="mt-4 text-lg font-medium text-gray-900">No products found</h3>
				<p class="mt-2 text-gray-600">
					No products match "<span class="font-medium">{searchQuery}</span>". Try a different search term or check your spelling.
				</p>
				<div class="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
					<button
						on:click={() => { searchQuery = ''; }}
						class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
					>
						<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
						</svg>
						Clear Search
					</button>
					<a
						href="/manajemen/produk/add"
						class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
					>
						<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
						</svg>
						Add New Product
					</a>
				</div>
			</div>
		{:else}
			<!-- Empty Products State -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
				<div class="mx-auto w-24 h-24 bg-blue-50 rounded-full flex items-center justify-center mb-6">
					<svg class="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
					</svg>
				</div>
				<h3 class="text-xl font-semibold text-gray-900 mb-2">Welcome to Product Management!</h3>
				<p class="text-gray-600 mb-6 max-w-md mx-auto">
					You haven't created any products yet. Start building your catalog by adding your first product.
				</p>

				<!-- Quick Start Guide -->
				<div class="bg-blue-50 rounded-lg p-6 mb-8 text-left max-w-2xl mx-auto">
					<h4 class="font-medium text-blue-900 mb-3 flex items-center">
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
						Quick Start Guide
					</h4>
					<div class="space-y-2 text-sm text-blue-800">
						<div class="flex items-start">
							<span class="flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">1</span>
							<span>Click "Add Your First Product" to create a new product</span>
						</div>
						<div class="flex items-start">
							<span class="flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">2</span>
							<span>Fill in product details like name, description, and price</span>
						</div>
						<div class="flex items-start">
							<span class="flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">3</span>
							<span>Upload product images and set categories</span>
						</div>
						<div class="flex items-start">
							<span class="flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">4</span>
							<span>Publish your product to make it visible to customers</span>
						</div>
					</div>
				</div>

				<!-- Action Buttons -->
				<div class="flex flex-col sm:flex-row gap-4 justify-center">
					<a
						href="/manajemen/produk/add"
						class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
					>
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
						</svg>
						Add Your First Product
					</a>
					<a
						href="/manajemen/kategori"
						class="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 font-medium"
					>
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
						</svg>
						Manage Categories First
					</a>
				</div>

				<!-- Help Text -->
				<p class="text-sm text-gray-500 mt-6">
					Need help getting started? Contact our support team at
					<a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 underline"><EMAIL></a>.
				</p>
			</div>
		{/if}
	{:else}
		<!-- Products Table -->
		<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
							<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
						</tr>
					</thead>
					<tbody class="bg-white divide-y divide-gray-200">
						{#each paginatedProducts as product (product.id)}
							<tr class="hover:bg-gray-50 transition-colors duration-150">
								<td class="px-6 py-4">
									<div class="flex items-center">
										<div class="flex-shrink-0 h-12 w-12">
											<DynamicImage
												slug={generateProductSlug(product.name)}
												index={1}
												alt={product.name}
												className="h-12 w-12 rounded-lg object-cover"
												debug={false}
											/>
										</div>
										<div class="ml-4">
											<div class="text-sm font-medium text-gray-900">{product.name}</div>
											{#if product.description}
												<div class="text-sm text-gray-500">{product.description.substring(0, 60)}{product.description.length > 60 ? '...' : ''}</div>
											{/if}
										</div>
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="text-sm font-medium text-gray-900">{formatCurrency(product.base_price)}</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="flex flex-col gap-1">
										{#if product.is_active}
											<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
										{:else}
											<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
										{/if}
										{#if product.is_featured}
											<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Featured</span>
										{/if}
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
									{formatDate(product.created_at)}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
									<div class="flex items-center justify-end space-x-2">
										<a
											href="/manajemen/produk/edit/{product.id}"
											class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
											aria-label="Edit {product.name}"
											title="Edit product"
										>
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
											</svg>
										</a>
										<button
											on:click={() => deleteProduct(product.id, product.name)}
											class="text-red-600 hover:text-red-900 transition-colors duration-200"
											aria-label="Delete {product.name}"
											title="Delete product"
										>
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
											</svg>
										</button>
									</div>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>

			<!-- Pagination -->
			{#if totalPages > 1}
				<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
					<div class="flex-1 flex justify-between sm:hidden">
						<button 
							on:click={() => goToPage(currentPage - 1)}
							disabled={currentPage === 1}
							class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							Previous
						</button>
						<button 
							on:click={() => goToPage(currentPage + 1)}
							disabled={currentPage === totalPages}
							class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							Next
						</button>
					</div>
					<div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
						<div>
							<p class="text-sm text-gray-700">
								Showing <span class="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to 
								<span class="font-medium">{Math.min(currentPage * itemsPerPage, filteredProducts.length)}</span> of 
								<span class="font-medium">{filteredProducts.length}</span> results
							</p>
						</div>
						<div>
							<nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
								<button 
									on:click={() => goToPage(currentPage - 1)}
									disabled={currentPage === 1}
									class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<span class="sr-only">Previous</span>
									<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
									</svg>
								</button>
								
								{#each Array.from({length: totalPages}, (_, i) => i + 1) as page}
									{#if page === currentPage}
										<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
											{page}
										</span>
									{:else}
										<button 
											on:click={() => goToPage(page)}
											class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
										>
											{page}
										</button>
									{/if}
								{/each}
								
								<button 
									on:click={() => goToPage(currentPage + 1)}
									disabled={currentPage === totalPages}
									class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<span class="sr-only">Next</span>
									<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
									</svg>
								</button>
							</nav>
						</div>
					</div>
				</div>
			{/if}
		</div>
	{/if}
</div>
