<script>
	import { goto } from '$app/navigation';
	import { productApi, categoryApi, generateProductSlug } from '$lib/api.js';
	import MultiImageUpload from '$lib/components/MultiImageUpload.svelte';
	import ProductThemeManager from '$lib/components/ProductThemeManager.svelte';
	import ProductDimensionManager from '$lib/components/ProductDimensionManager.svelte';
	import { onMount } from 'svelte';

	// Form state
	let formData = {
		name: '',
		description: '',
		base_price: '',
		category_id: '',
		is_featured: false,
		is_active: true
	};

	// Component state
	let categories = [];
	let loading = false;
	let error = null;
	let validationErrors = {};
	let productSlug = '';
	let showImageUpload = false;
	let uploadedImages = {
		mobile: [],
		desktop: []
	};
	let productThemes = [];
	let productDimensions = [];
	let createdProductId = null;

	// Generate product slug from name
	$: productSlug = generateProductSlug(formData.name);
	$: showImageUpload = productSlug.length > 0;

	// Load categories on component mount
	onMount(async () => {
		try {
			loading = true;
			categories = await categoryApi.getAll();
		} catch (err) {
			console.error('Failed to load categories:', err);
			error = 'Failed to load categories. Please refresh the page.';
		} finally {
			loading = false;
		}

		// Setup event listeners for image uploads
		const handleUploadComplete = (event) => {
			console.log('Upload completed:', event.detail);
			const { aspect, images } = event.detail;
			
			if (aspect === '4x5') {
				uploadedImages.mobile = images;
			} else if (aspect === '16x9') {
				uploadedImages.desktop = images;
			}
		};

		const handleImageDeleted = (event) => {
			console.log('Image deleted:', event.detail);
			const { aspect, images } = event.detail;
			
			if (aspect === '4x5') {
				uploadedImages.mobile = images;
			} else if (aspect === '16x9') {
				uploadedImages.desktop = images;
			}
		};

		const handleUploadError = (event) => {
			console.error('Upload error:', event.detail);
			error = `Image upload failed: ${event.detail.message}`;
		};

		document.addEventListener('upload-complete', handleUploadComplete);
		document.addEventListener('image-deleted', handleImageDeleted);
		document.addEventListener('upload-error', handleUploadError);

		return () => {
			document.removeEventListener('upload-complete', handleUploadComplete);
			document.removeEventListener('image-deleted', handleImageDeleted);
			document.removeEventListener('upload-error', handleUploadError);
		};
	});

	function validateForm() {
		validationErrors = {};

		// Name validation
		if (!formData.name?.trim()) {
			validationErrors.name = 'Product name is required';
		} else if (formData.name.trim().length < 3) {
			validationErrors.name = 'Product name must be at least 3 characters long';
		}

		// Price validation
		if (!formData.base_price) {
			validationErrors.base_price = 'Price is required';
		} else {
			const price = parseFloat(formData.base_price);
			if (isNaN(price) || price <= 0) {
				validationErrors.base_price = 'Price must be a valid positive number';
			}
		}

		// Description validation
		if (formData.description?.trim() && formData.description.trim().length > 1000) {
			validationErrors.description = 'Description must be less than 1000 characters';
		}

		return Object.keys(validationErrors).length === 0;
	}

	async function handleSubmit() {
		// Clear previous errors
		error = null;
		
		if (!validateForm()) {
			error = 'Please fix the validation errors before submitting';
			return;
		}

		try {
			loading = true;

			// Prepare product data
			const productData = {
				name: formData.name.trim(),
				description: formData.description?.trim() || '',
				base_price: parseFloat(formData.base_price),
				category_id: formData.category_id || null,
				is_featured: Boolean(formData.is_featured),
				is_active: Boolean(formData.is_active)
				// Let backend generate unique slug automatically
			};

			console.log('Submitting product data:', productData);

			// Create the product
			const createdProduct = await productApi.create(productData);
			console.log('Product created successfully:', createdProduct);

			// Store product ID for themes and dimensions
			createdProductId = createdProduct.id;

			// Show success message and redirect
			const successMessage = encodeURIComponent(`Product "${formData.name}" created successfully!`);
			goto(`/manajemen/produk?success=${successMessage}`);
			
		} catch (err) {
			console.error('Error creating product:', err);
			
			// Handle different types of errors
			if (err.response) {
				// Server responded with error status
				if (err.response.status === 400) {
					error = 'Invalid product data. Please check your inputs.';
				} else if (err.response.status === 409) {
					error = 'A product with this name already exists.';
				} else if (err.response.status === 500) {
					error = 'Server error. Please try again later.';
				} else {
					error = `Server error: ${err.response.status}`;
				}
			} else if (err.request) {
				// Request was made but no response received
				error = 'Network error. Please check your connection and try again.';
			} else {
				// Something else happened
				error = err.message || 'An unexpected error occurred';
			}
		} finally {
			loading = false;
		}
	}

	function handleCancel() {
		// Ask for confirmation if form has data
		if (formData.name || formData.description || formData.base_price) {
			if (confirm('Are you sure you want to leave? All unsaved changes will be lost.')) {
				goto('/manajemen/produk');
			}
		} else {
			goto('/manajemen/produk');
		}
	}

	function clearError() {
		error = null;
	}

	// Format price display
	function formatPrice(price) {
		if (!price) return '0';
		return parseInt(price).toLocaleString('id-ID');
	}

	// Theme management handlers
	function handleAddTheme(event) {
		const themeData = event.detail;
		// TODO: Call API to save theme when product is created
		productThemes = [...productThemes, { ...themeData, id: Date.now().toString() }];
	}

	function handleUpdateTheme(event) {
		const { id, data } = event.detail;
		productThemes = productThemes.map(theme =>
			theme.id === id ? { ...theme, ...data } : theme
		);
	}

	function handleDeleteTheme(event) {
		const themeId = event.detail;
		productThemes = productThemes.filter(theme => theme.id !== themeId);
	}

	function handleSetDefaultTheme(event) {
		const themeId = event.detail;
		productThemes = productThemes.map(theme => ({
			...theme,
			is_default: theme.id === themeId
		}));
	}

	// Dimension management handlers
	function handleAddDimension(event) {
		const dimensionData = event.detail;
		// TODO: Call API to save dimension when product is created
		productDimensions = [...productDimensions, { ...dimensionData, id: Date.now().toString() }];
	}

	function handleUpdateDimension(event) {
		const { id, data } = event.detail;
		productDimensions = productDimensions.map(dimension =>
			dimension.id === id ? { ...dimension, ...data } : dimension
		);
	}

	function handleDeleteDimension(event) {
		const dimensionId = event.detail;
		productDimensions = productDimensions.filter(dimension => dimension.id !== dimensionId);
	}

	function handleSetDefaultDimension(event) {
		const dimensionId = event.detail;
		productDimensions = productDimensions.map(dimension => ({
			...dimension,
			is_default: dimension.id === dimensionId
		}));
	}
</script>

<svelte:head>
	<title>Add New Product - Astro Works</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Add New Product</h1>
			<p class="text-gray-600 mt-1">Create a new product for your store</p>
		</div>
		<button 
			on:click={handleCancel}
			class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
			disabled={loading}
		>
			<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
			</svg>
			Back to Products
		</button>
	</div>

	<!-- Error Message -->
	{#if error}
		<div class="bg-red-50 border border-red-200 rounded-lg p-4">
			<div class="flex justify-between items-start">
				<div class="flex">
					<svg class="w-5 h-5 text-red-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
					</svg>
					<div class="ml-3">
						<h3 class="text-sm font-medium text-red-800">Error</h3>
						<p class="text-sm text-red-700 mt-1">{error}</p>
					</div>
				</div>
				<button 
					on:click={clearError}
					class="text-red-400 hover:text-red-600"
					aria-label="Dismiss error"
				>
					<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
					</svg>
				</button>
			</div>
		</div>
	{/if}

	<!-- Product Form -->
	<form on:submit|preventDefault={handleSubmit} class="space-y-6">
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Basic Information -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<h2 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
				
				<div class="space-y-4">
					<!-- Product Name -->
					<div>
						<label for="name" class="block text-sm font-medium text-gray-700 mb-2">
							Product Name <span class="text-red-500">*</span>
						</label>
						<input 
							id="name"
							type="text" 
							bind:value={formData.name}
							placeholder="Enter product name"
							class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors {validationErrors.name ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}"
							required
							disabled={loading}
						>
						{#if validationErrors.name}
							<p class="mt-1 text-sm text-red-600">{validationErrors.name}</p>
						{/if}
						{#if productSlug}
							<p class="mt-1 text-sm text-gray-500">Slug: <code class="bg-gray-100 px-1 rounded">{productSlug}</code></p>
						{/if}
					</div>

					<!-- Description -->
					<div>
						<label for="description" class="block text-sm font-medium text-gray-700 mb-2">
							Description
						</label>
						<textarea 
							id="description"
							bind:value={formData.description}
							rows="4"
							placeholder="Enter product description"
							class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors {validationErrors.description ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}"
							disabled={loading}
						></textarea>
						{#if validationErrors.description}
							<p class="mt-1 text-sm text-red-600">{validationErrors.description}</p>
						{/if}
						{#if formData.description}
							<p class="mt-1 text-sm text-gray-500">{formData.description.length}/1000 characters</p>
						{/if}
					</div>

					<!-- Base Price -->
					<div>
						<label for="base_price" class="block text-sm font-medium text-gray-700 mb-2">
							Base Price (Rp) <span class="text-red-500">*</span>
						</label>
						<input 
							id="base_price"
							type="number" 
							bind:value={formData.base_price}
							placeholder="0"
							min="0"
							step="1000"
							class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors {validationErrors.base_price ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}"
							required
							disabled={loading}
						>
						{#if validationErrors.base_price}
							<p class="mt-1 text-sm text-red-600">{validationErrors.base_price}</p>
						{/if}
					</div>

					<!-- Category -->
					<div>
						<label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">
							Category
						</label>
						<select 
							id="category_id"
							bind:value={formData.category_id}
							class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
							disabled={loading}
						>
							<option value="">Select a category</option>
							{#each categories as category}
								<option value={category.id}>{category.name}</option>
							{/each}
						</select>
						{#if categories.length === 0 && !loading}
							<p class="mt-1 text-sm text-yellow-600">No categories available. Consider creating categories first.</p>
						{/if}
					</div>
				</div>
			</div>

			<!-- Settings -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<h2 class="text-lg font-semibold text-gray-900 mb-4">Settings</h2>
				
				<div class="space-y-4">
					<!-- Status Toggles -->
					<div class="space-y-4">
						<div class="flex items-start">
							<div class="flex items-center h-5">
								<input 
									id="is_active"
									type="checkbox" 
									bind:checked={formData.is_active}
									class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
									disabled={loading}
								>
							</div>
							<div class="ml-3">
								<label for="is_active" class="text-sm font-medium text-gray-700">
									Active Product
								</label>
								<p class="text-xs text-gray-500">Active products are visible to customers</p>
							</div>
						</div>

						<div class="flex items-start">
							<div class="flex items-center h-5">
								<input 
									id="is_featured"
									type="checkbox" 
									bind:checked={formData.is_featured}
									class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
									disabled={loading}
								>
							</div>
							<div class="ml-3">
								<label for="is_featured" class="text-sm font-medium text-gray-700">
									Featured Product
								</label>
								<p class="text-xs text-gray-500">Featured products appear prominently on the homepage</p>
							</div>
						</div>
					</div>

					<!-- Preview -->
					<div class="mt-6 p-4 bg-gray-50 rounded-lg">
						<h3 class="text-sm font-medium text-gray-900 mb-3">Preview</h3>
						<div class="text-sm text-gray-600 space-y-2">
							<div class="flex justify-between">
								<span class="font-medium">Name:</span>
								<span class="text-right">{formData.name || 'Product name'}</span>
							</div>
							<div class="flex justify-between">
								<span class="font-medium">Price:</span>
								<span class="text-right">Rp {formatPrice(formData.base_price)}</span>
							</div>
							<div class="flex justify-between">
								<span class="font-medium">Category:</span>
								<span class="text-right">
									{#if formData.category_id}
										{categories.find(c => c.id === formData.category_id)?.name || 'Unknown'}
									{:else}
										Uncategorized
									{/if}
								</span>
							</div>
							<div class="flex justify-between">
								<span class="font-medium">Status:</span>
								<span class="text-right">
									{#if formData.is_active}
										<span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">Active</span>
									{:else}
										<span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">Inactive</span>
									{/if}
									{#if formData.is_featured}
										<span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800 ml-1">Featured</span>
									{/if}
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Image Upload Section -->
		{#if showImageUpload}
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<!-- Mobile Images (4:5) -->
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<MultiImageUpload
						aspect="4x5"
						productSlug={productSlug}
						label="Mobile Images"
						maxFiles={5}
						existingImages={[]}
					/>
				</div>

				<!-- Desktop Images (16:9) -->
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<MultiImageUpload
						aspect="16x9"
						productSlug={productSlug}
						label="Desktop Images"
						maxFiles={5}
						existingImages={[]}
					/>
				</div>
			</div>
		{:else}
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
				<div class="flex">
					<svg class="w-5 h-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
					</svg>
					<div class="ml-3">
						<h3 class="text-sm font-medium text-blue-800">Image Upload Available</h3>
						<p class="text-sm text-blue-700 mt-1">Enter a product name to enable image uploads</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Product Themes Section -->
		<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
			<ProductThemeManager
				themes={productThemes}
				productId={createdProductId}
				disabled={loading}
				on:add={handleAddTheme}
				on:update={handleUpdateTheme}
				on:delete={handleDeleteTheme}
				on:setDefault={handleSetDefaultTheme}
			/>
		</div>

		<!-- Product Dimensions Section -->
		<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
			<ProductDimensionManager
				dimensions={productDimensions}
				productId={createdProductId}
				disabled={loading}
				on:add={handleAddDimension}
				on:update={handleUpdateDimension}
				on:delete={handleDeleteDimension}
				on:setDefault={handleSetDefaultDimension}
			/>
		</div>

		<!-- Form Actions -->
		<div class="flex flex-col sm:flex-row sm:items-center sm:justify-end gap-4 pt-6 border-t border-gray-200">
			<button 
				type="button"
				on:click={handleCancel}
				class="w-full sm:w-auto px-6 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
				disabled={loading}
			>
				Cancel
			</button>
			<button 
				type="submit"
				class="w-full sm:w-auto px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
				disabled={loading}
			>
				{#if loading}
					<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
						<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
					</svg>
					Creating Product...
				{:else}
					Create Product
				{/if}
			</button>
		</div>
	</form>
</div>