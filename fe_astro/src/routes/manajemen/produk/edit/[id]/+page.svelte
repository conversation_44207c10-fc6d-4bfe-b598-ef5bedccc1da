<script>
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { productApi, categoryApi, generateProductSlug, getProductImages } from '$lib/api.js';
	import MultiImageUpload from '$lib/components/MultiImageUpload.svelte';
	import ProductThemeManager from '$lib/components/ProductThemeManager.svelte';
	import ProductDimensionManager from '$lib/components/ProductDimensionManager.svelte';
	import { config } from '$lib/config/env';

	let productId = $page.params.id;
	let formData = {
		name: '',
		description: '',
		base_price: '',
		category_id: '',
		is_featured: false,
		is_active: true
	};

	let categories = [];
	let loading = true;
	let saving = false;
	let error = null;
	let validationErrors = {};
	let productNotFound = false;
	let productSlug = '';
	let mobileImages = [];
	let desktopImages = [];
	let productThemes = [];
	let productDimensions = [];

	// Load product and categories on component mount
	onMount(async () => {
		await Promise.all([loadProduct(), loadCategories()]);
	});

	// Generate product slug from name and load images
	$: if (formData.name) {
		productSlug = generateProductSlug(formData.name);
		loadProductImages();
	}

	// Handle upload completion and image deletion events
	onMount(() => {
		const handleUploadComplete = (event) => {
			console.log('Upload completed:', event.detail);
			loadProductImages(); // Reload images
		};

		const handleImageDeleted = (event) => {
			console.log('Image deleted:', event.detail);
			loadProductImages(); // Reload images
		};

		document.addEventListener('upload-complete', handleUploadComplete);
		document.addEventListener('image-deleted', handleImageDeleted);

		return () => {
			document.removeEventListener('upload-complete', handleUploadComplete);
			document.removeEventListener('image-deleted', handleImageDeleted);
		};
	});

	async function loadProductImages() {
		if (!productSlug) return;

		try {
			console.log('Loading images for product slug:', productSlug);
			const images = await getProductImages(productSlug);
			console.log('Images loaded:', images);
			mobileImages = images.mobile_images || [];
			desktopImages = images.desktop_images || [];
		} catch (err) {
			console.error('Failed to load product images:', err);
			mobileImages = [];
			desktopImages = [];
		}
	}

	async function loadProduct() {
		try {
			console.log('Loading product with ID:', productId);
			console.log('API URL:', `${config.api.url}/products/${productId}`);
			const product = await productApi.getById(productId);
			console.log('Product loaded successfully:', product);
			formData = {
				name: product.name || '',
				description: product.description || '',
				base_price: product.base_price?.toString() || '',
				category_id: product.category_id || '',
				is_featured: product.is_featured || false,
				is_active: product.is_active !== false // Default to true if undefined
			};
		} catch (err) {
			console.error('Error loading product:', err);
			if (err instanceof Error && err.message.includes('404')) {
				productNotFound = true;
				console.log('Product not found - will show not found message');
			} else {
				error = err instanceof Error ? err.message : 'Failed to load product';
				console.error('Product loading error:', error);
			}
		} finally {
			loading = false;
		}
	}

	async function loadCategories() {
		try {
			categories = await categoryApi.getAll();
		} catch (err) {
			console.error('Failed to load categories:', err);
		}
	}

	function validateForm() {
		validationErrors = {};

		if (!formData.name?.trim()) {
			validationErrors.name = 'Product name is required';
		}

		if (!formData.base_price || parseFloat(formData.base_price) <= 0) {
			validationErrors.base_price = 'Valid price is required';
		}

		return Object.keys(validationErrors).length === 0;
	}

	async function handleSubmit() {
		if (!validateForm()) {
			return;
		}

		try {
			saving = true;
			error = null;

			// Convert price to number
			const productData = {
				...formData,
				base_price: parseFloat(formData.base_price),
				category_id: formData.category_id || null
			};

			await productApi.update(productId, productData);
			
			// Redirect to product list with success message
			goto('/manajemen/produk?success=updated');
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to update product';
		} finally {
			saving = false;
		}
	}

	function handleCancel() {
		goto('/manajemen/produk');
	}

	async function handleDelete() {
		if (!confirm(`Are you sure you want to delete "${formData.name}"? This action cannot be undone.`)) {
			return;
		}

		try {
			saving = true;
			await productApi.delete(productId);
			goto('/manajemen/produk?success=deleted');
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to delete product';
			saving = false;
		}
	}

	// Theme management handlers
	function handleAddTheme(event) {
		const themeData = event.detail;
		// TODO: Call API to save theme
		productThemes = [...productThemes, { ...themeData, id: Date.now().toString() }];
	}

	function handleUpdateTheme(event) {
		const { id, data } = event.detail;
		productThemes = productThemes.map(theme =>
			theme.id === id ? { ...theme, ...data } : theme
		);
	}

	function handleDeleteTheme(event) {
		const themeId = event.detail;
		productThemes = productThemes.filter(theme => theme.id !== themeId);
	}

	function handleSetDefaultTheme(event) {
		const themeId = event.detail;
		productThemes = productThemes.map(theme => ({
			...theme,
			is_default: theme.id === themeId
		}));
	}

	// Dimension management handlers
	function handleAddDimension(event) {
		const dimensionData = event.detail;
		// TODO: Call API to save dimension
		productDimensions = [...productDimensions, { ...dimensionData, id: Date.now().toString() }];
	}

	function handleUpdateDimension(event) {
		const { id, data } = event.detail;
		productDimensions = productDimensions.map(dimension =>
			dimension.id === id ? { ...dimension, ...data } : dimension
		);
	}

	function handleDeleteDimension(event) {
		const dimensionId = event.detail;
		productDimensions = productDimensions.filter(dimension => dimension.id !== dimensionId);
	}

	function handleSetDefaultDimension(event) {
		const dimensionId = event.detail;
		productDimensions = productDimensions.map(dimension => ({
			...dimension,
			is_default: dimension.id === dimensionId
		}));
	}
</script>

<svelte:head>
	<title>Edit Product - Astro Works</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Edit Product</h1>
			<p class="text-gray-600 mt-1">Update product information</p>
		</div>
		<div class="flex items-center space-x-3">
			<button 
				on:click={handleDelete}
				disabled={loading || saving}
				class="inline-flex items-center px-4 py-2 border border-red-300 rounded-lg text-red-700 bg-white hover:bg-red-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
			>
				<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
				</svg>
				Delete
			</button>
			<button 
				on:click={handleCancel}
				class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
			>
				<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
				</svg>
				Back to Products
			</button>
		</div>
	</div>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
			<span class="ml-3 text-gray-600">Loading product...</span>
		</div>
	{:else if productNotFound}
		<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
			<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
			</svg>
			<h3 class="mt-4 text-lg font-medium text-gray-900">Product Not Found</h3>
			<p class="mt-2 text-gray-600">The product you're looking for doesn't exist or has been deleted.</p>
			<button 
				on:click={handleCancel}
				class="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
			>
				Back to Products
			</button>
		</div>
	{:else}
		<!-- Error Message -->
		{#if error}
			<div class="bg-red-50 border border-red-200 rounded-lg p-4">
				<div class="flex">
					<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
					</svg>
					<div class="ml-3">
						<h3 class="text-sm font-medium text-red-800">Error updating product</h3>
						<p class="text-sm text-red-700 mt-1">{error}</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Product Form -->
		<form on:submit|preventDefault={handleSubmit} class="space-y-6">
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<!-- Basic Information -->
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<h2 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
					
					<div class="space-y-4">
						<!-- Product Name -->
						<div>
							<label for="name" class="block text-sm font-medium text-gray-700 mb-2">
								Product Name <span class="text-red-500">*</span>
							</label>
							<input 
								id="name"
								type="text" 
								bind:value={formData.name}
								placeholder="Enter product name"
								class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 {validationErrors.name ? 'border-red-500' : ''}"
								required
							>
							{#if validationErrors.name}
								<p class="mt-1 text-sm text-red-600">{validationErrors.name}</p>
							{/if}
						</div>

						<!-- Description -->
						<div>
							<label for="description" class="block text-sm font-medium text-gray-700 mb-2">
								Description
							</label>
							<textarea 
								id="description"
								bind:value={formData.description}
								rows="4"
								placeholder="Enter product description"
								class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
							></textarea>
						</div>

						<!-- Base Price -->
						<div>
							<label for="base_price" class="block text-sm font-medium text-gray-700 mb-2">
								Base Price (Rp) <span class="text-red-500">*</span>
							</label>
							<input 
								id="base_price"
								type="number" 
								bind:value={formData.base_price}
								placeholder="0"
								min="0"
								step="1000"
								class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 {validationErrors.base_price ? 'border-red-500' : ''}"
								required
							>
							{#if validationErrors.base_price}
								<p class="mt-1 text-sm text-red-600">{validationErrors.base_price}</p>
							{/if}
						</div>

						<!-- Category -->
						<div>
							<label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">
								Category
							</label>
							<select 
								id="category_id"
								bind:value={formData.category_id}
								class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
							>
								<option value="">Select a category</option>
								{#each categories as category}
									<option value={category.id}>{category.name}</option>
								{/each}
							</select>
						</div>
					</div>
				</div>

				<!-- Settings -->
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<h2 class="text-lg font-semibold text-gray-900 mb-4">Settings</h2>
					
					<div class="space-y-4">
						<!-- Status Toggles -->
						<div class="space-y-3">
							<div class="flex items-center">
								<input 
									id="is_active"
									type="checkbox" 
									bind:checked={formData.is_active}
									class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
								>
								<label for="is_active" class="ml-3 text-sm font-medium text-gray-700">
									Active Product
								</label>
							</div>
							<p class="text-xs text-gray-500 ml-7">Active products are visible to customers</p>

							<div class="flex items-center">
								<input 
									id="is_featured"
									type="checkbox" 
									bind:checked={formData.is_featured}
									class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
								>
								<label for="is_featured" class="ml-3 text-sm font-medium text-gray-700">
									Featured Product
								</label>
							</div>
							<p class="text-xs text-gray-500 ml-7">Featured products appear prominently on the homepage</p>
						</div>

						<!-- Preview -->
						<div class="mt-6 p-4 bg-gray-50 rounded-lg">
							<h3 class="text-sm font-medium text-gray-900 mb-2">Preview</h3>
							<div class="text-sm text-gray-600 space-y-1">
								<p><strong>Name:</strong> {formData.name || 'Product name'}</p>
								<p><strong>Price:</strong> Rp {formData.base_price ? parseInt(formData.base_price).toLocaleString() : '0'}</p>
								<p><strong>Status:</strong> 
									{#if formData.is_active}
										<span class="text-green-600">Active</span>
									{:else}
										<span class="text-red-600">Inactive</span>
									{/if}
									{#if formData.is_featured}
										<span class="text-yellow-600">• Featured</span>
									{/if}
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Image Management Section -->
			{#if productSlug}
				<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
					<!-- Mobile Images (4:5) -->
					<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
						<MultiImageUpload
							aspect="4x5"
							productSlug={productSlug}
							label="Mobile Images"
							maxFiles={5}
							existingImages={mobileImages}
						/>
					</div>

					<!-- Desktop Images (16:9) -->
					<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
						<MultiImageUpload
							aspect="16x9"
							productSlug={productSlug}
							label="Desktop Images"
							maxFiles={5}
							existingImages={desktopImages}
						/>
					</div>
				</div>
			{/if}

			<!-- Product Themes Section -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<ProductThemeManager
					themes={productThemes}
					productId={productId}
					disabled={saving}
					on:add={handleAddTheme}
					on:update={handleUpdateTheme}
					on:delete={handleDeleteTheme}
					on:setDefault={handleSetDefaultTheme}
				/>
			</div>

			<!-- Product Dimensions Section -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<ProductDimensionManager
					dimensions={productDimensions}
					productId={productId}
					disabled={saving}
					on:add={handleAddDimension}
					on:update={handleUpdateDimension}
					on:delete={handleDeleteDimension}
					on:setDefault={handleSetDefaultDimension}
				/>
			</div>

			<!-- Form Actions -->
			<div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 mt-6">
				<button 
					type="button"
					on:click={handleCancel}
					class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
					disabled={saving}
				>
					Cancel
				</button>
				<button 
					type="submit"
					class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
					disabled={saving}
				>
					{#if saving}
						<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
						Updating...
					{:else}
						Update Product
					{/if}
				</button>
			</div>
		</form>
	{/if}
</div>
