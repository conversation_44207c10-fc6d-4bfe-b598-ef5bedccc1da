<script lang="ts">
	/**
	 * Product Detail Page - Main Container
	 * Manages global state for product selections and coordinates all child components
	 */
	
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { generateProductSlug } from '$lib/api.js';

	// Get data from page load function
	let { data } = $props();
	import Header from '$lib/components/Header.svelte';
	import ProductImageGallery from '$lib/components/ProductImageGallery.svelte';
	import ColorPicker from '$lib/components/ColorPicker.svelte';
	import SizeSelector from '$lib/components/SizeSelector.svelte';
	import AccessoriesSelector from '$lib/components/AccessoriesSelector.svelte';
	import QuantitySelector from '$lib/components/QuantitySelector.svelte';
	import PriceBreakdown from '$lib/components/PriceBreakdown.svelte';
	
	// TypeScript interfaces
	interface Theme {
		id: string;
		name: string;
		color: string;
		isDefault?: boolean;
	}

	interface Size {
		id: string;
		label: string;
		dimensions: {
			width: number;
			height: number;
			unit: string;
		};
		price: number;
		isDefault?: boolean;
	}

	interface Accessory {
		id: string;
		name: string;
		description?: string;
		price: number;
		category: string;
		image?: string;
		maxQuantity?: number;
	}

	interface Product {
		id: string;
		baseName: string;
		shortDescription?: string;
		longDescription?: string;
		themes: Theme[];
		sizes: Size[];
		accessories: Accessory[];
		images: string[];
		category: string;
		isActive: boolean;
	}

	interface ProductSelection {
		theme: Theme;
		size: Size;
		accessories: Map<string, {accessory: Accessory, quantity: number}>;
		quantity: number;
	}

	// Reactive state using Svelte 5 runes
	let product = $state<Product | null>(null);
	let loading = $state(true);
	let error = $state<string | null>(null);
	let isNotFound = $state(false);
	
	// Product selection state
	let selectedTheme = $state<Theme | null>(null);
	let selectedSize = $state<Size | null>(null);
	let selectedAccessories = $state<Map<string, {accessory: Accessory, quantity: number}>>(new Map());
	let quantity = $state(1);
	
	// Derived reactive values
	let dynamicProductTitle = $derived(() => {
		if (!product || !selectedTheme || !selectedSize) return '';

		const accessoryNames = Array.from(selectedAccessories.values())
			.filter(item => item.quantity > 0)
			.map(item => item.accessory.name)
			.join(' + ');

		const baseTitle = `${product.baseName} ${selectedTheme.name} ${selectedSize.label}`;
		return accessoryNames ? `${baseTitle} + ${accessoryNames}` : baseTitle;
	});

	// Get the title value for template usage
	let productTitle = $derived(dynamicProductTitle());
	
	let basePrice = $derived(() => {
		return selectedSize ? selectedSize.price * quantity : 0;
	});

	let accessoriesTotal = $derived(() => {
		let total = 0;
		for (const item of selectedAccessories.values()) {
			total += item.accessory.price * item.quantity * quantity;
		}
		return total;
	});

	let finalTotal = $derived(() => basePrice() + accessoriesTotal());
	
	// Get product ID from URL
	const productId = $page.params.id;

	/**
	 * Fetch accessories from backend API
	 */
	async function fetchAccessories(): Promise<Accessory[]> {
		try {
			const response = await fetch('/api/v1/products/accessories');
			if (response.ok) {
				const accessories = await response.json();
				return accessories.map((acc: any) => ({
					id: acc.id,
					name: acc.name,
					description: acc.description,
					price: acc.base_price,
					category: 'Accessories', // All items from this endpoint are accessories
					image: acc.image_url,
					maxQuantity: 99
				}));
			} else {
				console.error('Failed to fetch accessories: HTTP', response.status);
				return [];
			}
		} catch (err) {
			console.error('Failed to fetch accessories:', err);
			return [];
		}
	}

	/**
	 * Fetch product data from backend API
	 */
	async function fetchProduct() {
		try {
			loading = true;
			error = null;

			// Fetch accessories from backend
			const accessories = await fetchAccessories();

			const response = await fetch(`/api/v1/products/${productId}`);
			if (!response.ok) {
				throw new Error(`Failed to fetch product: ${response.status}`);
			}

			const productData = await response.json();

		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load product';
			console.error('Error fetching product:', err);
		} finally {
			loading = false;
		}
	}
	
	/**
	 * Handle theme selection
	 */
	function handleThemeChange(theme: Theme) {
		selectedTheme = theme;
	}
	
	/**
	 * Handle size selection
	 */
	function handleSizeChange(size: Size) {
		selectedSize = size;
	}
	
	/**
	 * Handle accessory quantity change
	 */
	function handleAccessoryChange(accessory: Accessory, newQuantity: number) {
		if (newQuantity <= 0) {
			selectedAccessories.delete(accessory.id);
		} else {
			selectedAccessories.set(accessory.id, { accessory, quantity: newQuantity });
		}
		// Trigger reactivity
		selectedAccessories = new Map(selectedAccessories);
	}
	
	/**
	 * Handle main quantity change
	 */
	function handleQuantityChange(newQuantity: number) {
		quantity = Math.max(1, newQuantity);
	}
	
	/**
	 * Add product to cart
	 */
	function addToCart() {
		if (!product || !selectedTheme || !selectedSize) return;

		const cartItem = {
			id: product.id || 'unknown',
			name: productTitle,
			category: product.category || 'Furniture',
			price: selectedSize.price,
			quantity: quantity,
			selectedTheme: selectedTheme.name,
			selectedSize: selectedSize.label,
			// Enhanced data for offline functionality
			image: product.images?.[0] || '/images/placeholder.jpg',
			description: product.shortDescription || product.longDescription || '',
			isStandalone: false,
			// Store complete theme and size data
			selectedThemeData: {
				id: selectedTheme.id,
				name: selectedTheme.name,
				color: selectedTheme.color,
				isDefault: selectedTheme.isDefault
			},
			selectedSizeData: {
				id: selectedSize.id,
				label: selectedSize.label,
				price: selectedSize.price,
				dimensions: selectedSize.dimensions,
				isDefault: selectedSize.isDefault
			},
			accessories: Array.from(selectedAccessories.values())
				.filter(item => item.quantity > 0)
				.map(item => ({
					id: item.accessory.id,
					name: item.accessory.name,
					price: item.accessory.price,
					quantity: item.quantity,
					selectedOptions: undefined,
					// Store complete accessory data
					description: item.accessory.description || '',
					image: item.accessory.image || '/images/accessories/placeholder.jpg'
				})),
			// Store complete product data for offline use
			productData: {
				baseName: product.baseName,
				shortDescription: product.shortDescription,
				longDescription: product.longDescription,
				images: product.images,
				themes: product.themes,
				sizes: product.sizes,
				availableAccessories: product.accessories,
				specifications: []
			}
		};

		// Save to localStorage
		if (typeof localStorage !== 'undefined') {
			const existingCart = localStorage.getItem('astro-works-cart');
			let cart = [];

			if (existingCart) {
				try {
					cart = JSON.parse(existingCart);
				} catch (e) {
					console.error('Error parsing cart:', e);
				}
			}

			// Check if item already exists in cart
			const existingItemIndex = cart.findIndex((item: any) =>
				item.id === cartItem.id &&
				item.selectedTheme === cartItem.selectedTheme &&
				item.selectedSize === cartItem.selectedSize
			);

			if (existingItemIndex >= 0) {
				// Update existing item quantity
				cart[existingItemIndex].quantity += cartItem.quantity;
			} else {
				// Add new item
				cart.push(cartItem);
			}

			localStorage.setItem('astro-works-cart', JSON.stringify(cart));

			// Log for debugging
			console.log('Product added to cart:', cartItem);
			console.log('Updated cart:', cart);

			// Redirect to cart page
			goto('/checkout');
		}
	}

	/**
	 * Add accessory to cart (simplified version for accessories)
	 */
	function addToCartAccessory() {
		if (!product) {
			console.error('Missing product data');
			return;
		}

		const cartItem = {
			id: `${product.id}-accessory-${Date.now()}`,
			name: product.baseName,
			category: (product as any).category || 'Accessories',
			price: (product as any).price || 0,
			quantity: quantity,
			isAccessory: true,
			// Enhanced data for offline functionality
			image: product.images?.[0] || '/images/placeholder.jpg',
			description: (product as any).description || '',
			isStandalone: true,
			// Store complete product data for offline use
			productData: {
				baseName: product.baseName,
				description: (product as any).description,
				images: product.images,
				price: (product as any).price || 0,
				category: (product as any).category || 'Accessories'
			}
		};

		// Save to localStorage
		if (typeof localStorage !== 'undefined') {
			const existingCart = localStorage.getItem('astro-works-cart');
			let cart = [];

			if (existingCart) {
				try {
					cart = JSON.parse(existingCart);
				} catch (e) {
					console.error('Error parsing cart:', e);
				}
			}

			// Check if item already exists in cart
			const existingItemIndex = cart.findIndex((item: any) =>
				item.id === cartItem.id && item.isAccessory
			);

			if (existingItemIndex >= 0) {
				// Update existing item quantity
				cart[existingItemIndex].quantity += cartItem.quantity;
			} else {
				// Add new item
				cart.push(cartItem);
			}

			localStorage.setItem('astro-works-cart', JSON.stringify(cart));

			// Log for debugging
			console.log('Accessory added to cart:', cartItem);
			console.log('Updated cart:', cart);

			// Redirect to cart page
			goto('/checkout');
		}
	}


	// Load product data on mount
	onMount(() => {
		if (data.product) {
			product = data.product;
			// Set default selections
			selectedTheme = data.product.themes.find((t: Theme) => t.isDefault) || data.product.themes[0];
			selectedSize = data.product.sizes.find((s: Size) => s.isDefault) || data.product.sizes[0];
			loading = false;
		} else if (data.error) {
			error = data.error;
			isNotFound = (data as any).isNotFound || false;
			loading = false;
		} else {
			fetchProduct();
		}
	});
</script>

<svelte:head>
	<title>{productTitle || 'Product Detail'} - Astro Works</title>
	<meta name="description" content={product?.shortDescription || 'Premium furniture from Astro Works'} />
</svelte:head>

<!-- Main Layout -->
<div class="min-h-screen bg-[#5F44F0] lg:bg-gray-50 ">
	<!-- Header -->
	<Header />
	
	
	<!-- Main Content -->
	<main class="lg:container mx-auto  px-4 sm:px-6 lg:px-8 py-32 rounded-t-[48px] bg-gray-50 min-h-screen lg:min-h-0">
		
	<!-- Breadcrumb Navigation -->
	<!-- <nav class="bg-white border-b mb-16">
		<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
			<ol class="flex items-center space-x-2 text-sm text-gray-500">
				<li><a href="/" class="hover:text-blue-600 transition-colors">Home</a></li>
				<li class="flex items-center">
					<svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
					</svg>
					{#if product}
						<span class="text-gray-700">{product.category}</span>
					{/if}
				</li>
				<li class="flex items-center">
					<svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-gray-900 font-medium">{productTitle || 'Product Detail'}</span>
				</li>
			</ol>
		</div>
	</nav> -->
		{#if loading}
			<!-- Loading State -->
			<div class="flex items-center justify-center py-20">
				<div class="text-center">
					<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
					<p class="text-gray-600">Loading product details...</p>
				</div>
			</div>
		{:else if error}
			<!-- Error State -->
			<div class="text-center py-20">
				<div class="text-red-500 text-6xl mb-4">
					{#if isNotFound}
						🔍
					{:else}
						⚠️
					{/if}
				</div>
				<h2 class="text-2xl font-bold text-gray-900 mb-2">
					{#if isNotFound}
						Product Not Found
					{:else}
						Something Went Wrong
					{/if}
				</h2>
				<p class="text-gray-600 mb-6">{error}</p>

				{#if isNotFound}
					<!-- Suggestions for 404 -->
					<div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6 max-w-md mx-auto">
						<h3 class="text-lg font-semibold text-blue-900 mb-3">What you can do:</h3>
						<ul class="text-left text-blue-800 space-y-2">
							<li>• Check if the product link is correct</li>
							<li>• Browse our current products below</li>
							<li>• Contact us if you need help finding a specific item</li>
						</ul>
					</div>
				{/if}

				<div class="space-x-4">
					<a href="/" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 inline-block">
						← Back to Home
					</a>
					{#if isNotFound}
						<a href="/products" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors duration-200 inline-block">
							Browse Products
						</a>
					{/if}
				</div>
			</div>
		{:else if product}
			<!-- Check if this is an accessory product -->
			{#if (product as any).isAccessory || (product as any).category?.toLowerCase() === 'accessories'}
				<!-- Accessory Product Layout -->
				<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
					<!-- Left Column - Image Gallery -->
					<div class="space-y-4">
						<ProductImageGallery
							productSlug={generateProductSlug(product.baseName)}
							selectedTheme=""
							productName={product.baseName}
							images={product.images}
							imageCount={Math.max(product.images.length, 1)}
							rounded="3xl"
						/>
					</div>

					<!-- Right Column - Accessory Details -->
					<div class="space-y-6">
						<!-- Accessory Title and Price -->
						<div class="space-y-2">
							<div class="inline-block bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full mb-2">
								Accessory
							</div>
							<h1 class="text-2xl lg:text-3xl font-bold text-gray-900">{product.baseName}</h1>
							<div class="flex items-center space-x-2">
								<span class="text-sm text-gray-500">Rp</span>
								<span class="text-3xl font-bold text-gray-900">
									{((product as any).price * quantity).toLocaleString('id-ID')}
								</span>
							</div>
						</div>

						<!-- Accessory Description -->
						{#if (product as any).description}
							<div class="space-y-3">
								<h3 class="text-lg font-semibold text-gray-900">Description</h3>
								<p class="text-gray-600 leading-relaxed">{(product as any).description}</p>
							</div>
						{/if}

						<!-- Quantity and Order Section -->
						<div class="border-t pt-6 space-y-4">
							<!-- Quantity Selector -->
							<div class="flex items-center justify-between">
								<span class="text-lg font-semibold text-gray-900">Quantity</span>
								<QuantitySelector
									quantity={quantity}
									onQuantityChange={handleQuantityChange}
								/>
							</div>

							<!-- Simple Price Display -->
							<div class="bg-gray-50 p-4 rounded-lg">
								<div class="flex justify-between items-center">
									<span class="text-gray-600">Unit Price:</span>
									<span class="font-medium">Rp {((product as any).price).toLocaleString('id-ID')}</span>
								</div>
								<div class="flex justify-between items-center text-lg font-bold border-t pt-2 mt-2">
									<span>Total:</span>
									<span class="text-blue-600">Rp {((product as any).price * quantity).toLocaleString('id-ID')}</span>
								</div>
							</div>

							<!-- Action Buttons -->
							<div class="space-y-3">
								<!-- Add to Cart Button -->
								<button
									class="w-full bg-blue-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center space-x-2"
									onclick={addToCartAccessory}
								>
									<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
									</svg>
									<span>Add to Cart</span>
								</button>
							</div>
						</div>
					</div>
				</div>
			{:else}
				<!-- Regular Product Layout -->
				<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
					<!-- Left Column - Image Gallery -->
					<div class="space-y-4">
						<ProductImageGallery
							productSlug={generateProductSlug(product.baseName)}
							selectedTheme={selectedTheme?.name || ''}
							productName={product.baseName}
							images={product.images}
							imageCount={Math.max(product.images.length, 1)}
							rounded="3xl"
						/>
					</div>

					<!-- Right Column - Product Details -->
					<div class="space-y-6">
						<!-- Product Title and Price -->
						<div class="space-y-2">
							<h1 class="text-2xl lg:text-3xl font-bold text-gray-900">
								{productTitle}
							</h1>
							<div class="flex items-center space-x-2">
								<span class="text-sm text-gray-500">Rp</span>
								<span class="text-3xl font-bold text-gray-900">
									{finalTotal().toLocaleString('id-ID')}
								</span>
							</div>
						</div>

						<!-- Theme Selector -->
						{#if product.themes && product.themes.length > 0}
							<div class="space-y-3">
								<h3 class="text-lg font-semibold text-gray-900">Warna/Tema</h3>
								<ColorPicker
									themes={product.themes}
									selectedTheme={selectedTheme?.id || ''}
									onThemeChange={handleThemeChange}
								/>
							</div>
						{:else}
							<div class="space-y-3">
								<h3 class="text-lg font-semibold text-gray-900">Warna/Tema</h3>
								<div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
									<p class="text-sm text-gray-600">
										Tidak ada detail mengenai warna untuk produk ini.
									</p>
								</div>
							</div>
						{/if}

						<!-- Size Selector -->
						{#if product.sizes && product.sizes.length > 0}
							<div class="space-y-3">
								<h3 class="text-lg font-semibold text-gray-900">Ukuran/Dimensi</h3>
								<SizeSelector
									sizes={product.sizes}
									selectedSize={selectedSize?.id || ''}
									onSizeChange={handleSizeChange}
								/>
							</div>
						{:else}
							<div class="space-y-3">
								<h3 class="text-lg font-semibold text-gray-900">Ukuran/Dimensi</h3>
								<div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
									<p class="text-sm text-gray-600">
										Tidak ada detail mengenai ukuran untuk produk ini.
									</p>
								</div>
							</div>
						{/if}

						<!-- Product Description -->
						{#if product.longDescription}
							<div class="space-y-3">
								<h3 class="text-lg font-semibold text-gray-900">Description</h3>
								<p class="text-gray-600 leading-relaxed">{product.longDescription}</p>
							</div>
						{/if}

						<!-- Accessories Selector - Hidden for accessories category -->
						 {#if product?.accessories?.length > 0 && product?.category?.toLowerCase?.() !== 'accessories'}
							<div class="space-y-3">
								<h3 class="text-lg font-semibold text-gray-900">Additional Item</h3>
								<AccessoriesSelector
									accessories={product.accessories}
									selectedAccessories={selectedAccessories}
									onAccessoryChange={handleAccessoryChange}
								/>
							</div>
						{/if}

						<!-- Quantity and Order Section -->
						<div class="border-t pt-6 space-y-4">
							<!-- Quantity Selector -->
							<div class="flex items-center justify-between">
								<span class="text-lg font-semibold text-gray-900">Quantity</span>
								<QuantitySelector
									quantity={quantity}
									onQuantityChange={handleQuantityChange}
								/>
							</div>

							<!-- Price Calculator -->
							<PriceBreakdown
								basePrice={basePrice()}
								accessoriesTotal={accessoriesTotal()}
								finalTotal={finalTotal()}
								selectedAccessories={selectedAccessories}
								quantity={quantity}
							/>

							<!-- Action Buttons -->
							<div class="space-y-3">
								<!-- Add to Cart Button -->
								<button
									class="w-full bg-blue-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center space-x-2"
									onclick={addToCart}
								>
									<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
									</svg>
									<span>Add to Cart</span>
								</button>
							</div>
						</div>
					</div>
				</div>
			{/if}
		{/if}
	</main>
</div>