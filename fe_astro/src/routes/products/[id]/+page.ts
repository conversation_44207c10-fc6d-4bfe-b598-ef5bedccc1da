import type { PageLoad } from './$types';



export const load: PageLoad = async ({ params, fetch }) => {
	const { id } = params;

	try {
		// First, try to get all products and find the one that matches the slug
		const productsResponse = await fetch('/api/v1/products');

		if (productsResponse.ok) {
			const allProducts = await productsResponse.json();

			// Find product by slug or ID
			const matchedProduct = allProducts.find((product: any) =>
				product.slug === id || product.id === id
			);

			if (matchedProduct) {
				// Transform backend API response to frontend format
				const product = {
					id: matchedProduct.id,
					baseName: matchedProduct.name,
					description: matchedProduct.description || "Premium furniture with modern design",
					images: matchedProduct.image_url ? [matchedProduct.image_url] : [
						"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=800&fit=crop",
						"https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800&h=800&fit=crop",
						"https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&h=800&fit=crop"
					],
					themes: [
						{ id: "hitam", name: "Hitam", color: "#2D3748", isDefault: true },
						{ id: "putih", name: "Putih", color: "#F7FAFC" },
						{ id: "coklat", name: "Coklat", color: "#8B4513" },
						{ id: "abu", name: "Abu-abu", color: "#718096" }
					],
					sizes: [
						{
							id: "default",
							label: "Standard Size",
							dimensions: { width: 2.4, height: 2.7, unit: "m" },
							price: parseFloat(matchedProduct.base_price),
							isDefault: true
						},
						{
							id: "large",
							label: "Large Size",
							dimensions: { width: 3.0, height: 3.0, unit: "m" },
							price: Math.round(parseFloat(matchedProduct.base_price) * 1.3),
						}
					],
					accessories: []
				};

				// Get accessories from API
				try {
					const accessoriesResponse = await fetch('/api/v1/products/accessories');
					if (accessoriesResponse.ok) {
						const accessories = await accessoriesResponse.json();
						product.accessories = accessories.map((acc: any) => ({
							id: acc.id,
							name: acc.name,
							description: acc.description,
							price: parseFloat(acc.base_price),
							category: 'Accessories',
							maxQuantity: 99
						}));
					}
				} catch (error) {
					console.error('Failed to fetch accessories:', error);
				}

				return {
					product,
					productId: id
				};
			}
		}

		// Check if this looks like a UUID (old product ID format)
		const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

		// Try individual product endpoint using slug
		const apiEndpoint = `/api/v1/products/slug/${id}`;
		const response = await fetch(apiEndpoint);

		if (response.ok) {
			const apiProduct = await response.json();

			// Fetch themes and dimensions from API using slug
			const [themesResponse, dimensionsResponse] = await Promise.all([
				fetch(`/api/v1/products/slug/${id}/themes`),
				fetch(`/api/v1/products/slug/${id}/dimensions`)
			]);

			let themes = [];
			let sizes = [];

			// Handle themes - use database data as-is
			if (themesResponse.ok) {
				themes = await themesResponse.json();
			}

			// Handle dimensions/sizes - use database data as-is
			if (dimensionsResponse.ok) {
				sizes = await dimensionsResponse.json();
			}

			// Transform backend API response to frontend format
			const product = {
				id: apiProduct.id,
				baseName: apiProduct.name,
				description: apiProduct.description || "Premium furniture with modern design",
				images: apiProduct.image_url ? [apiProduct.image_url] : [
					"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=800&fit=crop",
					"https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800&h=800&fit=crop",
					"https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&h=800&fit=crop"
				],
				themes: themes,
				sizes: sizes,
				accessories: [
					{
						id: "led-gola",
						name: "Led Gola",
						description: "LED strip lighting for ambient illumination and modern aesthetics",
						price: 3000000,
						category: "Lighting",
						maxQuantity: 5
					},
					{
						id: "tandem-box",
						name: "Tandem Box",
						description: "Soft-close drawer system for smooth and quiet operation",
						price: 1500000,
						category: "Hardware",
						maxQuantity: 10
					}
				]
			};

			return {
				product,
				productId: id
			};
		}

		// Handle specific HTTP error codes
		if (response.status === 404) {
			console.log(`Product with ID ${id} not found in database`);

			// Check if this looks like a UUID (old product ID format)
			const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
			if (uuidRegex.test(id)) {
				// This is likely an old UUID-based product ID that no longer exists
				// Return a proper 404 error instead of falling back to mock data
				return {
					product: null,
					productId: id,
					error: 'Product not found. This product may have been removed or the link is outdated.',
					isNotFound: true
				};
			}

			// For non-UUID IDs, we'll fall through to mock data
		} else {
			console.log(`API request failed with status ${response.status}: ${response.statusText}`);
		}

		// If API fails or returns 404 for non-UUID IDs, try to use mock data for demo
		console.log('API not available or product not found, checking mock data');

		// Check if this is an accessory product
		const accessoryProducts = {
			"led-gola": {
				id: "led-gola",
				baseName: "Led Gola",
				description: "Premium LED strip lighting system for ambient illumination and modern aesthetics. Perfect for under-cabinet lighting, accent lighting, and creating atmospheric mood lighting in any space.",
				category: "Accessories",
				isAccessory: true,
				price: 250000,
				images: [
					"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=800&fit=crop",
					"https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800&h=800&fit=crop",
					"https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=800&fit=crop"
				],
				themes: [
					{ id: "warm-white", name: "Warm White", color: "#FFF8DC", isDefault: true },
					{ id: "cool-white", name: "Cool White", color: "#F0F8FF" },
					{ id: "rgb", name: "RGB Color", color: "#FF6B6B" }
				],
				sizes: [
					{
						id: "1m",
						label: "1 Meter",
						dimensions: { width: 1, height: 0.01, unit: "m" },
						price: 250000,
						isDefault: true
					},
					{
						id: "2m",
						label: "2 Meters",
						dimensions: { width: 2, height: 0.01, unit: "m" },
						price: 450000
					},
					{
						id: "3m",
						label: "3 Meters",
						dimensions: { width: 3, height: 0.01, unit: "m" },
						price: 650000
					}
				],
				accessories: []
			},
			"tandem-box": {
				id: "tandem-box",
				baseName: "Tandem Box",
				description: "Premium soft-close drawer system with smooth and quiet operation. Features high-quality steel construction with advanced damping technology for superior performance and durability.",
				category: "Accessories",
				isAccessory: true,
				price: 180000,
				images: [
					"https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800&h=800&fit=crop",
					"https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=800&fit=crop",
					"https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?w=800&h=800&fit=crop"
				],
				themes: [
					{ id: "silver", name: "Silver", color: "#C0C0C0", isDefault: true },
					{ id: "black", name: "Black", color: "#2D3748" },
					{ id: "white", name: "White", color: "#F7FAFC" }
				],
				sizes: [
					{
						id: "30cm",
						label: "30cm Depth",
						dimensions: { width: 0.3, height: 0.15, unit: "m" },
						price: 180000,
						isDefault: true
					},
					{
						id: "45cm",
						label: "45cm Depth",
						dimensions: { width: 0.45, height: 0.15, unit: "m" },
						price: 220000
					},
					{
						id: "60cm",
						label: "60cm Depth",
						dimensions: { width: 0.6, height: 0.15, unit: "m" },
						price: 280000
					}
				],
				accessories: []
			},
			"glass-door": {
				id: "glass-door",
				baseName: "Glass Door",
				description: "Premium tempered glass door panels for elegant cabinet fronts. Features safety glass construction with polished edges and modern hardware for a sophisticated look.",
				category: "Accessories",
				isAccessory: true,
				price: 320000,
				images: [
					"https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=800&fit=crop",
					"https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?w=800&h=800&fit=crop",
					"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=800&fit=crop"
				],
				themes: [
					{ id: "clear", name: "Clear Glass", color: "#F0F8FF", isDefault: true },
					{ id: "frosted", name: "Frosted Glass", color: "#E6E6FA" },
					{ id: "tinted", name: "Tinted Glass", color: "#708090" }
				],
				sizes: [
					{
						id: "40x60",
						label: "40 × 60 cm",
						dimensions: { width: 0.4, height: 0.6, unit: "m" },
						price: 320000,
						isDefault: true
					},
					{
						id: "50x70",
						label: "50 × 70 cm",
						dimensions: { width: 0.5, height: 0.7, unit: "m" },
						price: 420000
					},
					{
						id: "60x80",
						label: "60 × 80 cm",
						dimensions: { width: 0.6, height: 0.8, unit: "m" },
						price: 520000
					}
				],
				accessories: []
			}
		};

		// Check if this is an accessory product
		if (accessoryProducts[id as keyof typeof accessoryProducts]) {
			return {
				product: accessoryProducts[id as keyof typeof accessoryProducts],
				productId: id
			};
		}

		// Default main product mock data
		const mockProduct = {
			id: id,
			baseName: "Fantasy TV Cabinet",
			description: "Premium TV cabinet with modern design and ample storage space",
			images: [
				"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=800&fit=crop",
				"https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800&h=800&fit=crop",
				"https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&h=800&fit=crop"
			],
			themes: [
				{ id: "hitam", name: "Hitam", color: "#2D3748", isDefault: true },
				{ id: "putih", name: "Putih", color: "#F7FAFC" },
				{ id: "coklat", name: "Coklat", color: "#8B4513" },
				{ id: "abu", name: "Abu-abu", color: "#718096" }
			],
			sizes: [
				{
					id: "2x3m",
					label: "2,4 × 2,7m",
					dimensions: { width: 2.4, height: 2.7, unit: "m" },
					price: 1590000,
					isDefault: true
				},
				{
					id: "3x3m",
					label: "3,0 × 3,0m",
					dimensions: { width: 3.0, height: 3.0, unit: "m" },
					price: 2190000
				},
				{
					id: "custom",
					label: "Custom Size",
					dimensions: { width: 0, height: 0, unit: "m" },
					price: 0
				}
			],
			accessories: [
				{
					id: "led-gola",
					name: "Led Gola",
					description: "LED strip lighting for ambient illumination and modern aesthetics",
					price: 250000,
					category: "Lighting",
					maxQuantity: 5
				},
				{
					id: "tandem-box",
					name: "Tandem Box",
					description: "Soft-close drawer system for smooth and quiet operation",
					price: 180000,
					category: "Hardware",
					maxQuantity: 10
				},
				{
					id: "glass-door",
					name: "Glass Door",
					description: "Tempered glass door panels for elegant cabinet fronts",
					price: 320000,
					category: "Doors",
					maxQuantity: 4
				},
				{
					id: "soft-close-hinges",
					name: "Soft Close Hinges",
					description: "Premium soft-close hinges for cabinet doors",
					price: 150000,
					category: "Hardware",
					maxQuantity: 8
				},
				{
					id: "pull-out-basket",
					name: "Pull Out Basket",
					description: "Wire basket organizer with smooth sliding mechanism",
					price: 280000,
					category: "Storage",
					maxQuantity: 6
				},
				{
					id: "cabinet-lighting",
					name: "Cabinet Lighting",
					description: "Under-cabinet LED lighting system with dimmer control",
					price: 350000,
					category: "Lighting",
					maxQuantity: 3
				}
			]
		};

		return {
			product: mockProduct,
			productId: id
		};

	} catch (error) {
		console.error('Error loading product:', error);

		// Return error state
		return {
			product: null,
			productId: id,
			error: error instanceof Error ? error.message : 'Failed to load product'
		};
	}
};
