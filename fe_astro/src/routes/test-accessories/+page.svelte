<script lang="ts">
	/**
	 * Test page for AccessoriesSelector component
	 * Tests the updated component with image display and unlimited quantities
	 */
	
	import AccessoriesSelector from '$lib/components/AccessoriesSelector.svelte';
	import { config } from '$lib/config/env';
	
	// Mock accessories data for testing
	const mockAccessories = [
		{
			id: 'led-gola',
			name: 'LED Gola',
			description: 'Premium LED lighting system for modern kitchen cabinets',
			price: 250000,
			category: 'Lighting',
			image: `${config.api.imagesUrl}/additional-items/led-gola.jpg`
		},
		{
			id: 'tandem-box',
			name: 'Tandem Box',
			description: 'High-quality drawer system with soft-close mechanism',
			price: 180000,
			category: 'Hardware',
			image: `${config.api.imagesUrl}/additional-items/tandem-box.jpg`
		},
		{
			id: 'glass-door',
			name: 'Glass Door',
			description: 'Tempered glass door panels for elegant cabinet fronts',
			price: 320000,
			category: 'Doors',
			image: `${config.api.imagesUrl}/additional-items/glass-door.jpg`
		},
		{
			id: 'soft-close-hinges',
			name: 'Soft Close Hinges',
			description: 'Premium hinges with soft-close technology',
			price: 95000,
			category: 'Hardware',
			image: `${config.api.imagesUrl}/additional-items/soft-close-hinges.jpg`
		},
		{
			id: 'pull-out-basket',
			name: 'Pull Out Basket',
			description: 'Wire basket system for easy storage access',
			price: 150000,
			category: 'Storage',
			image: `${config.api.imagesUrl}/additional-items/pull-out-basket.jpg`
		},
		{
			id: 'cabinet-lighting',
			name: 'Cabinet Lighting',
			description: 'Under-cabinet LED strip lighting',
			price: 120000,
			category: 'Lighting',
			image: `${config.api.imagesUrl}/additional-items/cabinet-lighting.jpg`
		}
	];
	
	// Component state
	let selectedAccessories = $state<Map<string, {accessory: any, quantity: number}>>(new Map());
	
	/**
	 * Handle accessory quantity change
	 */
	function handleAccessoryChange(accessory: any, quantity: number) {
		if (quantity <= 0) {
			selectedAccessories.delete(accessory.id);
		} else {
			selectedAccessories.set(accessory.id, { accessory, quantity });
		}
		// Trigger reactivity
		selectedAccessories = new Map(selectedAccessories);
		console.log('Selected accessories updated:', selectedAccessories);
	}
	
	/**
	 * Calculate total price
	 */
	function calculateTotal(): number {
		let total = 0;
		for (const item of selectedAccessories.values()) {
			total += item.accessory.price * item.quantity;
		}
		return total;
	}
	
	/**
	 * Format price to Indonesian Rupiah
	 */
	function formatPrice(price: number): string {
		return new Intl.NumberFormat('id-ID').format(price);
	}
</script>

<svelte:head>
	<title>Test Accessories Selector - Astro Works</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 py-8">
	<div class="container mx-auto px-4">
		<div class="max-w-4xl mx-auto">
			<!-- Header -->
			<div class="text-center mb-8">
				<h1 class="text-3xl font-bold text-gray-900 mb-4">AccessoriesSelector Component Test</h1>
				<p class="text-gray-600">
					Testing the updated AccessoriesSelector component with image display and unlimited quantities
				</p>
			</div>
			
			<!-- Test Features -->
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
				<h2 class="text-lg font-semibold text-blue-900 mb-2">Test Features</h2>
				<ul class="text-sm text-blue-800 space-y-1">
					<li>✅ Images displayed above accessory names (h-40 height, object-cover, rounded corners)</li>
					<li>✅ Images sourced from /static/images/additional-items/ directory</li>
					<li>✅ No quantity restrictions - unlimited selection allowed</li>
					<li>✅ Responsive styling with Tailwind CSS</li>
					<li>✅ Svelte 5 runes syntax ($state, $derived)</li>
					<li>✅ Proper TypeScript interfaces and accessibility</li>
				</ul>
			</div>
			
			<!-- AccessoriesSelector Component -->
			<div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
				<h2 class="text-xl font-semibold text-gray-900 mb-4">Additional Items</h2>
				<AccessoriesSelector 
					accessories={mockAccessories}
					selectedAccessories={selectedAccessories}
					onAccessoryChange={handleAccessoryChange}
				/>
			</div>
			
			<!-- Selection Summary -->
			{#if selectedAccessories.size > 0}
				<div class="bg-green-50 border border-green-200 rounded-lg p-6">
					<h2 class="text-xl font-semibold text-green-900 mb-4">Selection Summary</h2>
					<div class="space-y-3">
						{#each Array.from(selectedAccessories.values()) as item}
							<div class="flex items-center justify-between">
								<div>
									<span class="font-medium text-green-900">{item.accessory.name}</span>
									<span class="text-green-700 ml-2">× {item.quantity}</span>
								</div>
								<div class="text-green-900 font-semibold">
									Rp {formatPrice(item.accessory.price * item.quantity)}
								</div>
							</div>
						{/each}
						<div class="border-t border-green-200 pt-3 mt-3">
							<div class="flex items-center justify-between text-lg font-bold text-green-900">
								<span>Total:</span>
								<span>Rp {formatPrice(calculateTotal())}</span>
							</div>
						</div>
					</div>
				</div>
			{:else}
				<div class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
					<p class="text-gray-600">No accessories selected. Try adding some items above!</p>
				</div>
			{/if}
			
			<!-- Navigation -->
			<div class="mt-8 text-center">
				<a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block">
					← Back to Home
				</a>
			</div>
		</div>
	</div>
</div>
