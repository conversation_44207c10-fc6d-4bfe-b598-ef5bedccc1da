<script>
	import DynamicImage from '$lib/components/DynamicImage.svelte';
</script>

<svelte:head>
	<title>Test Images - Dynamic Image Component</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
	<h1 class="text-3xl font-bold text-gray-900 mb-8">Dynamic Image Component Test</h1>
	
	<div class="space-y-8">
		<!-- Test Product Images -->
		<div class="bg-white rounded-lg shadow-lg p-6">
			<h2 class="text-xl font-semibold text-gray-800 mb-4">Test Product Images</h2>
			<p class="text-gray-600 mb-6">
				This component automatically displays mobile (4:5) images on small screens and desktop (16:9) images on larger screens.
				Resize your browser window to see the responsive behavior.
			</p>
			
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				<!-- Fantasy TV Cabinet -->
				<div class="space-y-2">
					<h3 class="font-medium text-gray-700">Fantasy TV Cabinet</h3>
					<div class="w-full h-64">
						<DynamicImage
							slug="fantasy-tv-cabinet"
							index={1}
							alt="Fantasy TV Cabinet"
							className="w-full h-full rounded-lg border border-gray-200"
						/>
					</div>
					<p class="text-sm text-gray-500">Slug: fantasy-tv-cabinet</p>
					<p class="text-xs text-green-600">✅ Has real images</p>
				</div>

				<!-- LED Gola -->
				<div class="space-y-2">
					<h3 class="font-medium text-gray-700">LED Gola</h3>
					<div class="w-full h-64">
						<DynamicImage
							slug="led-gola"
							index={1}
							alt="LED Gola"
							className="w-full h-full rounded-lg border border-gray-200"
						/>
					</div>
					<p class="text-sm text-gray-500">Slug: led-gola</p>
					<p class="text-xs text-green-600">✅ Has real images</p>
				</div>

				<!-- Tandem Box -->
				<div class="space-y-2">
					<h3 class="font-medium text-gray-700">Tandem Box</h3>
					<div class="w-full h-64">
						<DynamicImage
							slug="tandem-box"
							index={1}
							alt="Tandem Box"
							className="w-full h-full rounded-lg border border-gray-200"
						/>
					</div>
					<p class="text-sm text-gray-500">Slug: tandem-box</p>
					<p class="text-xs text-green-600">✅ Has real images</p>
				</div>

				<!-- Non-existent Product (Fallback Test) -->
				<div class="space-y-2">
					<h3 class="font-medium text-gray-700">Non-existent Product</h3>
					<div class="w-full h-64">
						<DynamicImage
							slug="non-existent-product"
							index={1}
							alt="Non-existent Product"
							className="w-full h-full rounded-lg border border-gray-200"
						/>
					</div>
					<p class="text-sm text-gray-500">Slug: non-existent-product</p>
					<p class="text-xs text-orange-600">⚠️ Should show placeholder</p>
				</div>

				<!-- Empty Slug Test -->
				<div class="space-y-2">
					<h3 class="font-medium text-gray-700">Empty Slug</h3>
					<div class="w-full h-64">
						<DynamicImage
							slug=""
							index={1}
							alt="Empty Slug"
							className="w-full h-full rounded-lg border border-gray-200"
						/>
					</div>
					<p class="text-sm text-gray-500">Empty slug</p>
					<p class="text-xs text-orange-600">⚠️ Should show placeholder</p>
				</div>

				<!-- Multiple Index Test -->
				<div class="space-y-2">
					<h3 class="font-medium text-gray-700">Fantasy TV Cabinet (Index 2)</h3>
					<div class="w-full h-64">
						<DynamicImage
							slug="fantasy-tv-cabinet"
							index={2}
							alt="Fantasy TV Cabinet Index 2"
							className="w-full h-full rounded-lg border border-gray-200"
						/>
					</div>
					<p class="text-sm text-gray-500">Slug: fantasy-tv-cabinet, Index: 2</p>
					<p class="text-xs text-orange-600">⚠️ Index 2 doesn't exist, should show placeholder</p>
				</div>
			</div>
		</div>
		
		<!-- Responsive Behavior Info -->
		<div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
			<h2 class="text-xl font-semibold text-blue-800 mb-4">Responsive Behavior</h2>
			<div class="space-y-2 text-blue-700">
				<p><strong>Mobile (&lt; 768px):</strong> Shows 4:5 aspect ratio images (4x5_product-slug_N.ext)</p>
				<p><strong>Desktop (≥ 768px):</strong> Shows 16:9 aspect ratio images (16x9_product-slug_N.ext)</p>
				<p><strong>Fallback:</strong> If the preferred aspect ratio is not found, tries the other aspect ratio</p>
				<p><strong>Final Fallback:</strong> Shows placeholder image if no product images are found</p>
			</div>
		</div>
		
		<!-- Current Viewport Info -->
		<div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
			<h2 class="text-xl font-semibold text-gray-800 mb-4">Current Viewport</h2>
			<div class="text-gray-600">
				<p>Resize your browser window to test responsive behavior:</p>
				<ul class="list-disc list-inside mt-2 space-y-1">
					<li>Width &lt; 768px: Mobile layout (4:5 images)</li>
					<li>Width ≥ 768px: Desktop layout (16:9 images)</li>
				</ul>
			</div>
		</div>
	</div>
</div>

<style>
	/* Additional styles for better visualization */
	.container {
		max-width: 1200px;
	}
</style>
