# =============================================================================
# WRANGLER CONFIGURATION - ASTRO WORKS E-COMMERCE FRONTEND
# =============================================================================
# Configuration file for Cloudflare Workers and environment variable management

name = "astro-works-frontend"
main = ".svelte-kit/cloudflare/index.js"
compatibility_date = "2024-06-10"
compatibility_flags = ["nodejs_compat"]
workers_dev = false

# Static assets configuration (Workers Static Assets)
assets = { directory = ".svelte-kit/cloudflare/static", binding = "ASSETS" }

# Custom domain configuration will be handled via Cloudflare Dashboard
# routes = [
#   { pattern = "astrokabinet.id", custom_domain = true },
#   { pattern = "www.astrokabinet.id", custom_domain = true }
# ]

# =============================================================================
# ENVIRONMENT CONFIGURATIONS
# =============================================================================

# Development Environment
[env.development]
name = "astro-works-frontend-dev"

# Staging Environment
[env.staging]
name = "astro-works-frontend-staging"

# Production Environment
[env.production]
name = "astro-works-frontend-prod"
routes = ["astrokabinet.id/*"]

# =============================================================================
# DEVELOPMENT ENVIRONMENT VARIABLES (LOCAL DEVELOPMENT ONLY)
# =============================================================================
[env.development.vars]
PUBLIC_API_BASE_URL = "http://localhost:7998"
PUBLIC_API_VERSION = "v1"
PUBLIC_API_URL = "http://localhost:7998/api/v1"
PUBLIC_STATIC_URL = "http://localhost:7998/static"
PUBLIC_UPLOADS_URL = "http://localhost:7998/static/uploads"
PUBLIC_IMAGES_URL = "http://localhost:7998/static/images"
PUBLIC_ENVIRONMENT = "development"
PUBLIC_APP_NAME = "Astro Works"
PUBLIC_APP_VERSION = "1.0.0"
PUBLIC_WHATSAPP_PHONE_NUMBER = "***********"
PUBLIC_WHATSAPP_BASE_URL = "https://wa.me/"
PUBLIC_COMPANY_NAME = "Astro Works Indonesia"
PUBLIC_COMPANY_EMAIL = "<EMAIL>"
PUBLIC_COMPANY_ADDRESS = "Jakarta, Indonesia"
PUBLIC_BANK_NAME = "BCA"
PUBLIC_BANK_ACCOUNT_NUMBER = "**********"
PUBLIC_BANK_ACCOUNT_NAME = "Astro Works Indonesia PT"
PUBLIC_IMAGE_QUALITY = "85"
PUBLIC_IMAGE_LAZY_LOADING = "true"
PUBLIC_IMAGE_PLACEHOLDER = "true"
PUBLIC_CART_STORAGE_KEY = "astro_cart"
PUBLIC_CART_EXPIRY_HOURS = "24"
PUBLIC_PRODUCTS_PER_PAGE = "12"
PUBLIC_CATEGORIES_PER_PAGE = "20"
PUBLIC_ENABLE_ANALYTICS = "false"
PUBLIC_ENABLE_CHAT = "true"
PUBLIC_ENABLE_REVIEWS = "false"
PUBLIC_ENABLE_WISHLIST = "false"
PUBLIC_DEBUG_MODE = "true"
PUBLIC_SHOW_CONSOLE_LOGS = "true"
PUBLIC_MOCK_API = "false"
PUBLIC_ENABLE_PWA = "false"
PUBLIC_ENABLE_SERVICE_WORKER = "false"

# =============================================================================
# STAGING ENVIRONMENT VARIABLES
# =============================================================================
[env.staging.vars]
PUBLIC_API_BASE_URL = "https://staging-api.astrokabinet.id"
PUBLIC_API_VERSION = "v1"
PUBLIC_API_URL = "https://staging-api.astrokabinet.id/api/v1"
PUBLIC_STATIC_URL = "https://staging-api.astrokabinet.id/static"
PUBLIC_UPLOADS_URL = "https://staging-api.astrokabinet.id/static/uploads"
PUBLIC_IMAGES_URL = "https://staging-api.astrokabinet.id/static/images"
PUBLIC_APP_NAME = "Astro Works (Staging)"
PUBLIC_APP_VERSION = "1.0.0"
PUBLIC_ENVIRONMENT = "staging"
PUBLIC_WHATSAPP_PHONE_NUMBER = "***********"
PUBLIC_WHATSAPP_BASE_URL = "https://wa.me/"
PUBLIC_COMPANY_NAME = "Astro Works Indonesia"
PUBLIC_COMPANY_EMAIL = "<EMAIL>"
PUBLIC_COMPANY_ADDRESS = "Jakarta, Indonesia"
PUBLIC_BANK_NAME = "BCA"
PUBLIC_BANK_ACCOUNT_NUMBER = "**********"
PUBLIC_BANK_ACCOUNT_NAME = "Astro Works Indonesia PT"
PUBLIC_IMAGE_QUALITY = "85"
PUBLIC_IMAGE_LAZY_LOADING = "true"
PUBLIC_IMAGE_PLACEHOLDER = "true"
PUBLIC_CART_STORAGE_KEY = "astro_cart"
PUBLIC_CART_EXPIRY_HOURS = "24"
PUBLIC_PRODUCTS_PER_PAGE = "12"
PUBLIC_CATEGORIES_PER_PAGE = "20"
PUBLIC_ENABLE_ANALYTICS = "true"
PUBLIC_ENABLE_CHAT = "true"
PUBLIC_ENABLE_REVIEWS = "true"
PUBLIC_ENABLE_WISHLIST = "true"
PUBLIC_DEBUG_MODE = "false"
PUBLIC_SHOW_CONSOLE_LOGS = "false"
PUBLIC_MOCK_API = "false"
PUBLIC_ENABLE_PWA = "true"
PUBLIC_ENABLE_SERVICE_WORKER = "true"

# =============================================================================
# PRODUCTION ENVIRONMENT VARIABLES
# =============================================================================

[env.production.vars]
PUBLIC_API_BASE_URL = "https://furapi.astrokabinet.id"
PUBLIC_API_VERSION = "v1"
PUBLIC_API_URL = "https://furapi.astrokabinet.id/api/v1"
PUBLIC_STATIC_URL = "https://furapi.astrokabinet.id/static"
PUBLIC_UPLOADS_URL = "https://furapi.astrokabinet.id/static/uploads"
PUBLIC_IMAGES_URL = "https://furapi.astrokabinet.id/static/images"
PUBLIC_APP_NAME = "Astro Works"
PUBLIC_APP_VERSION = "1.0.0"
PUBLIC_ENVIRONMENT = "production"
PUBLIC_WHATSAPP_PHONE_NUMBER = "***********"
PUBLIC_WHATSAPP_BASE_URL = "https://wa.me/"
PUBLIC_COMPANY_NAME = "Astro Works Indonesia"
PUBLIC_COMPANY_EMAIL = "<EMAIL>"
PUBLIC_COMPANY_ADDRESS = "Jakarta, Indonesia"
PUBLIC_BANK_NAME = "BCA"
PUBLIC_BANK_ACCOUNT_NUMBER = "**********"
PUBLIC_BANK_ACCOUNT_NAME = "Astro Works Indonesia PT"
PUBLIC_IMAGE_QUALITY = "90"
PUBLIC_IMAGE_LAZY_LOADING = "true"
PUBLIC_IMAGE_PLACEHOLDER = "true"
PUBLIC_CART_STORAGE_KEY = "astro_cart"
PUBLIC_CART_EXPIRY_HOURS = "24"
PUBLIC_PRODUCTS_PER_PAGE = "12"
PUBLIC_CATEGORIES_PER_PAGE = "20"
PUBLIC_ENABLE_ANALYTICS = "true"
PUBLIC_ENABLE_CHAT = "true"
PUBLIC_ENABLE_REVIEWS = "true"
PUBLIC_ENABLE_WISHLIST = "true"
PUBLIC_DEBUG_MODE = "false"
PUBLIC_SHOW_CONSOLE_LOGS = "false"
PUBLIC_MOCK_API = "false"
PUBLIC_ENABLE_PWA = "true"
PUBLIC_ENABLE_SERVICE_WORKER = "true"

# =============================================================================
# BUILD CONFIGURATION
# =============================================================================
[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
[dev]
ip = "localhost"
port = 8787
local_protocol = "http"
