#!/bin/bash

# Astro Works - Run Both Servers
# ==============================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🚀 Starting Astro Works Servers${NC}"
echo -e "${CYAN}===============================${NC}"
echo ""

# Function to cleanup background processes
cleanup() {
    echo -e "\n${YELLOW}[INFO]${NC} Stopping servers..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    wait $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} Servers stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Check if bun is available
if command -v bun >/dev/null 2>&1; then
    PACKAGE_MANAGER="bun"
else
    PACKAGE_MANAGER="npm"
fi

echo -e "${BLUE}[INFO]${NC} Using $PACKAGE_MANAGER for frontend"
echo ""

# Start backend in background
echo -e "${PURPLE}[BACKEND]${NC} Starting Rust backend on port 7998..."
cd be_astro
PORT=7998 cargo run --bin be_astro &
BACKEND_PID=$!
cd ..

# Give backend time to start
sleep 3

# Start frontend in background
echo -e "${PURPLE}[FRONTEND]${NC} Starting Svelte frontend with $PACKAGE_MANAGER..."
cd fe_astro
$PACKAGE_MANAGER run dev &
FRONTEND_PID=$!
cd ..

echo ""
echo -e "${GREEN}[SUCCESS]${NC} Both servers are starting..."
echo ""
echo -e "${CYAN}URLs:${NC}"
echo -e "  🌐 Frontend: ${GREEN}http://localhost:5173${NC}"
echo -e "  🔗 Backend API: ${GREEN}http://localhost:7998/api/v1${NC}"
echo -e "  ❤️  Health Check: ${GREEN}http://localhost:7998/health${NC}"
echo ""
echo -e "${YELLOW}[INFO]${NC} Press Ctrl+C to stop both servers"
echo ""

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID
