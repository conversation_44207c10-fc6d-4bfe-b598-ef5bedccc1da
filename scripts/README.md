# Scripts Directory

This directory contains all shell scripts organized by functionality for the Astro Works E-commerce Platform.

## Directory Structure

```
scripts/
├── deploy/             # Docker Compose deployment configurations
├── deployment/         # Deployment and infrastructure scripts
├── development/        # Development environment and tools
├── image-management/   # Image processing and management
├── ssl/               # SSL/HTTPS and security scripts
├── testing/           # Testing and validation scripts
└── README.md          # This file
```

## 🐳 Deploy Configurations (`deploy/`)

Docker Compose files and environment configurations for deployment.

| File | Description |
|------|-------------|
| `docker-compose.yml` | Base services (postgres, redis, backend) |
| `docker-compose.prod.yml` | Production overrides (traefik, SSL, etc.) |
| `.env.production` | Production environment variables |

## 📁 Deployment Scripts (`deployment/`)

Scripts for deploying and managing the application in production environments.

| Script | Description |
|--------|-------------|
| `deploy-vps.sh` | Deploy backend to VPS using Docker context |
| `deploy.sh` | General deployment script |
| `fast-build-vps.sh` | Optimized fast deployment to VPS |
| `health-check.sh` | Health check monitoring |
| `monitor-vps.sh` | VPS monitoring and status checks |
| `monitor.sh` | General monitoring script |
| `setup-docker-context.sh` | Setup Docker context for VPS |
| `setup.sh` | Initial project setup |

## 🛠️ Development Scripts (`development/`)

Scripts for development environment setup and maintenance.

| Script | Description |
|--------|-------------|
| `add-domains.sh` | Add custom domains to hosts file |
| `fix_sqlx_queries.sh` | Fix SQLx query compilation issues |
| `optimize-rust-build.sh` | Analyze and optimize Rust builds |
| `setup-domain-dev.sh` | Setup development domains |
| `setup-domains.sh` | Configure local domains |
| `setup-env.sh` | Interactive environment setup |
| `validate-env.sh` | Validate environment configuration |
| `validate-setup.sh` | Validate complete setup |

## 🖼️ Image Management Scripts (`image-management/`)

Scripts for handling product images and media assets.

| Script | Description |
|--------|-------------|
| `create-missing-images.sh` | Generate missing product images |
| `download_additional_items_images.sh` | Download accessory images |
| `download_furniture_images.sh` | Download furniture product images |
| `download_furniture_images.py` | Python script for furniture image downloads |
| `download_images.sh` | General image download script |
| `download_product_images.py` | Python script for product image downloads |
| `generate-product-images.sh` | Generate product image variants |
| `verify-product-images.sh` | Verify image integrity and presence |

## 🔒 SSL/Security Scripts (`ssl/`)

Scripts for SSL certificates and security configuration.

| Script | Description |
|--------|-------------|
| `generate-ssl.sh` | Generate SSL certificates |
| `generate-traefik-auth.sh` | Generate Traefik authentication |
| `setup-local-https.sh` | Setup local HTTPS development |
| `simple-https-dev.sh` | Simple HTTPS development setup |
| `start-https-dev.sh` | Start HTTPS development server |

## 🧪 Testing Scripts (`testing/`)

Scripts for testing and quality assurance.

| Script | Description |
|--------|-------------|
| `performance_test.sh` | Performance testing and benchmarks |
| `test_api.sh` | API endpoint testing |

## Usage

All scripts can be executed directly or through the Makefile targets:

```bash
# Direct execution
./scripts/deployment/deploy-vps.sh

# Through Makefile (recommended)
make deploy-vps
```

## Permissions

Ensure scripts have execute permissions:

```bash
chmod +x scripts/**/*.sh
```

## Frontend Scripts

Frontend-specific scripts remain in `fe_astro/scripts/` for Wrangler and Svelte-related functionality:

- `setup-custom-domain.sh` - Custom domain setup for Cloudflare
- `sync-env-vars.cjs` - Environment variable synchronization
- `validate-env-vars.cjs` - Environment validation
- `wrangler-env-manager.cjs` - Wrangler environment management

## Contributing

When adding new scripts:

1. Place them in the appropriate category directory
2. Update this README
3. Add corresponding Makefile targets if needed
4. Ensure proper permissions and documentation
