#!/bin/bash

# ===========================================
# ASTRO WORKS INDONESIA - BACKUP SCRIPT
# ===========================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[BACKUP]${NC} $1"
}

# Default values
BACKUP_TYPE="manual"
RETENTION_DAYS=7

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --type)
            BACKUP_TYPE="$2"
            shift 2
            ;;
        --retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [--type manual|auto] [--retention DAYS]"
            echo ""
            echo "Options:"
            echo "  --type      Backup type (manual or auto)"
            echo "  --retention Number of days to retain backups (default: 7)"
            echo "  --help      Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "💾 Astro Works Indonesia - Database Backup"
echo "=========================================="

# Check if .env exists and source it
if [ ! -f .env ]; then
    print_error ".env file not found"
    exit 1
fi

source .env

# Validate required environment variables
if [ -z "$POSTGRES_PASSWORD" ]; then
    print_error "POSTGRES_PASSWORD not set in .env file"
    exit 1
fi

print_header "Starting $BACKUP_TYPE backup"

# Create backup directory if it doesn't exist
mkdir -p backups

# Generate backup filename
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="backups/backup_${BACKUP_TYPE}_${TIMESTAMP}.sql"

print_status "Creating backup: $BACKUP_FILE"

# Check if database is accessible
if ! docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U astro_user -d astro_ecommerce > /dev/null 2>&1; then
    print_error "Database is not accessible"
    exit 1
fi

# Create database backup
print_status "Dumping database..."

if docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U astro_user -d astro_ecommerce > "$BACKUP_FILE"; then
    print_status "Database backup completed successfully"
    
    # Get backup file size
    BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
    print_status "Backup size: $BACKUP_SIZE"
    
    # Verify backup file is not empty
    if [ ! -s "$BACKUP_FILE" ]; then
        print_error "Backup file is empty"
        rm -f "$BACKUP_FILE"
        exit 1
    fi
    
    # Test backup integrity
    print_status "Verifying backup integrity..."
    if head -n 10 "$BACKUP_FILE" | grep -q "PostgreSQL database dump"; then
        print_status "Backup integrity verified"
    else
        print_warning "Backup integrity check failed"
    fi
    
else
    print_error "Database backup failed"
    rm -f "$BACKUP_FILE"
    exit 1
fi

# Backup static files
print_header "Backing up static files"

STATIC_BACKUP="backups/static_${BACKUP_TYPE}_${TIMESTAMP}.tar.gz"

if [ -d "be_astro/static/uploads" ]; then
    print_status "Creating static files backup: $STATIC_BACKUP"
    
    if tar -czf "$STATIC_BACKUP" -C be_astro/static uploads/ 2>/dev/null; then
        STATIC_SIZE=$(du -h "$STATIC_BACKUP" | cut -f1)
        print_status "Static files backup completed: $STATIC_SIZE"
    else
        print_warning "Static files backup failed"
        rm -f "$STATIC_BACKUP"
    fi
else
    print_warning "Static uploads directory not found"
fi

# Backup configuration files
print_header "Backing up configuration"

CONFIG_BACKUP="backups/config_${BACKUP_TYPE}_${TIMESTAMP}.tar.gz"

print_status "Creating configuration backup: $CONFIG_BACKUP"

# Create temporary directory for config files
TEMP_CONFIG_DIR=$(mktemp -d)

# Copy important config files (excluding sensitive .env)
cp docker-compose.prod.yml "$TEMP_CONFIG_DIR/" 2>/dev/null || true
cp -r monitoring/ "$TEMP_CONFIG_DIR/" 2>/dev/null || true
cp -r scripts/ "$TEMP_CONFIG_DIR/" 2>/dev/null || true

# Create config backup
if tar -czf "$CONFIG_BACKUP" -C "$TEMP_CONFIG_DIR" . 2>/dev/null; then
    CONFIG_SIZE=$(du -h "$CONFIG_BACKUP" | cut -f1)
    print_status "Configuration backup completed: $CONFIG_SIZE"
else
    print_warning "Configuration backup failed"
    rm -f "$CONFIG_BACKUP"
fi

# Cleanup temp directory
rm -rf "$TEMP_CONFIG_DIR"

# Cleanup old backups
print_header "Cleaning up old backups"

print_status "Removing backups older than $RETENTION_DAYS days"

# Remove old database backups
find backups/ -name "backup_*.sql" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true

# Remove old static backups
find backups/ -name "static_*.tar.gz" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true

# Remove old config backups
find backups/ -name "config_*.tar.gz" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true

# Count remaining backups
DB_BACKUPS=$(ls -1 backups/backup_*.sql 2>/dev/null | wc -l)
STATIC_BACKUPS=$(ls -1 backups/static_*.tar.gz 2>/dev/null | wc -l)
CONFIG_BACKUPS=$(ls -1 backups/config_*.tar.gz 2>/dev/null | wc -l)

print_status "Remaining backups: $DB_BACKUPS database, $STATIC_BACKUPS static, $CONFIG_BACKUPS config"

# Show backup directory size
BACKUP_DIR_SIZE=$(du -sh backups/ | cut -f1)
print_status "Total backup directory size: $BACKUP_DIR_SIZE"

# Create backup summary
print_header "Backup Summary"

echo "Backup completed at: $(date)"
echo "Backup type: $BACKUP_TYPE"
echo "Database backup: $BACKUP_FILE ($BACKUP_SIZE)"
[ -f "$STATIC_BACKUP" ] && echo "Static backup: $STATIC_BACKUP ($STATIC_SIZE)"
[ -f "$CONFIG_BACKUP" ] && echo "Config backup: $CONFIG_BACKUP ($CONFIG_SIZE)"
echo "Retention policy: $RETENTION_DAYS days"
echo "Total backups: $DB_BACKUPS database, $STATIC_BACKUPS static, $CONFIG_BACKUPS config"

# Log backup to system log if available
if command -v logger &> /dev/null; then
    logger "Astro Works backup completed: $BACKUP_FILE"
fi

echo ""
print_status "✅ Backup process completed successfully!"

# Optional: Send notification (uncomment if you have notification system)
# curl -X POST "your-webhook-url" -d "Astro Works backup completed: $BACKUP_FILE" || true
