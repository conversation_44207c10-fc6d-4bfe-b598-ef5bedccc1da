#!/bin/bash

# Build locally and deploy binary to avoid VPS compilation overload
# This script builds the Rust binary locally and creates a lightweight Docker image

set -e

echo "🏗️  Building Astro Works locally and deploying binary..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to build locally
build_local() {
    echo -e "${BLUE}🔨 Building Rust binary locally...${NC}"
    
    cd be_astro
    
    # Clean previous builds
    cargo clean
    
    # Build release binary optimized for 1GB VPS deployment
    echo -e "${YELLOW}Building with single job for 1 CPU target...${NC}"
    CARGO_BUILD_JOBS=1 RUSTFLAGS="-C opt-level=s -C target-cpu=generic" cargo build --release
    
    # Check if binary was created
    if [ ! -f "target/release/be_astro" ]; then
        echo -e "${RED}❌ Build failed - binary not found${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Local build completed${NC}"
    cd ..
}

# Function to create lightweight Dockerfile
create_deploy_dockerfile() {
    echo -e "${BLUE}📝 Creating deployment Dockerfile...${NC}"
    
    cat > be_astro/Dockerfile.deploy << 'EOF'
# Lightweight deployment image - no compilation needed
FROM debian:bookworm-slim

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    libpq5 \
    libssl3 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy pre-built binary
COPY target/release/be_astro /app/be_astro

# Create user
RUN useradd -r -s /bin/false astro \
    && chown -R astro:astro /app \
    && chmod +x /app/be_astro

USER astro

EXPOSE 7998

HEALTHCHECK --interval=30s --timeout=5s \
  CMD curl -f http://localhost:7998/health || exit 1

CMD ["./be_astro"]
EOF

    echo -e "${GREEN}✅ Deployment Dockerfile created${NC}"
}

# Function to create deployment docker-compose
create_deploy_compose() {
    echo -e "${BLUE}📝 Creating deployment docker-compose...${NC}"
    
    cat > scripts/deploy/docker-compose.binary.yml << 'COMPOSE_EOF'
version: '3.8'

services:
  backend:
    build:
      context: ../../be_astro
      dockerfile: Dockerfile.deploy
    container_name: astro_backend_prod
    environment:
      DATABASE_URL: postgres://astro_user:${POSTGRES_PASSWORD}@postgres:5432/astro_ecommerce
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      HOST: 0.0.0.0
      PORT: 7998
      RUST_LOG: ${RUST_LOG:-info}
      CORS_ORIGIN: ${CORS_ORIGIN:-https://astrokabinet.id,https://www.astrokabinet.id}
      WHATSAPP_PHONE_NUMBER: ${WHATSAPP_PHONE_NUMBER:-***********}
      ADMIN_DEFAULT_EMAIL: ${ADMIN_DEFAULT_EMAIL}
      ADMIN_DEFAULT_PASSWORD: ${ADMIN_DEFAULT_PASSWORD}
      BANK_NAME: ${BANK_NAME}
      BANK_ACCOUNT_NUMBER: ${BANK_ACCOUNT_NUMBER}
      BANK_ACCOUNT_NAME: ${BANK_ACCOUNT_NAME}
    volumes:
      - ../../be_astro/static:/app/static
    expose:
      - "7998"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7998/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(\`api.astrokabinet.id\`)"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=7998"
      - "traefik.http.routers.backend.middlewares=api-ratelimit,security-headers"
    networks:
      - traefik
      - astro_network
    # Minimal resource limits for 1GB VPS
    deploy:
      resources:
        limits:
          cpus: '0.4'        # 40% CPU for 1 CPU VPS
          memory: 384M       # 384MB for 1GB VPS
        reservations:
          cpus: '0.1'
          memory: 192M
COMPOSE_EOF

    echo -e "${GREEN}✅ Binary deployment compose created${NC}"
}

# Function to deploy binary
deploy_binary() {
    echo -e "${BLUE}🚀 Deploying pre-built binary...${NC}"
    
    # Check if Docker context exists
    if ! docker context inspect astro-vps >/dev/null 2>&1; then
        echo -e "${RED}❌ Docker context 'astro-vps' not found${NC}"
        echo -e "${YELLOW}Please set up Docker context first${NC}"
        exit 1
    fi
    
    # Use Docker context
    export DOCKER_CONTEXT=astro-vps
    
    echo -e "${BLUE}🐳 Deploying with pre-built binary...${NC}"
    
    # Deploy using binary compose file
    cd scripts/deploy
    docker-compose -f docker-compose.yml -f docker-compose.binary.yml --env-file .env.production up -d --build backend
    
    echo -e "${GREEN}✅ Binary deployment completed${NC}"
}

# Function to check deployment
check_deployment() {
    echo -e "${BLUE}🏥 Checking deployment...${NC}"
    
    # Wait for service to start
    sleep 30
    
    # Check backend health
    if curl -f -s --connect-timeout 10 https://api.astrokabinet.id/health >/dev/null; then
        echo -e "${GREEN}✅ Backend is running successfully${NC}"
    else
        echo -e "${RED}❌ Backend health check failed${NC}"
        echo -e "${YELLOW}Checking logs...${NC}"
        docker --context astro-vps logs astro_backend_prod --tail 20
        return 1
    fi
}

# Function to cleanup
cleanup() {
    echo -e "${BLUE}🧹 Cleaning up temporary files...${NC}"
    
    # Remove temporary Dockerfile
    if [ -f "be_astro/Dockerfile.deploy" ]; then
        rm be_astro/Dockerfile.deploy
    fi
    
    # Remove temporary compose file
    if [ -f "scripts/deploy/docker-compose.binary.yml" ]; then
        rm scripts/deploy/docker-compose.binary.yml
    fi
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    echo -e "${GREEN}🌟 Astro Works Binary Deployment${NC}"
    echo "=================================="
    echo -e "${YELLOW}This method builds locally and deploys binary to avoid VPS overload${NC}"
    echo ""
    
    # Build locally
    build_local
    
    # Create deployment files
    create_deploy_dockerfile
    create_deploy_compose
    
    # Deploy binary
    deploy_binary
    
    # Check deployment
    check_deployment
    
    echo -e "${GREEN}🎉 Binary deployment completed successfully!${NC}"
    echo -e "${BLUE}Frontend: https://astrokabinet.id${NC}"
    echo -e "${BLUE}Backend API: https://api.astrokabinet.id${NC}"
}

# Run main function
main "$@"
