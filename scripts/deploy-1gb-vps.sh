#!/bin/bash

# Optimized deployment script for 1GB RAM + 1 CPU VPS
# This script ensures maximum resource efficiency

set -e

echo "🔧 Deploying to 1GB VPS with maximum optimization..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if VPS has enough free resources
check_vps_resources() {
    echo -e "${BLUE}📊 Checking VPS resources (1GB RAM + 1 CPU)...${NC}"
    
    # Check via Docker context
    if ! docker context inspect astro-vps >/dev/null 2>&1; then
        echo -e "${RED}❌ Docker context 'astro-vps' not found${NC}"
        exit 1
    fi
    
    # Get VPS memory info
    VPS_MEM_INFO=$(docker --context astro-vps run --rm alpine free -m 2>/dev/null || echo "Unable to check")
    
    if [[ "$VPS_MEM_INFO" != "Unable to check" ]]; then
        VPS_TOTAL_MEM=$(echo "$VPS_MEM_INFO" | awk 'NR==2{print $2}')
        VPS_USED_MEM=$(echo "$VPS_MEM_INFO" | awk 'NR==2{print $3}')
        VPS_FREE_MEM=$(echo "$VPS_MEM_INFO" | awk 'NR==2{print $4}')
        VPS_MEM_USAGE=$(echo "scale=1; $VPS_USED_MEM * 100 / $VPS_TOTAL_MEM" | bc)
        
        echo -e "💾 VPS Memory: ${VPS_USED_MEM}MB/${VPS_TOTAL_MEM}MB (${VPS_MEM_USAGE}% used)"
        
        if (( $(echo "$VPS_MEM_USAGE > 75" | bc -l) )); then
            echo -e "${RED}⚠️  WARNING: VPS memory usage is high (${VPS_MEM_USAGE}%)${NC}"
            echo -e "${YELLOW}Recommend stopping unnecessary services first${NC}"
            read -p "Continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    else
        echo -e "${YELLOW}⚠️  Unable to check VPS resources directly${NC}"
    fi
}

# Function to free up VPS resources
cleanup_vps() {
    echo -e "${BLUE}🧹 Cleaning up VPS resources...${NC}"
    
    # Stop existing containers gracefully
    echo -e "${YELLOW}Stopping existing containers...${NC}"
    docker --context astro-vps stop $(docker --context astro-vps ps -q) 2>/dev/null || true
    
    # Remove unused containers and images
    echo -e "${YELLOW}Removing unused Docker resources...${NC}"
    docker --context astro-vps container prune -f 2>/dev/null || true
    docker --context astro-vps image prune -f 2>/dev/null || true
    
    # Wait for resources to be freed
    sleep 5
    
    echo -e "${GREEN}✅ VPS cleanup completed${NC}"
}

# Function to deploy with minimal resource usage
deploy_minimal() {
    echo -e "${BLUE}🚀 Starting minimal resource deployment...${NC}"
    
    # Change to deployment directory
    cd "$(dirname "$0")/deploy"
    
    # Use Docker context
    export DOCKER_CONTEXT=astro-vps
    
    echo -e "${BLUE}🐳 Deploying services one by one to minimize resource usage...${NC}"
    
    # Deploy PostgreSQL first
    echo -e "${YELLOW}1/3 Deploying PostgreSQL...${NC}"
    docker-compose -f docker-compose.prod.yml up -d postgres
    
    # Wait for PostgreSQL to be ready
    echo -e "${YELLOW}Waiting for PostgreSQL to be ready...${NC}"
    sleep 30
    
    # Deploy Redis
    echo -e "${YELLOW}2/3 Deploying Redis...${NC}"
    docker-compose -f docker-compose.prod.yml up -d redis
    
    # Wait for Redis to be ready
    echo -e "${YELLOW}Waiting for Redis to be ready...${NC}"
    sleep 15
    
    # Deploy Backend last
    echo -e "${YELLOW}3/3 Deploying Backend...${NC}"
    docker-compose -f docker-compose.prod.yml up -d backend
    
    # Deploy Traefik
    echo -e "${YELLOW}4/4 Deploying Traefik...${NC}"
    docker-compose -f docker-compose.prod.yml up -d traefik
    
    echo -e "${GREEN}✅ Minimal deployment completed${NC}"
}

# Function to monitor deployment
monitor_deployment() {
    echo -e "${BLUE}📈 Monitoring deployment progress...${NC}"
    
    # Monitor for 2 minutes
    for i in {1..12}; do
        echo -e "${BLUE}Check $i/12...${NC}"
        
        # Check container status
        CONTAINERS=$(docker --context astro-vps ps --format "table {{.Names}}\t{{.Status}}" | grep astro || true)
        echo "$CONTAINERS"
        
        # Check memory usage if possible
        MEM_INFO=$(docker --context astro-vps run --rm alpine free -m 2>/dev/null | awk 'NR==2{printf "Memory: %dMB/%dMB (%.1f%%)", $3, $2, $3*100/$2}' || echo "Memory: Unable to check")
        echo "$MEM_INFO"
        
        echo "---"
        sleep 10
    done
}

# Function to verify deployment
verify_deployment() {
    echo -e "${BLUE}🏥 Verifying deployment...${NC}"
    
    # Wait for services to fully start
    sleep 30
    
    # Check backend health
    echo -e "${YELLOW}Checking backend health...${NC}"
    if curl -f -s --connect-timeout 15 --max-time 30 https://api.astrokabinet.id/health >/dev/null; then
        echo -e "${GREEN}✅ Backend is healthy${NC}"
    else
        echo -e "${RED}❌ Backend health check failed${NC}"
        echo -e "${YELLOW}Checking backend logs...${NC}"
        docker --context astro-vps logs astro_backend_prod --tail 20
        return 1
    fi
    
    # Check database connection
    echo -e "${YELLOW}Checking database connection...${NC}"
    if docker --context astro-vps exec astro_postgres_prod pg_isready -U astro_user >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Database is healthy${NC}"
    else
        echo -e "${RED}❌ Database health check failed${NC}"
        docker --context astro-vps logs astro_postgres_prod --tail 10
        return 1
    fi
    
    # Check Redis connection
    echo -e "${YELLOW}Checking Redis connection...${NC}"
    if docker --context astro-vps exec astro_redis_prod redis-cli ping >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis is healthy${NC}"
    else
        echo -e "${RED}❌ Redis health check failed${NC}"
        docker --context astro-vps logs astro_redis_prod --tail 10
        return 1
    fi
    
    echo -e "${GREEN}✅ All services are healthy${NC}"
}

# Function to show resource usage summary
show_resource_summary() {
    echo -e "${BLUE}📊 Final Resource Usage Summary${NC}"
    echo "=================================="
    
    # Show container resource usage
    echo -e "${YELLOW}Container Resource Usage:${NC}"
    docker --context astro-vps stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" 2>/dev/null || echo "Unable to get container stats"
    
    echo ""
    echo -e "${YELLOW}Expected Resource Allocation:${NC}"
    echo "PostgreSQL: 30% CPU, 256MB RAM"
    echo "Redis:      15% CPU, 128MB RAM"
    echo "Backend:    50% CPU, 512MB RAM"
    echo "System:     5%  CPU, 104MB RAM"
    echo "--------------------------------"
    echo "Total:      100% CPU, 1000MB RAM"
}

# Main execution
main() {
    echo -e "${GREEN}🌟 Astro Works 1GB VPS Optimized Deployment${NC}"
    echo "=============================================="
    echo -e "${YELLOW}Optimized for: 1GB RAM + 1 CPU VPS${NC}"
    echo ""
    
    # Check VPS resources
    check_vps_resources
    
    # Cleanup VPS
    cleanup_vps
    
    # Deploy with minimal resources
    deploy_minimal
    
    # Monitor deployment
    monitor_deployment
    
    # Verify deployment
    if verify_deployment; then
        echo -e "${GREEN}🎉 Deployment successful!${NC}"
        show_resource_summary
        
        echo ""
        echo -e "${GREEN}🌐 Your application is now live:${NC}"
        echo -e "${BLUE}Frontend: https://astrokabinet.id${NC}"
        echo -e "${BLUE}Backend API: https://api.astrokabinet.id${NC}"
    else
        echo -e "${RED}❌ Deployment verification failed${NC}"
        echo -e "${YELLOW}Check logs and try again${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
