#!/bin/bash

# =============================================================================
# Frontend Deployment Script
# =============================================================================
# This script deploys the Svelte frontend to Cloudflare Workers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/fe_astro"

# Functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if we're in the right directory
    if [[ ! -d "$FRONTEND_DIR" ]]; then
        print_error "Frontend directory not found: $FRONTEND_DIR"
        exit 1
    fi
    
    # Check if wrangler is installed
    if ! command -v wrangler &> /dev/null; then
        print_error "Wrangler CLI is not installed"
        print_info "Install with: npm install -g wrangler"
        exit 1
    fi
    
    # Check if logged in to Cloudflare
    if ! wrangler whoami &> /dev/null; then
        print_error "Not logged in to Cloudflare"
        print_info "Login with: wrangler login"
        exit 1
    fi
    
    print_success "Prerequisites check completed"
}

# Setup environment
setup_environment() {
    print_header "Setting Up Environment"
    
    cd "$FRONTEND_DIR"
    
    # Copy production environment
    print_info "Setting up production environment..."
    cp .env.production .env.local
    
    # Install dependencies
    print_info "Installing dependencies..."
    npm install
    
    print_success "Environment setup completed"
}

# Build frontend
build_frontend() {
    print_header "Building Frontend"
    
    cd "$FRONTEND_DIR"
    
    print_info "Building Svelte application..."
    npm run build
    
    print_success "Frontend build completed"
}

# Deploy to Cloudflare Workers
deploy_to_cloudflare() {
    print_header "Deploying to Cloudflare Workers"
    
    cd "$FRONTEND_DIR"
    
    print_info "Deploying to production environment..."
    wrangler deploy --env production
    
    print_success "Deployment to Cloudflare completed"
}

# Test deployment
test_deployment() {
    print_header "Testing Deployment"
    
    print_info "Testing frontend URL..."
    if curl -f -s "https://astrokabinet.id" > /dev/null; then
        print_success "Frontend is accessible"
    else
        print_warning "Frontend may still be deploying"
    fi
    
    print_info "Testing API connectivity from frontend..."
    # This would need to be tested manually in browser
    print_info "Please test login functionality in browser"
}

# Show deployment info
show_deployment_info() {
    print_header "Deployment Information"
    
    echo -e "${CYAN}Frontend URL:${NC} https://astrokabinet.id"
    echo -e "${CYAN}API URL:${NC} https://api.astrokabinet.id"
    echo -e "${CYAN}Environment:${NC} Production"
    echo ""
    echo -e "${YELLOW}Test URLs:${NC}"
    echo "  Frontend: https://astrokabinet.id"
    echo "  API Health: https://api.astrokabinet.id/health"
    echo "  Login Test: https://astrokabinet.id/manajemen"
    echo ""
    echo -e "${YELLOW}Management Commands:${NC}"
    echo "  View logs: wrangler tail --env production"
    echo "  Redeploy: $0"
    echo "  Rollback: wrangler rollback --env production"
}

# Main deployment function
main() {
    print_header "Astro Works Frontend Deployment"
    print_info "Deploying to: https://astrokabinet.id"
    
    check_prerequisites
    setup_environment
    build_frontend
    deploy_to_cloudflare
    test_deployment
    show_deployment_info
    
    print_success "Frontend deployment completed successfully!"
}

# Run main function
main "$@"
