#!/bin/bash

# =============================================================================
# VPS Deployment Script for Astro Works Backend API
# =============================================================================
# This script deploys the backend API to VPS using Docker context
# Port: 7998 | Domain: api.astrokabinet.id

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SECURE_ENV_FILE="$PROJECT_ROOT/.env.vps.secure"
VPS_ENV_FILE="$PROJECT_ROOT/.env.vps"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.vps.yml"
NGINX_CONFIG_DIR="$PROJECT_ROOT/nginx-vps"

# Load secure VPS configuration
if [[ -f "$SECURE_ENV_FILE" ]]; then
    source "$SECURE_ENV_FILE"
else
    echo -e "${RED}[ERROR]${NC} Secure environment file not found: $SECURE_ENV_FILE"
    echo "Please create .env.vps.secure with VPS connection details"
    exit 1
fi

# Functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available"
        exit 1
    fi
    
    # Check if SSH key exists
    if [[ ! -f "${SSH_KEY_PATH/#\~/$HOME}" ]]; then
        print_warning "SSH key not found at $SSH_KEY_PATH"
        print_info "Make sure you can SSH to the VPS"
    fi
    
    print_success "Prerequisites check completed"
}

# Setup Docker context
setup_docker_context() {
    print_header "Setting Up Docker Context"
    
    # Remove existing context if it exists
    if docker context ls | grep -q "$DOCKER_CONTEXT_NAME"; then
        print_info "Removing existing Docker context: $DOCKER_CONTEXT_NAME"
        docker context rm "$DOCKER_CONTEXT_NAME" || true
    fi
    
    # Create new Docker context
    print_info "Creating Docker context: $DOCKER_CONTEXT_NAME"
    docker context create "$DOCKER_CONTEXT_NAME" \
        --docker "host=ssh://$VPS_HOST"
    
    print_success "Docker context created successfully"
}

# Test VPS connection
test_vps_connection() {
    print_header "Testing VPS Connection"
    
    print_info "Testing SSH connection to $VPS_HOST..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$VPS_HOST" "echo 'SSH connection successful'"; then
        print_success "SSH connection to VPS successful"
    else
        print_error "Cannot connect to VPS via SSH"
        print_info "Please ensure:"
        print_info "1. SSH key is properly configured"
        print_info "2. VPS is accessible"
        print_info "3. User has Docker permissions"
        exit 1
    fi
    
    print_info "Testing Docker context..."
    if docker --context "$DOCKER_CONTEXT_NAME" version &> /dev/null; then
        print_success "Docker context connection successful"
    else
        print_error "Cannot connect to Docker on VPS"
        exit 1
    fi
}

# Prepare VPS environment
prepare_vps_environment() {
    print_header "Preparing VPS Environment"
    
    print_info "Creating deployment directory on VPS..."
    ssh "$VPS_HOST" "mkdir -p $VPS_DEPLOY_PATH"
    
    print_info "Copying environment files to VPS..."
    scp "$VPS_ENV_FILE" "$VPS_HOST:$VPS_DEPLOY_PATH/.env"
    
    print_info "Copying Docker Compose file to VPS..."
    scp "$COMPOSE_FILE" "$VPS_HOST:$VPS_DEPLOY_PATH/docker-compose.yml"

    print_info "Copying Nginx configuration to VPS..."
    rsync -avz --delete "$NGINX_CONFIG_DIR/" "$VPS_HOST:$VPS_DEPLOY_PATH/nginx-vps/"

    print_info "Copying backend source to VPS..."
    rsync -avz --delete "$PROJECT_ROOT/be_astro/" "$VPS_HOST:$VPS_DEPLOY_PATH/be_astro/"
    
    print_success "VPS environment prepared"
}

# Deploy services
deploy_services() {
    print_header "Deploying Services to VPS"
    
    print_info "Creating astro network..."
    ssh "$VPS_HOST" "docker network create astro_network 2>/dev/null || echo 'Network already exists'"

    print_info "Building and starting services with Nginx..."
    ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.yml --env-file .env up -d --build"
    
    print_info "Waiting for services to be healthy..."
    sleep 30
    
    print_info "Checking service health..."
    ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.yml ps"
    
    print_success "Services deployed successfully"
}

# Run database migrations
run_migrations() {
    print_header "Running Database Migrations"
    
    print_info "Waiting for database to be ready..."
    sleep 10
    
    print_info "Running migrations..."
    ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.yml exec -T backend /app/be_astro migrate" || {
        print_warning "Migration command failed, trying alternative method..."
        ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.yml exec -T backend sh -c 'cd /app && ./be_astro migrate'" || {
            print_warning "Migrations may need to be run manually"
        }
    }
    
    print_success "Database migrations completed"
}

# Test deployment
test_deployment() {
    print_header "Testing Deployment"
    
    print_info "Testing API health endpoint (internal)..."
    if ssh "$VPS_HOST" "curl -f http://localhost:7998/health" &> /dev/null; then
        print_success "Internal API health check passed"
    else
        print_warning "Internal API health check failed - service may still be starting"
    fi

    print_info "Testing API via domain (external)..."
    if curl -f "https://furapi.astrokabinet.id/health" &> /dev/null; then
        print_success "External API health check passed"
    else
        print_warning "External API health check failed - DNS/SSL may still be propagating"
    fi

    print_info "Checking service logs..."
    ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.yml logs --tail=20 backend"
}

# Show deployment info
show_deployment_info() {
    print_header "Deployment Information"
    
    echo -e "${CYAN}Backend API:${NC} https://furapi.astrokabinet.id"
    echo -e "${CYAN}Health Check:${NC} https://furapi.astrokabinet.id/health"
    echo -e "${CYAN}API Endpoints:${NC} https://furapi.astrokabinet.id/api/v1"
    echo -e "${CYAN}Nginx Proxy:${NC} Configured for furapi.astrokabinet.id"
    echo -e "${CYAN}VPS Location:${NC} $VPS_DEPLOY_PATH"
    echo ""
    echo -e "${YELLOW}SSL Certificate:${NC}"
    echo "  Self-signed SSL certificate configured"
    echo "  Certificate location: /etc/nginx/ssl/"
    echo ""
    echo -e "${YELLOW}Management Commands:${NC}"
    echo "  View logs: ssh $VPS_HOST 'cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.yml logs -f'"
    echo "  Restart:   ssh $VPS_HOST 'cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.yml restart'"
    echo "  Stop:      ssh $VPS_HOST 'cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.yml down'"
    echo "  Update:    $0"
}

# Main deployment function
main() {
    print_header "Astro Works Backend API Deployment"
    print_info "Deploying to VPS: $VPS_HOST"
    print_info "Target port: 7998"
    
    check_prerequisites
    setup_docker_context
    test_vps_connection
    prepare_vps_environment
    deploy_services
    run_migrations
    test_deployment
    show_deployment_info
    
    print_success "Deployment completed successfully!"
}

# Run main function
main "$@"
