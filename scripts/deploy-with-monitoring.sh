#!/bin/bash

# Deploy with Resource Monitoring
# This script monitors system resources during deployment to prevent overload

set -e

echo "🚀 Starting Astro Works Deployment with Resource Monitoring..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check system resources
check_resources() {
    echo -e "${BLUE}📊 Checking system resources...${NC}"
    
    # Get memory info (in MB)
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    USED_MEM=$(free -m | awk 'NR==2{printf "%.0f", $3}')
    FREE_MEM=$(free -m | awk 'NR==2{printf "%.0f", $4}')
    MEM_USAGE=$(echo "scale=1; $USED_MEM * 100 / $TOTAL_MEM" | bc)
    
    # Get CPU load
    CPU_LOAD=$(uptime | awk -F'load average:' '{ print $2 }' | cut -d, -f1 | sed 's/^[ \t]*//')
    
    # Get disk usage
    DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    echo -e "💾 Memory: ${USED_MEM}MB/${TOTAL_MEM}MB (${MEM_USAGE}% used)"
    echo -e "🔥 CPU Load: ${CPU_LOAD}"
    echo -e "💿 Disk Usage: ${DISK_USAGE}%"
    
    # Check if resources are too high (stricter for 1GB VPS)
    if (( $(echo "$MEM_USAGE > 70" | bc -l) )); then
        echo -e "${RED}⚠️  WARNING: Memory usage is high (${MEM_USAGE}%) for 1GB VPS${NC}"
        echo -e "${YELLOW}Consider freeing up memory before deployment${NC}"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    if (( $(echo "$DISK_USAGE > 85" | bc -l) )); then
        echo -e "${RED}⚠️  WARNING: Disk usage is high (${DISK_USAGE}%)${NC}"
        echo -e "${YELLOW}Consider cleaning up disk space before deployment${NC}"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    echo -e "${GREEN}✅ Resource check completed${NC}"
}

# Function to monitor resources during build
monitor_build() {
    echo -e "${BLUE}📈 Starting resource monitoring...${NC}"
    
    # Start monitoring in background
    (
        while true; do
            MEM_USAGE=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
            CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}')
            
            echo "$(date '+%H:%M:%S') - Memory: ${MEM_USAGE}% | CPU: ${CPU_USAGE}%"
            
            # Alert if usage is too high (stricter for 1GB VPS)
            if (( $(echo "$MEM_USAGE > 80" | bc -l) )); then
                echo -e "${RED}🚨 ALERT: Memory usage critical (${MEM_USAGE}%) on 1GB VPS${NC}"
            fi
            
            sleep 10
        done
    ) &
    
    MONITOR_PID=$!
    echo "Monitor started with PID: $MONITOR_PID"
}

# Function to stop monitoring
stop_monitor() {
    if [ ! -z "$MONITOR_PID" ]; then
        kill $MONITOR_PID 2>/dev/null || true
        echo -e "${GREEN}📊 Resource monitoring stopped${NC}"
    fi
}

# Trap to ensure monitor is stopped on exit
trap stop_monitor EXIT

# Main deployment function
deploy() {
    echo -e "${BLUE}🔧 Starting deployment process...${NC}"
    
    # Change to deployment directory
    cd "$(dirname "$0")"
    
    # Check if Docker context exists
    if ! docker context inspect astro-vps >/dev/null 2>&1; then
        echo -e "${RED}❌ Docker context 'astro-vps' not found${NC}"
        echo -e "${YELLOW}Please set up Docker context first${NC}"
        exit 1
    fi
    
    # Use Docker context
    export DOCKER_CONTEXT=astro-vps
    
    echo -e "${BLUE}🐳 Building and deploying with resource limits...${NC}"
    
    # Deploy with resource limits
    docker-compose -f docker-compose.prod.yml up -d --build
    
    echo -e "${GREEN}✅ Deployment completed${NC}"
}

# Function to check deployment health
check_health() {
    echo -e "${BLUE}🏥 Checking deployment health...${NC}"
    
    # Wait for services to start
    sleep 30
    
    # Check backend health
    if curl -f -s --connect-timeout 10 https://api.astrokabinet.id/health >/dev/null; then
        echo -e "${GREEN}✅ Backend is healthy${NC}"
    else
        echo -e "${RED}❌ Backend health check failed${NC}"
        echo -e "${YELLOW}Checking logs...${NC}"
        docker --context astro-vps logs astro_backend_prod --tail 20
    fi
    
    # Check database health
    if docker --context astro-vps exec astro_postgres_prod pg_isready -U astro_user >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Database is healthy${NC}"
    else
        echo -e "${RED}❌ Database health check failed${NC}"
    fi
    
    # Check Redis health
    if docker --context astro-vps exec astro_redis_prod redis-cli ping >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis is healthy${NC}"
    else
        echo -e "${RED}❌ Redis health check failed${NC}"
    fi
}

# Main execution
main() {
    echo -e "${GREEN}🌟 Astro Works Deployment with Resource Management${NC}"
    echo "=================================================="
    
    # Check initial resources
    check_resources
    
    # Start monitoring
    monitor_build
    
    # Deploy
    deploy
    
    # Stop monitoring
    stop_monitor
    
    # Check health
    check_health
    
    echo -e "${GREEN}🎉 Deployment process completed!${NC}"
    echo -e "${BLUE}Frontend: https://astrokabinet.id${NC}"
    echo -e "${BLUE}Backend API: https://api.astrokabinet.id${NC}"
}

# Run main function
main "$@"
