#!/bin/bash

set -e

echo "🚀 Deploying Astro Works Indonesia"
echo "=================================="

# Pull latest images
echo "Pulling latest images..."
docker-compose -f docker-compose.prod.yml pull

# Build and start services
echo "Building and starting services..."
docker-compose -f docker-compose.prod.yml up -d --build

# Wait for services to be healthy
echo "Waiting for services to be healthy..."
sleep 30

# Show status
echo "Checking service status..."
docker-compose -f docker-compose.prod.yml ps

# Show logs
echo "Showing recent logs..."
docker-compose -f docker-compose.prod.yml logs --tail=50

echo "✅ Deployment completed!"
echo "Backend API: http://localhost:7998"
echo "Frontend: http://localhost:3000"
echo "Prometheus: http://localhost:9090"
echo "Database: localhost:5432"
