version: '3.8'

services:
  backend:
    build:
      context: ../../be_astro
      dockerfile: Dockerfile.deploy
    container_name: astro_backend_prod
    environment:
      DATABASE_URL: postgres://astro_user:${POSTGRES_PASSWORD}@postgres:5432/astro_ecommerce
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      HOST: 0.0.0.0
      PORT: 7998
      RUST_LOG: ${RUST_LOG:-info}
      CORS_ORIGIN: ${CORS_ORIGIN:-https://astrokabinet.id,https://www.astrokabinet.id}
      WHATSAPP_PHONE_NUMBER: ${WHATSAPP_PHONE_NUMBER:-***********}
      ADMIN_DEFAULT_EMAIL: ${ADMIN_DEFAULT_EMAIL}
      ADMIN_DEFAULT_PASSWORD: ${ADMIN_DEFAULT_PASSWORD}
      BANK_NAME: ${BANK_NAME}
      BANK_ACCOUNT_NUMBER: ${BANK_ACCOUNT_NUMBER}
      BANK_ACCOUNT_NAME: ${BANK_ACCOUNT_NAME}
    volumes:
      - ../../be_astro/static:/app/static
    expose:
      - "7998"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7998/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`api.astrokabinet.id`)"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=7998"
      - "traefik.http.routers.backend.middlewares=api-ratelimit,security-headers"
    networks:
      - traefik
      - astro_network
    # Minimal resource limits for 1GB VPS
    deploy:
      resources:
        limits:
          cpus: '0.4'        # 40% CPU for 1 CPU VPS
          memory: 384M       # 384MB for 1GB VPS
        reservations:
          cpus: '0.1'
          memory: 192M
