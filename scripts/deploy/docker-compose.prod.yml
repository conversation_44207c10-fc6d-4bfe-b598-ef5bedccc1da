version: '3.8'

services:
  postgres:
    image: postgres:16-alpine
    container_name: astro_postgres_prod
    environment:
      POSTGRES_DB: astro_ecommerce
      POSTGRES_USER: astro_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./be_astro/migrations:/docker-entrypoint-initdb.d
    networks:
      - astro_network
    restart: unless-stopped
    command: >
      postgres
      -c max_connections=50
      -c shared_buffers=64MB
      -c effective_cache_size=256MB
      -c maintenance_work_mem=16MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=4MB
      -c default_statistics_target=50
      -c random_page_cost=1.1
      -c effective_io_concurrency=100
      -c log_statement=none
      -c log_min_duration_statement=2000
      -c work_mem=2MB
      -c temp_buffers=8MB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U astro_user -d astro_ecommerce"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Resource Limits for PostgreSQL (Optimized for 1GB VPS)
    deploy:
      resources:
        limits:
          cpus: '0.3'        # Limit to 30% of 1 CPU core
          memory: 256M       # Limit to 256MB RAM
        reservations:
          cpus: '0.05'       # Reserve 5% of 1 CPU core
          memory: 128M       # Reserve 128MB RAM

  redis:
    image: redis:7-alpine
    container_name: astro_redis_prod
    volumes:
      - redis_data:/data
    networks:
      - astro_network
    restart: unless-stopped
    command: >
      redis-server
      --maxmemory 96mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save ""
      --appendonly no
      --tcp-keepalive 60
      --timeout 300
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    # Resource Limits for Redis (Optimized for 1GB VPS)
    deploy:
      resources:
        limits:
          cpus: '0.15'       # Limit to 15% of 1 CPU core
          memory: 128M       # Limit to 128MB RAM
        reservations:
          cpus: '0.05'       # Reserve 5% of 1 CPU core
          memory: 64M        # Reserve 64MB RAM

  backend:
    build:
      context: ../../be_astro
      dockerfile: Dockerfile
    container_name: astro_backend_prod
    environment:
      DATABASE_URL: postgres://astro_user:${POSTGRES_PASSWORD}@postgres:5432/astro_ecommerce
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      HOST: 0.0.0.0
      PORT: 7998
      RUST_LOG: ${RUST_LOG:-info}
      CORS_ORIGIN: ${CORS_ORIGIN:-https://astrokabinet.id,https://www.astrokabinet.id}
      WHATSAPP_PHONE_NUMBER: ${WHATSAPP_PHONE_NUMBER:-***********}
      ADMIN_DEFAULT_EMAIL: ${ADMIN_DEFAULT_EMAIL}
      ADMIN_DEFAULT_PASSWORD: ${ADMIN_DEFAULT_PASSWORD}
      BANK_NAME: ${BANK_NAME}
      BANK_ACCOUNT_NUMBER: ${BANK_ACCOUNT_NUMBER}
      BANK_ACCOUNT_NAME: ${BANK_ACCOUNT_NAME}
    volumes:
      - ../../be_astro/static:/app/static
    expose:
      - "7998"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7998/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`furapi.astrokabinet.id`)"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=7998"
      - "traefik.http.routers.backend.middlewares=api-ratelimit,security-headers"
    networks:
      - traefik
      - astro_network
    # Resource Limits for Backend (Optimized for 1GB VPS)
    deploy:
      resources:
        limits:
          cpus: '0.5'        # Limit to 50% of 1 CPU core
          memory: 512M       # Limit to 512MB RAM
        reservations:
          cpus: '0.1'        # Reserve 10% of 1 CPU core
          memory: 256M       # Reserve 256MB RAM

# Frontend deployment handled separately via Cloudflare Workers
  # frontend:
  #   build:
  #     context: ./fe_astro
  #     dockerfile: Dockerfile.prod
  #     args:
  #       VITE_API_URL: ${API_URL:-https://astro-works.local/api/v1}
  #   container_name: astro_frontend_prod
  #   expose:
  #     - "3000"
  #   depends_on:
  #     backend:
  #       condition: service_healthy
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 30s
  #   labels:
  #     - "traefik.enable=true"
  #     - "traefik.http.routers.frontend.rule=Host(`${DOMAIN:-astro-works.local}`)"
  #     - "traefik.http.routers.frontend.entrypoints=websecure"
  #     - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"
  #     - "traefik.http.services.frontend.loadbalancer.server.port=3000"
  #     - "traefik.http.routers.frontend.middlewares=web-ratelimit,security-headers"
  #   networks:
  #     - traefik
  #     - astro_network

  traefik:
    image: traefik:v3.0
    command:
      - "--api.dashboard=true"
      - "--api.insecure=false"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=traefik"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL:-<EMAIL>}"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
      - "--log.level=INFO"
      - "--accesslog=true"
      - "--metrics.prometheus=true"
      - "--metrics.prometheus.addEntryPointsLabels=true"
      - "--metrics.prometheus.addServicesLabels=true"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "letsencrypt:/letsencrypt"
      - "../../traefik:/etc/traefik"
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      # Dashboard
      - "traefik.http.routers.dashboard.rule=Host(`traefik.${DOMAIN:-astro-works.local}`)"
      - "traefik.http.routers.dashboard.entrypoints=websecure"
      - "traefik.http.routers.dashboard.tls.certresolver=letsencrypt"
      - "traefik.http.routers.dashboard.service=api@internal"
      - "traefik.http.routers.dashboard.middlewares=dashboard-auth"
      # HTTP to HTTPS redirect
      - "traefik.http.routers.http-catchall.rule=hostregexp(`{host:.+}`)"
      - "traefik.http.routers.http-catchall.entrypoints=web"
      - "traefik.http.routers.http-catchall.middlewares=redirect-to-https"
      # Middlewares
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.permanent=true"
      - "traefik.http.middlewares.dashboard-auth.basicauth.users=${TRAEFIK_AUTH:-admin:$$2y$$10$$X2jnWkNVxHuAEzn7Jt8XxOzjqZrqQxQxQxQxQxQxQxQxQxQxQxQxQx}"
      # Rate limiting
      - "traefik.http.middlewares.api-ratelimit.ratelimit.average=10"
      - "traefik.http.middlewares.api-ratelimit.ratelimit.burst=20"
      - "traefik.http.middlewares.web-ratelimit.ratelimit.average=30"
      - "traefik.http.middlewares.web-ratelimit.ratelimit.burst=50"
      # Security headers
      - "traefik.http.middlewares.security-headers.headers.frameDeny=true"
      - "traefik.http.middlewares.security-headers.headers.contentTypeNosniff=true"
      - "traefik.http.middlewares.security-headers.headers.browserXssFilter=true"
      - "traefik.http.middlewares.security-headers.headers.referrerPolicy=strict-origin-when-cross-origin"
      - "traefik.http.middlewares.security-headers.headers.customRequestHeaders.X-Forwarded-Proto=https"
    networks:
      - traefik

volumes:
  postgres_data:
  redis_data:
  letsencrypt:

networks:
  traefik:
    external: true
  astro_network:
    driver: bridge
