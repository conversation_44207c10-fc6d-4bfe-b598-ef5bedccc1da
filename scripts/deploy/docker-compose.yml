version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: astro_postgres
    environment:
      POSTGRES_DB: astro_ecommerce
      POSTGRES_USER: astro_user
      POSTGRES_PASSWORD: astro_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../be_astro/database/init:/docker-entrypoint-initdb.d
    networks:
      - astro_network
    restart: unless-stopped

  # Mini-Redis Cache
  redis:
    image: redis:7-alpine
    container_name: astro_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - astro_network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Rust Backend (for development)
  backend:
    build:
      context: ../../be_astro
      dockerfile: Dockerfile
    container_name: astro_backend
    environment:
      DATABASE_URL: **************************************************/astro_ecommerce
      REDIS_URL: redis://redis:6379
      RUST_LOG: debug
    ports:
      - "7998:7998"
    depends_on:
      - postgres
      - redis
    networks:
      - astro_network
    restart: unless-stopped
    volumes:
      - ../../be_astro:/app
    working_dir: /app

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  astro_network:
    driver: bridge
  traefik:
    external: true
