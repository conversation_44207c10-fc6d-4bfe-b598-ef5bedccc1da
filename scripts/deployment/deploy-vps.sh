#!/bin/bash

# =============================================================================
# VPS Auto Deployment Script using Docker Context
# =============================================================================
# This script deploys the Astro Works backend to VPS using Docker context

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
VPS_USER="servomo"
VPS_IP="*************"
VPS_HOST="${VPS_USER}@${VPS_IP}"
CONTEXT_NAME="astro-vps"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
ENV_FILE="$PROJECT_ROOT/.env.vps"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.prod.yml"

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if Docker context exists
    if ! docker context ls | grep -q "$CONTEXT_NAME"; then
        print_error "Docker context '$CONTEXT_NAME' not found"
        print_info "Run: ./scripts/setup-docker-context.sh"
        exit 1
    fi
    print_success "Docker context '$CONTEXT_NAME' found"
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        print_error "Environment file not found: $ENV_FILE"
        print_info "Run: ./scripts/setup-docker-context.sh"
        exit 1
    fi
    print_success "Environment file found"
    
    # Test VPS connectivity
    print_info "Testing VPS connectivity..."
    if docker --context "$CONTEXT_NAME" version &>/dev/null; then
        print_success "VPS Docker connectivity working"
    else
        print_error "Cannot connect to VPS Docker"
        exit 1
    fi
}

# Sync project files to VPS
sync_files_to_vps() {
    print_header "Syncing Project Files to VPS"
    
    print_info "Syncing project files..."

    # Create remote directory
    ssh "$VPS_HOST" "sudo mkdir -p /srv/app/astro-works && sudo chown -R servomo:servomo /srv/app/astro-works"

    # Install rsync if not available
    ssh "$VPS_HOST" "command -v rsync >/dev/null 2>&1 || sudo apt-get update && sudo apt-get install -y rsync"

    # Sync necessary files
    rsync -avz --delete \
        --exclude 'target/' \
        --exclude 'node_modules/' \
        --exclude '.git/' \
        --exclude '.svelte-kit/' \
        --exclude 'build/' \
        --exclude 'dist/' \
        "$PROJECT_ROOT/" "$VPS_HOST:/srv/app/astro-works/"
    
    print_success "Project files synced to VPS"
}

# Build images on VPS
build_images_on_vps() {
    print_header "Building Images on VPS"
    
    print_info "Building backend image on VPS..."
    ssh "$VPS_HOST" "cd /srv/app/astro-works && docker build \
        -t astro-works-backend:latest \
        -f be_astro/Dockerfile.prod \
        be_astro/"
    
    print_success "Backend image built on VPS"
}

# Create backup on VPS
create_backup_on_vps() {
    print_header "Creating Backup on VPS"
    
    print_info "Checking if database is running..."
    if docker --context "$CONTEXT_NAME" ps | grep -q "astro_postgres_prod.*Up"; then
        timestamp=$(date +%Y%m%d_%H%M%S)
        backup_file="/srv/app/astro-works/backups/pre_deploy_backup_$timestamp.sql"

        print_info "Creating database backup..."
        docker --context "$CONTEXT_NAME" exec astro_postgres_prod \
            pg_dump -U astro_user astro_ecommerce > /tmp/backup.sql

        ssh "$VPS_HOST" "mkdir -p /srv/app/astro-works/backups"
        scp "$VPS_HOST:/tmp/backup.sql" "$VPS_HOST:$backup_file"
        
        print_success "Backup created: $backup_file"
    else
        print_warning "Database not running, skipping backup"
    fi
}

# Deploy services to VPS
deploy_services_to_vps() {
    print_header "Deploying Services to VPS"
    
    print_info "Creating Docker networks..."
    docker --context "$CONTEXT_NAME" network create traefik 2>/dev/null || print_info "Network already exists"
    
    print_info "Deploying services with Docker Compose..."
    ssh "$VPS_HOST" "cd /srv/app/astro-works && docker compose \
        -f docker-compose.prod.yml \
        --env-file .env.vps \
        up -d --build"
    
    print_success "Services deployed to VPS"
}

# Wait for services to be healthy
wait_for_health() {
    print_header "Waiting for Services to be Healthy"
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        print_info "Health check attempt $attempt/$max_attempts..."
        
        # Check backend health
        if docker --context "$CONTEXT_NAME" exec astro_backend_prod curl -f http://localhost:8000/health &>/dev/null; then
            print_success "Backend is healthy"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    print_error "Services failed to become healthy"
    return 1
}

# Run database migrations
run_migrations() {
    print_header "Running Database Migrations"
    
    print_info "Running migrations..."
    if docker --context "$CONTEXT_NAME" exec astro_backend_prod ./be_astro migrate 2>/dev/null; then
        print_success "Migrations completed"
    else
        print_warning "Migration command not available or failed"
    fi
}

# Show deployment status
show_deployment_status() {
    print_header "Deployment Status"
    
    print_info "Container status:"
    docker --context "$CONTEXT_NAME" ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    print_info "Service URLs:"
    echo -e "  Backend API: ${YELLOW}http://$VPS_IP:8000${NC}"
    echo -e "  Health Check: ${YELLOW}http://$VPS_IP:8000/health${NC}"
    echo -e "  Traefik Dashboard: ${YELLOW}http://$VPS_IP:8080${NC}"
    
    echo ""
    print_info "Useful commands:"
    echo "  View logs: docker --context $CONTEXT_NAME logs astro_backend_prod"
    echo "  SSH to VPS: ssh $VPS_HOST"
    echo "  Container shell: docker --context $CONTEXT_NAME exec astro_backend_prod /bin/bash"
}

# Cleanup old images
cleanup_old_images() {
    print_header "Cleaning Up"
    
    print_info "Removing unused images..."
    docker --context "$CONTEXT_NAME" image prune -f >/dev/null 2>&1 || true
    
    print_success "Cleanup completed"
}

# Rollback function
rollback() {
    print_header "Rolling Back Deployment"
    
    print_warning "Rolling back to previous version..."
    
    # Stop current services
    docker --context "$CONTEXT_NAME" compose \
        -f /srv/app/astro-works/docker-compose.prod.yml \
        --env-file /srv/app/astro-works/.env.vps \
        down

    # Restore from backup if available
    latest_backup=$(ssh "$VPS_HOST" "ls -t /srv/app/astro-works/backups/*.sql 2>/dev/null | head -1" || echo "")
    if [ -n "$latest_backup" ]; then
        print_info "Restoring from backup: $latest_backup"
        docker --context "$CONTEXT_NAME" exec -i astro_postgres_prod \
            psql -U astro_user -d astro_ecommerce < "$latest_backup"
    fi
    
    print_success "Rollback completed"
}

# Main deployment function
main() {
    echo -e "${BLUE}🚀 Astro Works VPS Auto Deployment${NC}"
    echo -e "${BLUE}===================================${NC}"
    echo ""
    echo -e "${CYAN}Target VPS:${NC}"
    echo -e "  Host: ${YELLOW}$VPS_HOST${NC}"
    echo -e "  Context: ${YELLOW}$CONTEXT_NAME${NC}"
    echo ""
    
    # Trap for cleanup on failure
    trap 'print_error "Deployment failed! Run with --rollback to rollback"; exit 1' ERR
    
    check_prerequisites
    sync_files_to_vps
    create_backup_on_vps
    build_images_on_vps
    deploy_services_to_vps
    
    if wait_for_health; then
        run_migrations
        cleanup_old_images
        show_deployment_status
        
        echo ""
        print_success "🎉 Deployment completed successfully!"
        print_info "Backend is now running on VPS at http://$VPS_IP:8000"
    else
        print_error "Deployment failed - services are not healthy"
        print_info "Check logs with: docker --context $CONTEXT_NAME logs astro_backend_prod"
        exit 1
    fi
}

# Handle command line arguments
case "${1:-}" in
    --rollback)
        rollback
        ;;
    --status)
        docker --context "$CONTEXT_NAME" ps
        ;;
    --logs)
        docker --context "$CONTEXT_NAME" logs -f astro_backend_prod
        ;;
    --help)
        echo "VPS Deployment Script"
        echo ""
        echo "Usage: $0 [OPTION]"
        echo ""
        echo "Options:"
        echo "  (no args)   Deploy to VPS"
        echo "  --rollback  Rollback deployment"
        echo "  --status    Show container status"
        echo "  --logs      Show backend logs"
        echo "  --help      Show this help"
        ;;
    *)
        main
        ;;
esac
