#!/bin/bash

# Production Deployment Script for Astro Works E-commerce Platform
# This script handles the complete deployment process

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_ROOT/.env.prod"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.prod.yml"
BACKUP_DIR="$PROJECT_ROOT/data/backups"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if running as root (not recommended)
    if [ "$EUID" -eq 0 ]; then
        log_warning "Running as root is not recommended for production deployment"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Check required commands
    for cmd in docker docker-compose curl; do
        if ! command -v $cmd &> /dev/null; then
            log_error "$cmd is required but not installed"
            exit 1
        fi
    done
    
    # Check environment file
    if [ ! -f "$ENV_FILE" ]; then
        log_error "Environment file not found: $ENV_FILE"
        log_info "Please copy .env.prod.example to .env.prod and configure it"
        exit 1
    fi
    
    # Check compose file
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker compose file not found: $COMPOSE_FILE"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Create backup before deployment
create_backup() {
    log_info "Creating backup before deployment..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Check if services are running
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps postgres | grep -q "Up"; then
        timestamp=$(date +%Y%m%d_%H%M%S)
        backup_file="$BACKUP_DIR/pre_deploy_backup_$timestamp.sql"
        
        log_info "Creating database backup: $backup_file"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T postgres \
            pg_dump -U astro_user astro_ecommerce > "$backup_file"
        
        if [ -f "$backup_file" ] && [ -s "$backup_file" ]; then
            log_success "Backup created successfully: $backup_file"
        else
            log_error "Backup creation failed"
            exit 1
        fi
    else
        log_warning "PostgreSQL not running, skipping backup"
    fi
}

# Build images
build_images() {
    log_info "Building production images..."
    
    # Build backend
    log_info "Building backend image..."
    docker build -t astro-works-backend:latest -f "$PROJECT_ROOT/be_astro/Dockerfile.prod" "$PROJECT_ROOT/be_astro/"
    
    # Build frontend
    log_info "Building frontend image..."
    docker build -t astro-works-frontend:latest -f "$PROJECT_ROOT/fe_astro/Dockerfile.prod" "$PROJECT_ROOT/fe_astro/"
    
    log_success "Images built successfully"
}

# Deploy services
deploy_services() {
    log_info "Deploying services..."
    
    # Create Traefik network if it doesn't exist
    docker network create traefik 2>/dev/null || log_info "Traefik network already exists"
    
    # Deploy with docker-compose
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    log_success "Services deployed"
}

# Wait for services to be healthy
wait_for_health() {
    log_info "Waiting for services to be healthy..."
    
    max_attempts=30
    attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        attempt=$((attempt + 1))
        log_info "Health check attempt $attempt/$max_attempts..."
        
        # Check if all services are healthy
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps | grep -q "unhealthy"; then
            log_warning "Some services are unhealthy, waiting..."
            sleep 10
            continue
        fi
        
        # Check if backend is responding
        if curl -f http://localhost/health >/dev/null 2>&1; then
            log_success "All services are healthy"
            return 0
        fi
        
        sleep 10
    done
    
    log_error "Services failed to become healthy within timeout"
    return 1
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Wait a bit more for database to be fully ready
    sleep 5
    
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend \
        ./be_astro migrate 2>/dev/null; then
        log_success "Database migrations completed"
    else
        log_warning "Migration command not available, skipping..."
    fi
}

# Cleanup old images
cleanup() {
    log_info "Cleaning up old images..."
    
    # Remove dangling images
    docker image prune -f >/dev/null 2>&1 || true
    
    log_success "Cleanup completed"
}

# Show deployment summary
show_summary() {
    log_success "Deployment completed successfully!"
    echo ""
    echo -e "${BLUE}📊 Deployment Summary${NC}"
    echo "====================="
    
    # Load domain from env file
    DOMAIN=$(grep "^DOMAIN=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"')
    
    echo -e "${GREEN}🌐 Application URLs:${NC}"
    echo "  Frontend: https://${DOMAIN:-astro-works.local}"
    echo "  API: https://${DOMAIN:-astro-works.local}/api/v1"
    echo "  Health Check: https://${DOMAIN:-astro-works.local}/health"
    echo "  Traefik Dashboard: https://traefik.${DOMAIN:-astro-works.local}"
    echo ""
    
    echo -e "${GREEN}🐳 Service Status:${NC}"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
    echo ""
    
    echo -e "${GREEN}💡 Useful Commands:${NC}"
    echo "  View logs: make logs-prod"
    echo "  Health check: ./scripts/health-check.sh"
    echo "  Restart services: make restart-prod"
    echo "  Create backup: make backup"
}

# Main deployment process
main() {
    echo -e "${BLUE}🚀 Astro Works Production Deployment${NC}"
    echo -e "${BLUE}====================================${NC}"
    echo ""
    
    check_prerequisites
    create_backup
    build_images
    deploy_services
    
    if wait_for_health; then
        run_migrations
        cleanup
        show_summary
    else
        log_error "Deployment failed - services are not healthy"
        log_info "Check logs with: make logs-prod"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --no-backup    Skip backup creation"
        echo "  --no-build     Skip image building"
        echo ""
        echo "Environment variables:"
        echo "  SKIP_BACKUP=1  Skip backup creation"
        echo "  SKIP_BUILD=1   Skip image building"
        exit 0
        ;;
    --no-backup)
        SKIP_BACKUP=1
        ;;
    --no-build)
        SKIP_BUILD=1
        ;;
esac

# Override functions based on flags
if [ "${SKIP_BACKUP:-}" = "1" ]; then
    create_backup() {
        log_info "Skipping backup creation (SKIP_BACKUP=1)"
    }
fi

if [ "${SKIP_BUILD:-}" = "1" ]; then
    build_images() {
        log_info "Skipping image building (SKIP_BUILD=1)"
    }
fi

# Run main deployment
main
