#!/bin/bash

# =============================================================================
# Fast VPS Build Script with Optimizations
# =============================================================================
# This script performs optimized build and deployment to VPS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
VPS_USER="servomo"
VPS_IP="*************"
VPS_HOST="${VPS_USER}@${VPS_IP}"
CONTEXT_NAME="astro-vps"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Pre-build optimizations
pre_build_optimizations() {
    print_header "Pre-Build Optimizations"
    
    # Clean previous builds
    print_info "Cleaning previous builds..."
    cd "$PROJECT_ROOT/be_astro"
    cargo clean
    cd "$PROJECT_ROOT"
    
    # Update Rust toolchain
    print_info "Updating Rust toolchain..."
    rustup update stable
    
    # Install build tools if needed
    if ! command -v sccache &> /dev/null; then
        print_info "Installing sccache for faster compilation..."
        cargo install sccache --locked
        export RUSTC_WRAPPER=sccache
    fi
    
    print_success "Pre-build optimizations completed"
}

# Sync only necessary files
sync_optimized() {
    print_header "Syncing Optimized Files"
    
    print_info "Creating optimized file list..."
    
    # Create temporary directory for optimized sync
    temp_dir=$(mktemp -d)
    
    # Copy only essential files
    cp -r be_astro "$temp_dir/"
    cp docker-compose.prod.yml "$temp_dir/"
    cp .env.vps "$temp_dir/"
    
    # Remove unnecessary files from temp directory
    find "$temp_dir/be_astro" -name "target" -type d -exec rm -rf {} + 2>/dev/null || true
    find "$temp_dir/be_astro" -name "*.orig" -delete 2>/dev/null || true
    
    print_info "Syncing optimized files to VPS..."
    ssh "$VPS_HOST" "sudo mkdir -p /srv/app/astro-works && sudo chown -R servomo:servomo /srv/app/astro-works"
    
    rsync -avz --delete \
        "$temp_dir/" "$VPS_HOST:/srv/app/astro-works/"
    
    # Cleanup
    rm -rf "$temp_dir"
    
    print_success "Optimized sync completed"
}

# Build with optimizations on VPS
build_optimized() {
    print_header "Building with Optimizations on VPS"
    
    print_info "Setting up build environment on VPS..."
    ssh "$VPS_HOST" << 'EOF'
        cd /srv/app/astro-works
        
        # Install build dependencies if needed
        if ! command -v rustc &> /dev/null; then
            curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
            source ~/.cargo/env
        fi
        
        # Update Rust
        rustup update stable
        
        # Set environment variables for faster builds
        export CARGO_NET_GIT_FETCH_WITH_CLI=true
        export CARGO_REGISTRIES_CRATES_IO_PROTOCOL=sparse
        export RUSTFLAGS="-C target-cpu=native -C opt-level=3"
        
        # Build with Docker buildkit for better caching
        export DOCKER_BUILDKIT=1
        
        echo "Build environment ready"
EOF
    
    print_info "Building Docker image with optimizations..."
    ssh "$VPS_HOST" << 'EOF'
        cd /srv/app/astro-works
        
        # Build with BuildKit and cache mounts
        DOCKER_BUILDKIT=1 docker build \
            --build-arg BUILDKIT_INLINE_CACHE=1 \
            --cache-from astro-works-backend:latest \
            -t astro-works-backend:latest \
            -f be_astro/Dockerfile.prod \
            be_astro/
EOF
    
    print_success "Optimized build completed"
}

# Deploy with minimal downtime
deploy_fast() {
    print_header "Fast Deployment"
    
    print_info "Deploying with minimal downtime..."
    ssh "$VPS_HOST" << 'EOF'
        cd /srv/app/astro-works
        
        # Use docker-compose for zero-downtime deployment
        docker compose -f docker-compose.prod.yml --env-file .env.vps up -d --no-deps backend
        
        # Wait for health check
        echo "Waiting for service to be healthy..."
        for i in {1..30}; do
            if docker exec astro_backend_prod curl -f http://localhost:8000/health >/dev/null 2>&1; then
                echo "Service is healthy"
                break
            fi
            echo "Waiting... ($i/30)"
            sleep 10
        done
EOF
    
    print_success "Fast deployment completed"
}

# Show build statistics
show_build_stats() {
    print_header "Build Statistics"
    
    print_info "Docker image sizes:"
    ssh "$VPS_HOST" "docker images astro-works-backend --format 'table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}'"
    
    print_info "Container status:"
    ssh "$VPS_HOST" "docker ps --filter name=astro_backend_prod --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'"
    
    print_info "Build cache usage:"
    ssh "$VPS_HOST" "docker system df"
}

# Main function
main() {
    echo -e "${BLUE}🚀 Fast VPS Build & Deploy${NC}"
    echo -e "${BLUE}==========================${NC}"
    echo ""
    echo -e "${CYAN}Target: ${YELLOW}$VPS_HOST${NC}"
    echo -e "${CYAN}Started: ${YELLOW}$(date)${NC}"
    echo ""
    
    local start_time=$(date +%s)
    
    case "${1:-all}" in
        "pre")
            pre_build_optimizations
            ;;
        "sync")
            sync_optimized
            ;;
        "build")
            build_optimized
            ;;
        "deploy")
            deploy_fast
            ;;
        "stats")
            show_build_stats
            ;;
        "all")
            pre_build_optimizations
            sync_optimized
            build_optimized
            deploy_fast
            show_build_stats
            ;;
        *)
            echo "Usage: $0 [pre|sync|build|deploy|stats|all]"
            echo ""
            echo "Commands:"
            echo "  pre     - Pre-build optimizations"
            echo "  sync    - Sync optimized files"
            echo "  build   - Build with optimizations"
            echo "  deploy  - Fast deployment"
            echo "  stats   - Show build statistics"
            echo "  all     - Run all steps (default)"
            exit 1
            ;;
    esac
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    print_success "🎉 Fast build completed in ${duration}s"
    print_info "Backend API: http://$VPS_IP:8000"
    print_info "Health check: http://$VPS_IP:8000/health"
}

main "$@"
