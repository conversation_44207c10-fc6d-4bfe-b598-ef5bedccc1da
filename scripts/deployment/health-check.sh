#!/bin/bash

# Health Check Script for Astro Works E-commerce
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
BACKEND_URL="http://localhost:8000"
FRONTEND_URL="http://localhost:5173"
PROD_FRONTEND_URL="http://localhost:3000"
DB_HOST="localhost"
DB_PORT="5432"
REDIS_HOST="localhost"
REDIS_PORT="6379"

echo "🏥 Astro Works Health Check"
echo "=========================="

# Check backend health
print_status "Checking backend health..."
if curl -f "$BACKEND_URL/health" >/dev/null 2>&1; then
    response=$(curl -s "$BACKEND_URL/health")
    if echo "$response" | grep -q "ok"; then
        print_success "Backend is healthy"
    else
        print_warning "Backend responded but status unclear: $response"
    fi
else
    print_error "Backend is not responding at $BACKEND_URL"
fi

# Check frontend (development)
print_status "Checking frontend (development)..."
if curl -f "$FRONTEND_URL" >/dev/null 2>&1; then
    print_success "Frontend (dev) is accessible"
else
    print_warning "Frontend (dev) is not accessible at $FRONTEND_URL"
fi

# Check frontend (production)
print_status "Checking frontend (production)..."
if curl -f "$PROD_FRONTEND_URL" >/dev/null 2>&1; then
    print_success "Frontend (prod) is accessible"
else
    print_warning "Frontend (prod) is not accessible at $PROD_FRONTEND_URL"
fi

# Check PostgreSQL
print_status "Checking PostgreSQL..."
if command -v pg_isready >/dev/null 2>&1; then
    if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U astro_user >/dev/null 2>&1; then
        print_success "PostgreSQL is ready"
    else
        print_error "PostgreSQL is not ready"
    fi
else
    # Try with docker
    if docker-compose exec -T postgres pg_isready -U astro_user >/dev/null 2>&1; then
        print_success "PostgreSQL is ready (via Docker)"
    else
        print_error "PostgreSQL is not accessible"
    fi
fi

# Check Redis
print_status "Checking Redis..."
if command -v redis-cli >/dev/null 2>&1; then
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping >/dev/null 2>&1; then
        print_success "Redis is ready"
    else
        print_error "Redis is not ready"
    fi
else
    # Try with docker
    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        print_success "Redis is ready (via Docker)"
    else
        print_error "Redis is not accessible"
    fi
fi

# Check API endpoints
print_status "Checking API endpoints..."
endpoints=(
    "/health"
    "/api/v1/categories"
    "/api/v1/products"
    "/api/v1/products/accessories"
    "/api/v1/bundles"
)

for endpoint in "${endpoints[@]}"; do
    if curl -f "$BACKEND_URL$endpoint" >/dev/null 2>&1; then
        print_success "✓ $endpoint"
    else
        print_error "✗ $endpoint"
    fi
done

# Check Docker containers
print_status "Checking Docker containers..."
if command -v docker >/dev/null 2>&1; then
    containers=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(postgres|redis|backend|frontend|traefik)" || true)
    if [ -n "$containers" ]; then
        echo "$containers"
    else
        print_warning "No relevant Docker containers running"
    fi
else
    print_warning "Docker not available"
fi

# Performance check
print_status "Checking response times..."
if command -v curl >/dev/null 2>&1; then
    health_time=$(curl -o /dev/null -s -w "%{time_total}" "$BACKEND_URL/health" 2>/dev/null || echo "N/A")
    products_time=$(curl -o /dev/null -s -w "%{time_total}" "$BACKEND_URL/api/v1/products" 2>/dev/null || echo "N/A")
    
    echo "  Health endpoint: ${health_time}s"
    echo "  Products endpoint: ${products_time}s"
fi

echo ""
echo "🎯 Health Check Summary"
echo "======================"
echo "Run this script regularly to monitor system health."
echo "For continuous monitoring, consider setting up a cron job:"
echo "  */5 * * * * /path/to/health-check.sh >> /var/log/astro-health.log"
