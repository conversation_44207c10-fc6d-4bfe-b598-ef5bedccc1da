#!/bin/bash

# =============================================================================
# VPS Monitoring Script
# =============================================================================
# This script monitors the VPS deployment status and health

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
VPS_USER="servomo"
VPS_IP="*************"
VPS_HOST="${VPS_USER}@${VPS_IP}"
CONTEXT_NAME="astro-vps"

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Check VPS system status
check_vps_system() {
    print_header "VPS System Status"
    
    print_info "Checking VPS connectivity..."
    if ssh -o ConnectTimeout=5 "$VPS_HOST" exit 2>/dev/null; then
        print_success "VPS is accessible"
    else
        print_error "VPS is not accessible"
        return 1
    fi
    
    print_info "System information:"
    ssh "$VPS_HOST" << 'EOF'
        echo "Hostname: $(hostname)"
        echo "Uptime: $(uptime -p)"
        echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
        echo "Memory: $(free -h | grep Mem | awk '{print $3"/"$2" ("$3/$2*100"%)"}')"
        echo "Disk: $(df -h / | tail -1 | awk '{print $3"/"$2" ("$5")"}')"
EOF
}

# Check Docker status
check_docker_status() {
    print_header "Docker Status"
    
    print_info "Checking Docker service..."
    if ssh "$VPS_HOST" "systemctl is-active docker" &>/dev/null; then
        print_success "Docker service is running"
    else
        print_error "Docker service is not running"
        return 1
    fi
    
    print_info "Docker version:"
    ssh "$VPS_HOST" "docker version --format 'Client: {{.Client.Version}}, Server: {{.Server.Version}}'"
    
    print_info "Docker system info:"
    ssh "$VPS_HOST" "docker system df"
}

# Check container status
check_containers() {
    print_header "Container Status"
    
    if ! docker context ls | grep -q "$CONTEXT_NAME"; then
        print_error "Docker context '$CONTEXT_NAME' not found"
        return 1
    fi
    
    print_info "Container status:"
    docker --context "$CONTEXT_NAME" ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.RunningFor}}"
    
    echo ""
    print_info "Container resource usage:"
    docker --context "$CONTEXT_NAME" stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Check service health
check_service_health() {
    print_header "Service Health Checks"
    
    # Check backend health
    print_info "Checking backend health..."
    if docker --context "$CONTEXT_NAME" exec astro_backend_prod curl -f http://localhost:8000/health &>/dev/null; then
        print_success "Backend is healthy"
    else
        print_error "Backend health check failed"
    fi
    
    # Check database health
    print_info "Checking database health..."
    if docker --context "$CONTEXT_NAME" exec astro_postgres_prod pg_isready -U astro_user &>/dev/null; then
        print_success "Database is healthy"
    else
        print_error "Database health check failed"
    fi
    
    # Check Redis health
    print_info "Checking Redis health..."
    if docker --context "$CONTEXT_NAME" exec astro_redis_prod redis-cli ping | grep -q "PONG"; then
        print_success "Redis is healthy"
    else
        print_error "Redis health check failed"
    fi
}

# Check logs for errors
check_logs() {
    print_header "Recent Log Analysis"
    
    print_info "Recent backend errors (last 50 lines):"
    docker --context "$CONTEXT_NAME" logs --tail 50 astro_backend_prod 2>&1 | grep -i "error\|panic\|fatal" || print_success "No recent errors found"
    
    echo ""
    print_info "Recent database errors (last 50 lines):"
    docker --context "$CONTEXT_NAME" logs --tail 50 astro_postgres_prod 2>&1 | grep -i "error\|fatal" || print_success "No recent database errors found"
}

# Check network connectivity
check_network() {
    print_header "Network Connectivity"
    
    print_info "Checking external connectivity from VPS..."
    if ssh "$VPS_HOST" "curl -s --max-time 5 https://google.com" &>/dev/null; then
        print_success "External connectivity is working"
    else
        print_warning "External connectivity issues detected"
    fi
    
    print_info "Checking service ports:"
    ssh "$VPS_HOST" << 'EOF'
        echo "Port 80 (HTTP): $(ss -tuln | grep ':80 ' && echo 'Open' || echo 'Closed')"
        echo "Port 443 (HTTPS): $(ss -tuln | grep ':443 ' && echo 'Open' || echo 'Closed')"
        echo "Port 8000 (Backend): $(ss -tuln | grep ':8000 ' && echo 'Open' || echo 'Closed')"
        echo "Port 8080 (Traefik): $(ss -tuln | grep ':8080 ' && echo 'Open' || echo 'Closed')"
EOF
}

# Show resource usage
show_resource_usage() {
    print_header "Resource Usage"
    
    print_info "CPU and Memory usage:"
    ssh "$VPS_HOST" << 'EOF'
        echo "CPU Usage:"
        top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4"%"}'
        
        echo "Memory Usage:"
        free -h | grep Mem | awk '{print "Used: "$3" / Total: "$2" ("$3/$2*100"%)"}'
        
        echo "Top processes by CPU:"
        ps aux --sort=-%cpu | head -6
        
        echo "Top processes by Memory:"
        ps aux --sort=-%mem | head -6
EOF
}

# Generate monitoring report
generate_report() {
    print_header "Monitoring Report"
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="vps_monitoring_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "VPS Monitoring Report - $timestamp"
        echo "========================================"
        echo ""
        
        echo "VPS Information:"
        echo "Host: $VPS_HOST"
        echo "IP: $VPS_IP"
        echo ""
        
        echo "System Status:"
        ssh "$VPS_HOST" "uptime && free -h && df -h /"
        echo ""
        
        echo "Docker Containers:"
        docker --context "$CONTEXT_NAME" ps
        echo ""
        
        echo "Container Stats:"
        docker --context "$CONTEXT_NAME" stats --no-stream
        echo ""
        
        echo "Recent Backend Logs:"
        docker --context "$CONTEXT_NAME" logs --tail 20 astro_backend_prod
        
    } > "$report_file"
    
    print_success "Monitoring report saved: $report_file"
}

# Show help
show_help() {
    echo "VPS Monitoring Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  --system      Check VPS system status"
    echo "  --docker      Check Docker status"
    echo "  --containers  Check container status"
    echo "  --health      Check service health"
    echo "  --logs        Check recent logs"
    echo "  --network     Check network connectivity"
    echo "  --resources   Show resource usage"
    echo "  --report      Generate monitoring report"
    echo "  --all         Run all checks (default)"
    echo "  --help        Show this help"
}

# Main monitoring function
main() {
    echo -e "${BLUE}📊 VPS Monitoring Dashboard${NC}"
    echo -e "${BLUE}===========================${NC}"
    echo ""
    echo -e "${CYAN}Target VPS: ${YELLOW}$VPS_HOST${NC}"
    echo -e "${CYAN}Timestamp: ${YELLOW}$(date)${NC}"
    echo ""
    
    check_vps_system
    check_docker_status
    check_containers
    check_service_health
    check_logs
    check_network
    show_resource_usage
    
    echo ""
    print_success "Monitoring completed"
    print_info "For continuous monitoring, run: watch -n 30 ./scripts/monitor-vps.sh --health"
}

# Handle command line arguments
case "${1:---all}" in
    --system)
        check_vps_system
        ;;
    --docker)
        check_docker_status
        ;;
    --containers)
        check_containers
        ;;
    --health)
        check_service_health
        ;;
    --logs)
        check_logs
        ;;
    --network)
        check_network
        ;;
    --resources)
        show_resource_usage
        ;;
    --report)
        generate_report
        ;;
    --help)
        show_help
        ;;
    --all|*)
        main
        ;;
esac
