#!/bin/bash

# Production Monitoring Script for Astro Works E-commerce Platform
# This script provides real-time monitoring of production services

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
REFRESH_INTERVAL=${REFRESH_INTERVAL:-5}
ENV_FILE=".env.prod"
COMPOSE_FILE="docker-compose.prod.yml"

# Load domain from env file if exists
if [ -f "$ENV_FILE" ]; then
    DOMAIN=$(grep "^DOMAIN=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"' 2>/dev/null || echo "astro-works.local")
else
    DOMAIN="astro-works.local"
fi

API_URL="https://${DOMAIN}/api/v1"
FRONTEND_URL="https://${DOMAIN}"
HEALTH_URL="https://${DOMAIN}/health"

# Functions
clear_screen() {
    clear
    echo -e "${BLUE}🔍 Astro Works Production Monitor${NC}"
    echo -e "${BLUE}=================================${NC}"
    echo -e "${CYAN}Domain: ${DOMAIN}${NC}"
    echo -e "${CYAN}Refresh: ${REFRESH_INTERVAL}s${NC} | ${YELLOW}Press Ctrl+C to exit${NC}"
    echo ""
}

check_service_health() {
    local service=$1
    local url=$2
    local timeout=${3:-5}
    
    if curl -s --max-time "$timeout" "$url" >/dev/null 2>&1; then
        echo -e "${GREEN}●${NC}"
    else
        echo -e "${RED}●${NC}"
    fi
}

get_container_status() {
    local container=$1
    local status=$(docker-compose -f "$COMPOSE_FILE" ps --services --filter "status=running" 2>/dev/null | grep "^$container$" || echo "")
    
    if [ -n "$status" ]; then
        echo -e "${GREEN}●${NC}"
    else
        echo -e "${RED}●${NC}"
    fi
}

get_container_stats() {
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" 2>/dev/null | \
    grep -E "(postgres|redis|backend|frontend|traefik)" | head -5
}

get_system_info() {
    # CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    
    # Memory usage
    local mem_info=$(free -m | awk 'NR==2{printf "%.1f%%", $3*100/$2}')
    
    # Disk usage
    local disk_usage=$(df -h / | awk 'NR==2{print $5}')
    
    # Load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    echo -e "${BLUE}System:${NC} CPU: ${cpu_usage}% | Memory: ${mem_info} | Disk: ${disk_usage} | Load: ${load_avg}"
}

get_response_times() {
    local health_time=$(curl -o /dev/null -s -w "%{time_total}" "$HEALTH_URL" 2>/dev/null || echo "N/A")
    local api_time=$(curl -o /dev/null -s -w "%{time_total}" "$API_URL/products" 2>/dev/null || echo "N/A")
    local frontend_time=$(curl -o /dev/null -s -w "%{time_total}" "$FRONTEND_URL" 2>/dev/null || echo "N/A")
    
    echo -e "${BLUE}Response Times:${NC} Health: ${health_time}s | API: ${api_time}s | Frontend: ${frontend_time}s"
}

get_database_info() {
    local db_connections=$(docker-compose -f "$COMPOSE_FILE" exec -T postgres \
        psql -U astro_user -d astro_ecommerce -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null | tr -d ' ' || echo "N/A")
    
    local db_size=$(docker-compose -f "$COMPOSE_FILE" exec -T postgres \
        psql -U astro_user -d astro_ecommerce -t -c "SELECT pg_size_pretty(pg_database_size('astro_ecommerce'));" 2>/dev/null | tr -d ' ' || echo "N/A")
    
    echo -e "${BLUE}Database:${NC} Connections: ${db_connections} | Size: ${db_size}"
}

get_redis_info() {
    local redis_memory=$(docker-compose -f "$COMPOSE_FILE" exec -T redis \
        redis-cli info memory 2>/dev/null | grep "used_memory_human" | cut -d':' -f2 | tr -d '\r' || echo "N/A")
    
    local redis_keys=$(docker-compose -f "$COMPOSE_FILE" exec -T redis \
        redis-cli dbsize 2>/dev/null | tr -d '\r' || echo "N/A")
    
    echo -e "${BLUE}Redis:${NC} Memory: ${redis_memory} | Keys: ${redis_keys}"
}

show_service_status() {
    echo -e "${PURPLE}📊 Service Status${NC}"
    echo "=================="
    
    printf "%-12s %-8s %-12s\n" "Service" "Status" "Health"
    printf "%-12s %-8s %-12s\n" "-------" "------" "------"
    
    # PostgreSQL
    printf "%-12s " "PostgreSQL"
    printf "%-8s " "$(get_container_status postgres)"
    printf "%-12s\n" "$(docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U astro_user 2>/dev/null && echo -e "${GREEN}●${NC}" || echo -e "${RED}●${NC}")"
    
    # Redis
    printf "%-12s " "Redis"
    printf "%-8s " "$(get_container_status redis)"
    printf "%-12s\n" "$(docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping 2>/dev/null >/dev/null && echo -e "${GREEN}●${NC}" || echo -e "${RED}●${NC}")"
    
    # Backend
    printf "%-12s " "Backend"
    printf "%-8s " "$(get_container_status backend)"
    printf "%-12s\n" "$(check_service_health "Backend" "$HEALTH_URL")"
    
    # Frontend
    printf "%-12s " "Frontend"
    printf "%-8s " "$(get_container_status frontend)"
    printf "%-12s\n" "$(check_service_health "Frontend" "$FRONTEND_URL")"
    
    # Traefik
    printf "%-12s " "Traefik"
    printf "%-8s " "$(get_container_status traefik)"
    printf "%-12s\n" "$(check_service_health "Traefik" "http://localhost:8080/ping")"
    
    echo ""
}

show_resource_usage() {
    echo -e "${PURPLE}💻 Resource Usage${NC}"
    echo "=================="
    
    get_system_info
    echo ""
    get_response_times
    echo ""
    get_database_info
    get_redis_info
    echo ""
    
    echo -e "${BLUE}Container Stats:${NC}"
    get_container_stats
    echo ""
}

show_recent_logs() {
    echo -e "${PURPLE}📋 Recent Logs (last 10 lines)${NC}"
    echo "==============================="
    
    # Backend logs
    echo -e "${CYAN}Backend:${NC}"
    docker-compose -f "$COMPOSE_FILE" logs --tail=3 backend 2>/dev/null | tail -3 || echo "No logs available"
    
    # Traefik logs
    echo -e "${CYAN}Traefik:${NC}"
    docker-compose -f "$COMPOSE_FILE" logs --tail=3 traefik 2>/dev/null | tail -3 || echo "No logs available"
    
    echo ""
}

show_alerts() {
    echo -e "${PURPLE}⚠️  Alerts${NC}"
    echo "=========="
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 80 ]; then
        echo -e "${RED}● High disk usage: ${disk_usage}%${NC}"
    fi
    
    # Check memory usage
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$mem_usage" -gt 80 ]; then
        echo -e "${RED}● High memory usage: ${mem_usage}%${NC}"
    fi
    
    # Check load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    if (( $(echo "$load_avg > 2.0" | bc -l 2>/dev/null || echo 0) )); then
        echo -e "${RED}● High system load: ${load_avg}${NC}"
    fi
    
    # Check if any containers are down
    local down_containers=$(docker-compose -f "$COMPOSE_FILE" ps --services --filter "status=exited" 2>/dev/null || echo "")
    if [ -n "$down_containers" ]; then
        echo -e "${RED}● Containers down: ${down_containers}${NC}"
    fi
    
    echo ""
}

# Main monitoring loop
monitor_loop() {
    while true; do
        clear_screen
        show_service_status
        show_resource_usage
        show_alerts
        show_recent_logs
        
        echo -e "${CYAN}Last updated: $(date)${NC}"
        echo -e "${YELLOW}Press Ctrl+C to exit, 'h' for help${NC}"
        
        sleep "$REFRESH_INTERVAL"
    done
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --once         Run once and exit (no loop)"
        echo "  --interval N   Set refresh interval in seconds (default: 5)"
        echo ""
        echo "Environment variables:"
        echo "  REFRESH_INTERVAL   Refresh interval in seconds"
        echo "  DOMAIN            Domain name for health checks"
        exit 0
        ;;
    --once)
        clear_screen
        show_service_status
        show_resource_usage
        show_alerts
        show_recent_logs
        exit 0
        ;;
    --interval)
        if [ -n "$2" ] && [ "$2" -gt 0 ] 2>/dev/null; then
            REFRESH_INTERVAL="$2"
        else
            echo "Invalid interval: $2"
            exit 1
        fi
        ;;
esac

# Trap Ctrl+C
trap 'echo -e "\n${GREEN}Monitoring stopped.${NC}"; exit 0' INT

# Start monitoring
monitor_loop
