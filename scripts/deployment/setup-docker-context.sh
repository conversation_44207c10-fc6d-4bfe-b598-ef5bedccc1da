#!/bin/bash

# =============================================================================
# Docker Context Setup Script for VPS Auto Deployment
# =============================================================================
# This script sets up Docker context for remote deployment to VPS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# VPS Configuration
VPS_USER="astro"
VPS_IP="**************"
VPS_HOST="${VPS_USER}@${VPS_IP}"
CONTEXT_NAME="astro-vps"

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    print_success "Docker is installed"
    
    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available"
        exit 1
    fi
    print_success "Docker Compose is available"
    
    # Check SSH connectivity
    print_info "Testing SSH connectivity to VPS..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$VPS_HOST" exit 2>/dev/null; then
        print_success "SSH connectivity to VPS is working"
    else
        print_error "Cannot connect to VPS via SSH"
        print_info "Please ensure:"
        print_info "1. SSH key is added to VPS: ssh-copy-id $VPS_HOST"
        print_info "2. VPS is accessible: ping $VPS_IP"
        print_info "3. SSH service is running on VPS"
        exit 1
    fi
}

# Setup SSH key if needed
setup_ssh_key() {
    print_header "SSH Key Setup"
    
    if [ ! -f ~/.ssh/id_rsa ]; then
        print_info "Generating SSH key..."
        ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
        print_success "SSH key generated"
    else
        print_success "SSH key already exists"
    fi
    
    print_info "Testing SSH key authentication..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$VPS_HOST" exit 2>/dev/null; then
        print_success "SSH key authentication is working"
    else
        print_warning "SSH key authentication failed"
        print_info "Copying SSH key to VPS..."
        ssh-copy-id "$VPS_HOST"
        print_success "SSH key copied to VPS"
    fi
}

# Install Docker on VPS if needed
install_docker_on_vps() {
    print_header "Installing Docker on VPS"
    
    print_info "Checking if Docker is installed on VPS..."
    if ssh "$VPS_HOST" "command -v docker" &>/dev/null; then
        print_success "Docker is already installed on VPS"
        return
    fi
    
    print_info "Installing Docker on VPS..."
    ssh "$VPS_HOST" << 'EOF'
        # Update package index
        sudo apt-get update
        
        # Install prerequisites
        sudo apt-get install -y \
            ca-certificates \
            curl \
            gnupg \
            lsb-release
        
        # Add Docker GPG key
        sudo mkdir -p /etc/apt/keyrings
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
        
        # Add Docker repository
        echo \
          "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
          $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
        
        # Install Docker
        sudo apt-get update
        sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
        
        # Add user to docker group
        sudo usermod -aG docker $USER
        
        # Enable Docker service
        sudo systemctl enable docker
        sudo systemctl start docker
        
        echo "Docker installation completed"
EOF
    
    print_success "Docker installed on VPS"
    print_warning "Please log out and log back in to VPS for group changes to take effect"
}

# Create Docker context
create_docker_context() {
    print_header "Creating Docker Context"
    
    # Remove existing context if it exists
    if docker context ls | grep -q "$CONTEXT_NAME"; then
        print_info "Removing existing Docker context..."
        docker context rm "$CONTEXT_NAME" -f
    fi
    
    print_info "Creating Docker context for VPS..."
    docker context create "$CONTEXT_NAME" \
        --docker "host=ssh://$VPS_HOST" \
        --description "Astro Works VPS Production Server"
    
    print_success "Docker context '$CONTEXT_NAME' created"
    
    # Test the context
    print_info "Testing Docker context..."
    if docker --context "$CONTEXT_NAME" version &>/dev/null; then
        print_success "Docker context is working"
    else
        print_error "Docker context test failed"
        print_info "Make sure Docker is running on VPS and user is in docker group"
        exit 1
    fi
}

# Setup VPS directories
setup_vps_directories() {
    print_header "Setting up VPS Directories"
    
    print_info "Creating project directories on VPS..."
    ssh "$VPS_HOST" << 'EOF'
        # Create project directory in /srv/app
        sudo mkdir -p /srv/app/astro-works/{data,logs,backups,ssl}

        # Create data directories
        sudo mkdir -p /srv/app/astro-works/data/{postgres,redis,uploads}

        # Set ownership to current user
        sudo chown -R $USER:$USER /srv/app/astro-works

        # Set permissions
        chmod 755 /srv/app/astro-works
        chmod 755 /srv/app/astro-works/data
        chmod 777 /srv/app/astro-works/data/uploads

        # Create symlink for convenience
        ln -sf /srv/app/astro-works ~/astro-works

        echo "Directories created successfully in /srv/app/astro-works"
EOF
    
    print_success "VPS directories setup completed"
}

# Create deployment configuration
create_deployment_config() {
    print_header "Creating Deployment Configuration"
    
    # Create VPS-specific environment file
    cat > .env.vps << EOF
# =============================================================================
# VPS PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================

# Domain Configuration
DOMAIN=astrokabinet.id
API_DOMAIN=api.astrokabinet.id
FRONTEND_URL=https://astrokabinet.id
API_URL=https://api.astrokabinet.id/api/v1

# Database Configuration
POSTGRES_USER=astro_user
POSTGRES_PASSWORD=astro_secure_password_2024
POSTGRES_DB=astro_ecommerce
DATABASE_URL=**************************************************************/astro_ecommerce

# Redis Configuration
REDIS_URL=redis://redis:6379

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-for-production-min-32-chars
ADMIN_DEFAULT_EMAIL=<EMAIL>
ADMIN_DEFAULT_PASSWORD=admin_secure_2024

# Application Configuration
RUST_LOG=info
ENVIRONMENT=production

# Business Configuration
WHATSAPP_PHONE_NUMBER=***********
BANK_NAME=BCA
BANK_ACCOUNT_NUMBER=**********
BANK_ACCOUNT_NAME=Astro Works Indonesia PT

# SSL Configuration
ACME_EMAIL=<EMAIL>

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
EOF
    
    print_success "VPS environment configuration created (.env.vps)"
    print_warning "Please update the passwords and secrets in .env.vps"
}

# Show usage instructions
show_usage() {
    print_header "Docker Context Setup Completed"
    
    echo -e "${GREEN}🎉 Docker context setup completed successfully!${NC}"
    echo ""
    echo -e "${CYAN}Available Commands:${NC}"
    echo ""
    echo -e "${YELLOW}Context Management:${NC}"
    echo "  docker context ls                    # List all contexts"
    echo "  docker context use $CONTEXT_NAME     # Switch to VPS context"
    echo "  docker context use default          # Switch back to local"
    echo ""
    echo -e "${YELLOW}Remote Deployment:${NC}"
    echo "  make deploy-vps                     # Deploy to VPS"
    echo "  make logs-vps                       # View VPS logs"
    echo "  make status-vps                     # Check VPS status"
    echo ""
    echo -e "${YELLOW}Manual Commands:${NC}"
    echo "  docker --context $CONTEXT_NAME ps   # List containers on VPS"
    echo "  docker --context $CONTEXT_NAME logs astro_backend_prod"
    echo "  docker --context $CONTEXT_NAME exec astro_backend_prod /bin/bash"
    echo ""
    echo -e "${CYAN}Next Steps:${NC}"
    echo "1. Update passwords in .env.vps"
    echo "2. Run: make deploy-vps"
    echo "3. Configure domain DNS to point to $VPS_IP"
    echo "4. Setup SSL certificates"
    echo ""
    echo -e "${GREEN}VPS Access:${NC}"
    echo "  SSH: ssh $VPS_HOST"
    echo "  IP: $VPS_IP"
    echo "  User: $VPS_USER"
}

# Main execution
main() {
    echo -e "${BLUE}🐳 Docker Context Setup for VPS Auto Deployment${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo ""
    echo -e "${CYAN}VPS Details:${NC}"
    echo -e "  Host: ${YELLOW}$VPS_HOST${NC}"
    echo -e "  IP: ${YELLOW}$VPS_IP${NC}"
    echo -e "  Context: ${YELLOW}$CONTEXT_NAME${NC}"
    echo ""
    
    check_prerequisites
    setup_ssh_key
    install_docker_on_vps
    create_docker_context
    setup_vps_directories
    create_deployment_config
    show_usage
}

# Run main function
main "$@"
