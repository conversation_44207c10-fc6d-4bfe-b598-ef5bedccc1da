#!/bin/bash

# Astro Works E-commerce Setup Script
set -e

echo "🚀 Setting up Astro Works E-commerce Platform"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
print_status "Checking prerequisites..."

# Check Docker
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check Rust
if ! command -v cargo &> /dev/null; then
    print_error "Rust is not installed. Please install Rust first."
    exit 1
fi

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

print_success "All prerequisites are installed"

# Step 1: Start database services
print_status "Starting database services..."
docker-compose up -d postgres redis

# Wait for PostgreSQL to be ready
print_status "Waiting for PostgreSQL to be ready..."
sleep 10

# Test database connection
until docker-compose exec -T postgres pg_isready -U astro_user -d astro_ecommerce; do
    print_status "Waiting for PostgreSQL..."
    sleep 2
done

print_success "PostgreSQL is ready"

# Step 2: Set up environment variables
print_status "Setting up environment variables..."
if [ ! -f "be_astro/.env" ]; then
    cp be_astro/.env.example be_astro/.env
    print_success "Created backend .env file"
else
    print_warning "Backend .env file already exists"
fi

# Step 3: Build and run migrations
print_status "Building Rust backend..."
cd be_astro
cargo build --release

print_status "Running database migrations..."
cargo run --bin migrate

print_status "Verifying database setup..."
cargo run --bin verify_db

cd ..

# Step 4: Load sample data
print_status "Loading sample data..."
docker-compose exec -T postgres psql -U astro_user -d astro_ecommerce -f /docker-entrypoint-initdb.d/sample_data.sql || {
    print_warning "Sample data file not found in container, loading from host..."
    docker-compose exec -T postgres psql -U astro_user -d astro_ecommerce < database/sample_data.sql
}

print_success "Sample data loaded"

# Step 5: Set up frontend
print_status "Setting up Svelte frontend..."
cd fe_astro

if [ ! -f ".env" ]; then
    echo "VITE_API_URL=http://localhost:8000/api/v1" > .env
    print_success "Created frontend .env file"
fi

print_status "Installing frontend dependencies..."
npm install

print_status "Building frontend..."
npm run build

cd ..

# Step 6: Start the backend server
print_status "Starting backend server..."
cd be_astro
cargo run &
BACKEND_PID=$!
cd ..

# Wait for backend to start
print_status "Waiting for backend to start..."
sleep 5

# Test backend health
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    print_success "Backend server is running"
else
    print_error "Backend server failed to start"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# Step 7: Start the frontend server
print_status "Starting frontend server..."
cd fe_astro
npm run dev &
FRONTEND_PID=$!
cd ..

# Wait for frontend to start
print_status "Waiting for frontend to start..."
sleep 5

print_success "Setup completed successfully!"
echo ""
echo "🎉 Astro Works E-commerce Platform is now running!"
echo "================================================"
echo "Frontend: http://localhost:5173"
echo "Backend API: http://localhost:8000"
echo "Health Check: http://localhost:8000/health"
echo ""
echo "Database:"
echo "  Host: localhost:5432"
echo "  Database: astro_ecommerce"
echo "  Username: astro_user"
echo ""
echo "Redis: localhost:6379"
echo ""
echo "To stop the services:"
echo "  kill $BACKEND_PID $FRONTEND_PID"
echo "  docker-compose down"
echo ""
echo "Press Ctrl+C to stop all services"

# Keep script running and handle cleanup
cleanup() {
    print_status "Shutting down services..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    docker-compose down
    print_success "All services stopped"
}

trap cleanup EXIT INT TERM

# Wait for user to stop
wait
