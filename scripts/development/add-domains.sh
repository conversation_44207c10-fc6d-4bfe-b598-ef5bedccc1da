#!/bin/bash

# Add domains to /etc/hosts for local development
# This script requires sudo privileges

echo "🌐 Adding Local Development Domains"
echo "==================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ This script requires sudo privileges${NC}"
    echo ""
    echo "Please run with:"
    echo "sudo ./add-domains.sh"
    echo ""
    echo "Or manually add these lines to /etc/hosts:"
    echo "127.0.0.1    astrokabinet.id api.astrokabinet.id astrokabinet.dev api.astrokabinet.dev"
    exit 1
fi

echo -e "${BLUE}📝 Adding domains to /etc/hosts...${NC}"

# Backup original hosts file
BACKUP_FILE="/etc/hosts.backup.$(date +%Y%m%d_%H%M%S)"
cp /etc/hosts "$BACKUP_FILE"
echo -e "${GREEN}✅ Backed up /etc/hosts to $BACKUP_FILE${NC}"

# Define domains
DOMAINS=(
    "astrokabinet.id"
    "api.astrokabinet.id"
    "astrokabinet.dev"
    "api.astrokabinet.dev"
)

# Check if domains already exist
DOMAINS_EXIST=false
for domain in "${DOMAINS[@]}"; do
    if grep -q "$domain" /etc/hosts; then
        echo -e "${YELLOW}⚠️  Domain $domain already exists in /etc/hosts${NC}"
        DOMAINS_EXIST=true
    fi
done

if [ "$DOMAINS_EXIST" = true ]; then
    echo ""
    echo -e "${YELLOW}Some domains already exist. Continue anyway? [y/N]${NC}"
    read -r response
    if [ "$response" != "y" ] && [ "$response" != "Y" ]; then
        echo -e "${BLUE}ℹ️  Operation cancelled${NC}"
        exit 0
    fi
fi

# Add domains to hosts file
echo "" >> /etc/hosts
echo "# Astro Cabinet Local Development Domains - $(date)" >> /etc/hosts

for domain in "${DOMAINS[@]}"; do
    if ! grep -q "$domain" /etc/hosts; then
        echo "127.0.0.1    $domain" >> /etc/hosts
        echo -e "${GREEN}✅ Added $domain${NC}"
    fi
done

echo ""
echo -e "${GREEN}🎉 Domains added successfully!${NC}"
echo ""
echo -e "${BLUE}🌐 You can now access:${NC}"
echo "   Frontend: http://astrokabinet.id:5174"
echo "   Frontend: http://astrokabinet.dev:5174"
echo "   Backend:  http://api.astrokabinet.id:8000"
echo "   Backend:  http://api.astrokabinet.dev:8000"
echo ""
echo -e "${BLUE}📋 To remove these domains later:${NC}"
echo "   sudo cp $BACKUP_FILE /etc/hosts"
echo ""
echo -e "${GREEN}✨ Ready for development with custom domains!${NC}"
