#!/bin/bash

# Script to replace sqlx::query! with sqlx::query for Docker build compatibility

echo "🔧 Fixing SQLx queries for Docker build..."

# Replace sqlx::query! with sqlx::query in all repository files
find be_astro/src/infrastructure/repositories -name "*.rs" -type f -exec sed -i 's/sqlx::query!/sqlx::query/g' {} \;

# Replace parameter placeholders with .bind() calls
# This is a simplified approach - we'll handle specific cases manually if needed

echo "✅ SQLx queries fixed for Docker build"
echo "Note: Manual verification may be needed for complex queries"
