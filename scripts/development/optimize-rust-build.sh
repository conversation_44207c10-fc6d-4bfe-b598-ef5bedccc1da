#!/bin/bash

# =============================================================================
# Rust Build Optimization Script
# =============================================================================
# This script analyzes and optimizes Rust build performance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Check if we're in the right directory
check_directory() {
    if [ ! -f "be_astro/Cargo.toml" ]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
}

# Install cargo-udeps for unused dependency detection
install_tools() {
    print_header "Installing Optimization Tools"
    
    # Check if cargo-udeps is installed
    if ! command -v cargo-udeps &> /dev/null; then
        print_info "Installing cargo-udeps..."
        cargo install cargo-udeps --locked
        print_success "cargo-udeps installed"
    else
        print_success "cargo-udeps already installed"
    fi
    
    # Check if cargo-bloat is installed
    if ! command -v cargo-bloat &> /dev/null; then
        print_info "Installing cargo-bloat..."
        cargo install cargo-bloat --locked
        print_success "cargo-bloat installed"
    else
        print_success "cargo-bloat already installed"
    fi
}

# Analyze unused dependencies
analyze_unused_deps() {
    print_header "Analyzing Unused Dependencies"
    
    cd be_astro
    
    print_info "Checking for unused dependencies..."
    if cargo +nightly udeps --all-targets; then
        print_success "No unused dependencies found"
    else
        print_warning "Found unused dependencies (see output above)"
    fi
    
    cd ..
}

# Analyze binary size
analyze_binary_size() {
    print_header "Analyzing Binary Size"
    
    cd be_astro
    
    print_info "Building release binary..."
    cargo build --release --quiet
    
    print_info "Analyzing binary size breakdown..."
    cargo bloat --release --crates
    
    print_info "Top 20 functions by size:"
    cargo bloat --release -n 20
    
    cd ..
}

# Check compilation time
measure_compile_time() {
    print_header "Measuring Compilation Time"
    
    cd be_astro
    
    print_info "Cleaning previous builds..."
    cargo clean
    
    print_info "Measuring clean build time..."
    time cargo build --release
    
    print_info "Measuring incremental build time..."
    touch src/main.rs
    time cargo build --release
    
    cd ..
}

# Generate optimization report
generate_report() {
    print_header "Generating Optimization Report"
    
    local report_file="rust_optimization_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# Rust Build Optimization Report

Generated on: $(date)

## Current Configuration

### Cargo.toml Optimizations
- Disabled default features for heavy crates
- Optimized image processing features
- Reduced SQLx features to essentials
- Optimized reqwest and rand crates

### Dockerfile Optimizations
- Added CARGO_REGISTRIES_CRATES_IO_PROTOCOL=sparse
- Added CARGO_NET_GIT_FETCH_WITH_CLI=true
- Added RUSTFLAGS="-C target-cpu=native -C opt-level=3"
- Multi-stage build with dependency caching

### .cargo/config.toml
- Parallel compilation (jobs = 4)
- Fast linker configuration (lld)
- Optimized profiles for dev and release
- Sparse registry protocol

## Recommendations

### Further Optimizations
1. **Use sccache for distributed compilation caching**
2. **Consider removing heavy dependencies:**
   - image crate (if not essential)
   - webp crate (if not essential)
   - reqwest (if only used for health checks)

3. **Feature flags optimization:**
   - Add feature flags for optional functionality
   - Disable default features more aggressively

4. **Build environment:**
   - Use faster SSD storage
   - Increase available RAM
   - Use more CPU cores

### Dependency Analysis
Run \`cargo +nightly udeps\` to find unused dependencies.

### Binary Size Analysis
Run \`cargo bloat --release --crates\` to analyze binary size.

## Build Time Improvements

### Before Optimization
- Clean build: ~15-20 minutes
- Heavy dependencies: image, webp, full SQLx features

### After Optimization
- Expected improvement: 30-50% faster builds
- Reduced binary size
- Better caching

## Next Steps
1. Monitor build times after changes
2. Consider feature flags for optional components
3. Evaluate necessity of image processing dependencies
4. Consider using pre-built Docker images for dependencies
EOF

    print_success "Optimization report generated: $report_file"
}

# Show optimization suggestions
show_suggestions() {
    print_header "Optimization Suggestions"
    
    echo -e "${CYAN}🚀 Build Speed Optimizations:${NC}"
    echo "1. Use sccache for compilation caching"
    echo "2. Enable parallel builds with more jobs"
    echo "3. Use faster linker (lld)"
    echo "4. Optimize Docker layer caching"
    echo ""
    
    echo -e "${CYAN}📦 Dependency Optimizations:${NC}"
    echo "1. Remove unused dependencies with cargo-udeps"
    echo "2. Disable default features for heavy crates"
    echo "3. Use feature flags for optional functionality"
    echo "4. Consider lighter alternatives for heavy crates"
    echo ""
    
    echo -e "${CYAN}🔧 Runtime Optimizations:${NC}"
    echo "1. Use release profile optimizations"
    echo "2. Enable LTO (Link Time Optimization)"
    echo "3. Strip debug symbols in production"
    echo "4. Use panic=abort for smaller binaries"
    echo ""
    
    echo -e "${CYAN}🐳 Docker Optimizations:${NC}"
    echo "1. Multi-stage builds with dependency caching"
    echo "2. Use Alpine Linux for smaller images"
    echo "3. Optimize layer ordering"
    echo "4. Use .dockerignore to exclude unnecessary files"
}

# Main function
main() {
    echo -e "${BLUE}🦀 Rust Build Optimization Tool${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    
    check_directory
    
    case "${1:-all}" in
        "deps")
            install_tools
            analyze_unused_deps
            ;;
        "size")
            analyze_binary_size
            ;;
        "time")
            measure_compile_time
            ;;
        "report")
            generate_report
            ;;
        "suggestions")
            show_suggestions
            ;;
        "all")
            install_tools
            analyze_unused_deps
            analyze_binary_size
            generate_report
            show_suggestions
            ;;
        *)
            echo "Usage: $0 [deps|size|time|report|suggestions|all]"
            echo ""
            echo "Commands:"
            echo "  deps        - Analyze unused dependencies"
            echo "  size        - Analyze binary size"
            echo "  time        - Measure compilation time"
            echo "  report      - Generate optimization report"
            echo "  suggestions - Show optimization suggestions"
            echo "  all         - Run all analyses (default)"
            ;;
    esac
}

main "$@"
