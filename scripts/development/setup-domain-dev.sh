#!/bin/bash

# Setup domain untuk development dengan existing servers
# Menggunakan development servers yang sudah berjalan

echo "🚀 Setup Domain untuk Development"
echo "================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Check if SSL certificates exist
if [ ! -f "ssl/certs/server.crt" ]; then
    echo -e "${BLUE}🔒 Generating SSL certificates...${NC}"
    ./generate-ssl.sh
    echo ""
fi

echo -e "${BLUE}📋 Domain Setup Instructions${NC}"
echo ""
echo "Please add these lines to your /etc/hosts file:"
echo ""
echo -e "${YELLOW}127.0.0.1    astrokabinet.id${NC}"
echo -e "${YELLOW}127.0.0.1    api.astrokabinet.id${NC}"
echo -e "${YELLOW}127.0.0.1    astrokabinet.dev${NC}"
echo -e "${YELLOW}127.0.0.1    api.astrokabinet.dev${NC}"
echo ""
echo "Quick command to add domains:"
echo 'echo "127.0.0.1    astrokabinet.id api.astrokabinet.id astrokabinet.dev api.astrokabinet.dev" | sudo tee -a /etc/hosts'
echo ""

# Check current services
echo -e "${BLUE}🔍 Checking Current Services${NC}"
echo ""

# Check backend
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend is running at http://localhost:8000${NC}"
    BACKEND_RUNNING=true
else
    echo -e "${RED}❌ Backend is not running${NC}"
    echo "   Start with: cd be_astro && cargo run --bin be_astro"
    BACKEND_RUNNING=false
fi

# Check frontend
if curl -s http://localhost:5174 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Frontend is running at http://localhost:5174${NC}"
    FRONTEND_RUNNING=true
else
    echo -e "${RED}❌ Frontend is not running${NC}"
    echo "   Start with: cd fe_astro && npm run dev"
    FRONTEND_RUNNING=false
fi

echo ""

# Test API
if [ "$BACKEND_RUNNING" = true ]; then
    PRODUCT_COUNT=$(curl -s "http://localhost:8000/api/v1/products?per_page=1000" | jq '. | length' 2>/dev/null || echo "0")
    echo -e "${BLUE}📊 API Status:${NC}"
    echo "   Products available: $PRODUCT_COUNT"
    echo "   API endpoint: http://localhost:8000/api/v1/products"
    echo ""
fi

echo -e "${BLUE}🌐 Access URLs${NC}"
echo ""
echo -e "${GREEN}Current (localhost):${NC}"
echo "   Frontend: http://localhost:5174"
echo "   Backend:  http://localhost:8000"
echo ""
echo -e "${GREEN}After adding domains to /etc/hosts:${NC}"
echo "   Frontend: http://astrokabinet.id:5174"
echo "   Frontend: http://astrokabinet.dev:5174"
echo "   Backend:  http://api.astrokabinet.id:8000"
echo "   Backend:  http://api.astrokabinet.dev:8000"
echo ""

echo -e "${BLUE}🔧 Configuration Status${NC}"
echo ""
echo "✅ SSL certificates generated"
echo "✅ CORS configured for localhost:5174"
echo "✅ Homepage configured to show all products"
if [ "$BACKEND_RUNNING" = true ] && [ "$FRONTEND_RUNNING" = true ]; then
    echo "✅ Both services are running"
else
    echo "⚠️  Some services need to be started"
fi
echo ""

echo -e "${BLUE}📝 Next Steps${NC}"
echo ""
echo "1. Add domains to /etc/hosts (copy command above)"
echo "2. Ensure both backend and frontend are running"
echo "3. Test the application:"
echo "   - Visit: http://localhost:5174"
echo "   - Or with domain: http://astrokabinet.id:5174"
echo "4. Verify all 7 products are visible on homepage"
echo "5. Test product detail pages and accessories"
echo ""

if [ "$BACKEND_RUNNING" = true ] && [ "$FRONTEND_RUNNING" = true ]; then
    echo -e "${GREEN}🎉 Development environment is ready!${NC}"
    echo ""
    echo "You can now:"
    echo "• Access the application at http://localhost:5174"
    echo "• View all products (7 total including accessories)"
    echo "• No CORS issues"
    echo "• Use custom domains after adding to /etc/hosts"
else
    echo -e "${YELLOW}⚠️  Please start the missing services first${NC}"
fi
