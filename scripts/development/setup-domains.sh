#!/bin/bash

# Setup local domains for Astro Cabinet project
# This script adds domain entries to /etc/hosts for local development

echo "Setting up local domains for Astro Cabinet..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Please run this script with sudo:"
    echo "sudo ./setup-domains.sh"
    exit 1
fi

# Backup original hosts file
cp /etc/hosts /etc/hosts.backup.$(date +%Y%m%d_%H%M%S)

# Define domains
DOMAINS=(
    "astrokabinet.id"
    "api.astrokabinet.id"
    "traefik.astrokabinet.id"
    "astrokabinet.dev"
    "api.astrokabinet.dev"
    "traefik.astrokabinet.dev"
)

# Add domains to hosts file
echo "" >> /etc/hosts
echo "# Astro Cabinet Local Development Domains" >> /etc/hosts

for domain in "${DOMAINS[@]}"; do
    # Check if domain already exists
    if grep -q "$domain" /etc/hosts; then
        echo "Domain $domain already exists in /etc/hosts"
    else
        echo "127.0.0.1    $domain" >> /etc/hosts
        echo "Added $domain to /etc/hosts"
    fi
done

echo ""
echo "Domain setup complete!"
echo ""
echo "You can now access:"
echo "  Frontend: https://astrokabinet.id or https://astrokabinet.dev"
echo "  API:      https://api.astrokabinet.id or https://api.astrokabinet.dev"
echo "  Traefik:  https://traefik.astrokabinet.id or https://traefik.astrokabinet.dev"
echo ""
echo "To start the services, run:"
echo "  docker-compose -f docker-compose.traefik.yml up -d"
echo ""
echo "To remove these domains later, restore from backup:"
echo "  sudo cp /etc/hosts.backup.* /etc/hosts"
