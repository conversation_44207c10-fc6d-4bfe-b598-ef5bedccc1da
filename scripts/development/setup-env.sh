#!/bin/bash

# =============================================================================
# Environment Setup Script
# =============================================================================
# This script helps set up environment files for development or production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

generate_jwt_secret() {
    openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64 | tr -d '\n'
}

generate_password() {
    openssl rand -base64 16 2>/dev/null || head -c 16 /dev/urandom | base64 | tr -d '\n'
}

prompt_user() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
    fi
    
    eval "$var_name='$input'"
}

setup_development() {
    print_header "Setting up Development Environment"
    
    # Copy template files
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_success "Created global .env file"
    else
        print_warning "Global .env file already exists"
    fi
    
    if [ ! -f "be_astro/.env" ]; then
        cp be_astro/.env.example be_astro/.env
        print_success "Created backend .env file"
    else
        print_warning "Backend .env file already exists"
    fi
    
    if [ ! -f "fe_astro/.env" ]; then
        cp fe_astro/.env.example fe_astro/.env
        print_success "Created frontend .env file"
    else
        print_warning "Frontend .env file already exists"
    fi
    
    # Generate secure secrets
    print_info "Generating secure secrets..."
    jwt_secret=$(generate_jwt_secret)
    admin_password=$(generate_password)
    
    # Update backend .env with secure values
    sed -i.bak "s/JWT_SECRET=.*/JWT_SECRET=$jwt_secret/" be_astro/.env
    sed -i.bak "s/ADMIN_DEFAULT_PASSWORD=.*/ADMIN_DEFAULT_PASSWORD=$admin_password/" be_astro/.env
    
    print_success "Generated secure JWT secret"
    print_success "Generated secure admin password: $admin_password"
    
    print_info "Development environment setup complete!"
    print_warning "Remember to update WhatsApp phone number and bank details in the .env files"
}

setup_production() {
    print_header "Setting up Production Environment"
    
    print_warning "Production setup requires manual configuration of sensitive values"
    
    # Copy production template
    if [ ! -f ".env" ]; then
        cp .env.production .env
        print_success "Created production .env file"
    else
        print_warning "Production .env file already exists"
    fi
    
    # Interactive configuration
    print_info "Please provide production configuration:"
    
    prompt_user "Domain name" "astrokabinet.id" domain
    prompt_user "Admin email" "admin@$domain" admin_email
    prompt_user "Company email" "info@$domain" company_email
    prompt_user "WhatsApp phone number" "***********" whatsapp_phone
    prompt_user "Bank name" "BCA" bank_name
    prompt_user "Bank account number" "" bank_account
    prompt_user "Bank account name" "Astro Works Indonesia PT" bank_account_name
    
    # Generate secure secrets
    print_info "Generating secure secrets..."
    jwt_secret=$(generate_jwt_secret)
    postgres_password=$(generate_password)
    redis_password=$(generate_password)
    admin_password=$(generate_password)
    
    # Update .env file
    sed -i.bak "s/DOMAIN=.*/DOMAIN=$domain/" .env
    sed -i.bak "s/ACME_EMAIL=.*/ACME_EMAIL=$admin_email/" .env
    sed -i.bak "s/POSTGRES_PASSWORD=.*/POSTGRES_PASSWORD=$postgres_password/" .env
    sed -i.bak "s/REDIS_PASSWORD=.*/REDIS_PASSWORD=$redis_password/" .env
    sed -i.bak "s/JWT_SECRET=.*/JWT_SECRET=$jwt_secret/" .env
    sed -i.bak "s/ADMIN_DEFAULT_EMAIL=.*/ADMIN_DEFAULT_EMAIL=$admin_email/" .env
    sed -i.bak "s/ADMIN_DEFAULT_PASSWORD=.*/ADMIN_DEFAULT_PASSWORD=$admin_password/" .env
    sed -i.bak "s/WHATSAPP_PHONE_NUMBER=.*/WHATSAPP_PHONE_NUMBER=$whatsapp_phone/" .env
    sed -i.bak "s/BANK_NAME=.*/BANK_NAME=$bank_name/" .env
    sed -i.bak "s/BANK_ACCOUNT_NUMBER=.*/BANK_ACCOUNT_NUMBER=$bank_account/" .env
    sed -i.bak "s/BANK_ACCOUNT_NAME=.*/BANK_ACCOUNT_NAME=$bank_account_name/" .env
    sed -i.bak "s/COMPANY_EMAIL=.*/COMPANY_EMAIL=$company_email/" .env
    
    # Update DATABASE_URL with new password
    sed -i.bak "s|DATABASE_URL=.*|DATABASE_URL=******************************************************/astro_ecommerce|" .env
    sed -i.bak "s|REDIS_URL=.*|REDIS_URL=redis://:$redis_password@redis:6379|" .env
    
    # Update URLs
    sed -i.bak "s|FRONTEND_URL=.*|FRONTEND_URL=https://$domain|" .env
    sed -i.bak "s|API_URL=.*|API_URL=https://api.$domain/api/v1|" .env
    sed -i.bak "s|CORS_ORIGIN=.*|CORS_ORIGIN=https://$domain|" .env
    
    print_success "Production configuration completed"
    print_info "Generated credentials:"
    echo -e "  Admin password: ${GREEN}$admin_password${NC}"
    echo -e "  Database password: ${GREEN}$postgres_password${NC}"
    echo -e "  Redis password: ${GREEN}$redis_password${NC}"
    
    print_warning "Please save these credentials securely!"
    print_warning "Remember to configure SSL certificates and DNS records"
}

update_config() {
    print_header "Updating Configuration"
    
    print_info "Available configuration options:"
    echo "1. Update WhatsApp phone number"
    echo "2. Update bank details"
    echo "3. Update company information"
    echo "4. Generate new JWT secret"
    echo "5. Update admin credentials"
    
    read -p "Select option (1-5): " option
    
    case $option in
        1)
            prompt_user "WhatsApp phone number" "***********" whatsapp_phone
            sed -i.bak "s/WHATSAPP_PHONE_NUMBER=.*/WHATSAPP_PHONE_NUMBER=$whatsapp_phone/" be_astro/.env
            sed -i.bak "s/VITE_WHATSAPP_PHONE_NUMBER=.*/VITE_WHATSAPP_PHONE_NUMBER=$whatsapp_phone/" fe_astro/.env
            print_success "WhatsApp phone number updated"
            ;;
        2)
            prompt_user "Bank name" "BCA" bank_name
            prompt_user "Bank account number" "" bank_account
            prompt_user "Bank account name" "Astro Works Indonesia PT" bank_account_name
            sed -i.bak "s/BANK_NAME=.*/BANK_NAME=$bank_name/" be_astro/.env
            sed -i.bak "s/BANK_ACCOUNT_NUMBER=.*/BANK_ACCOUNT_NUMBER=$bank_account/" be_astro/.env
            sed -i.bak "s/BANK_ACCOUNT_NAME=.*/BANK_ACCOUNT_NAME=$bank_account_name/" be_astro/.env
            print_success "Bank details updated"
            ;;
        3)
            prompt_user "Company name" "Astro Works Indonesia" company_name
            prompt_user "Company email" "<EMAIL>" company_email
            prompt_user "Company address" "Jakarta, Indonesia" company_address
            sed -i.bak "s/COMPANY_NAME=.*/COMPANY_NAME=$company_name/" be_astro/.env
            sed -i.bak "s/COMPANY_EMAIL=.*/COMPANY_EMAIL=$company_email/" be_astro/.env
            sed -i.bak "s/COMPANY_ADDRESS=.*/COMPANY_ADDRESS=$company_address/" be_astro/.env
            print_success "Company information updated"
            ;;
        4)
            jwt_secret=$(generate_jwt_secret)
            sed -i.bak "s/JWT_SECRET=.*/JWT_SECRET=$jwt_secret/" be_astro/.env
            print_success "New JWT secret generated"
            ;;
        5)
            prompt_user "Admin email" "<EMAIL>" admin_email
            admin_password=$(generate_password)
            sed -i.bak "s/ADMIN_DEFAULT_EMAIL=.*/ADMIN_DEFAULT_EMAIL=$admin_email/" be_astro/.env
            sed -i.bak "s/ADMIN_DEFAULT_PASSWORD=.*/ADMIN_DEFAULT_PASSWORD=$admin_password/" be_astro/.env
            print_success "Admin credentials updated"
            print_info "New admin password: $admin_password"
            ;;
        *)
            print_error "Invalid option"
            exit 1
            ;;
    esac
}

show_help() {
    echo "Environment Setup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev         Setup development environment"
    echo "  prod        Setup production environment"
    echo "  update      Update existing configuration"
    echo "  validate    Validate environment configuration"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev      # Setup development environment"
    echo "  $0 prod     # Setup production environment"
    echo "  $0 update   # Update configuration interactively"
}

# Main script logic
case "${1:-dev}" in
    "dev"|"development")
        setup_development
        ;;
    "prod"|"production")
        setup_production
        ;;
    "update"|"config")
        update_config
        ;;
    "validate"|"check")
        ./scripts/validate-env.sh
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac

print_info "Run './scripts/validate-env.sh' to validate your configuration"
