#!/bin/bash

# =============================================================================
# Environment Configuration Validation Script
# =============================================================================
# This script validates that all required environment variables are properly set

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
CHECKS_PASSED=0
CHECKS_WARNING=0
CHECKS_FAILED=0

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

check_env_var() {
    local var_name=$1
    local file_path=$2
    local required=${3:-true}
    
    if [ -f "$file_path" ] && grep -q "^${var_name}=" "$file_path"; then
        local value=$(grep "^${var_name}=" "$file_path" | cut -d'=' -f2- | sed 's/^"//' | sed 's/"$//')
        if [ -n "$value" ] && [ "$value" != "CHANGE_THIS" ] && [ "$value" != "your-super-secret-jwt-key-change-this-in-production" ]; then
            print_success "$var_name is set in $file_path"
            ((CHECKS_PASSED++))
        else
            if [ "$required" = "true" ]; then
                print_error "$var_name is not properly configured in $file_path"
                ((CHECKS_FAILED++))
            else
                print_warning "$var_name needs to be configured in $file_path"
                ((CHECKS_WARNING++))
            fi
        fi
    else
        if [ "$required" = "true" ]; then
            print_error "$var_name is missing from $file_path"
            ((CHECKS_FAILED++))
        else
            print_warning "$var_name is missing from $file_path"
            ((CHECKS_WARNING++))
        fi
    fi
}

check_file_exists() {
    local file_path=$1
    local description=$2
    
    if [ -f "$file_path" ]; then
        print_success "$description exists"
        ((CHECKS_PASSED++))
        return 0
    else
        print_error "$description is missing"
        ((CHECKS_FAILED++))
        return 1
    fi
}

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                    ASTRO WORKS - ENVIRONMENT VALIDATION                      ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Check if .env files exist
print_header "Environment Files"
check_file_exists ".env" "Global .env file"
check_file_exists "be_astro/.env" "Backend .env file"
check_file_exists "fe_astro/.env" "Frontend .env file"

# Validate Global Configuration
if [ -f ".env" ]; then
    print_header "Global Configuration (.env)"
    check_env_var "DOMAIN" ".env"
    check_env_var "DATABASE_URL" ".env"
    check_env_var "POSTGRES_PASSWORD" ".env"
    check_env_var "JWT_SECRET" ".env"
    check_env_var "WHATSAPP_PHONE_NUMBER" ".env"
    check_env_var "BANK_ACCOUNT_NUMBER" ".env"
    check_env_var "CORS_ORIGIN" ".env"
fi

# Validate Backend Configuration
if [ -f "be_astro/.env" ]; then
    print_header "Backend Configuration (be_astro/.env)"
    check_env_var "DATABASE_URL" "be_astro/.env"
    check_env_var "REDIS_URL" "be_astro/.env"
    check_env_var "JWT_SECRET" "be_astro/.env"
    check_env_var "ADMIN_DEFAULT_EMAIL" "be_astro/.env"
    check_env_var "ADMIN_DEFAULT_PASSWORD" "be_astro/.env"
    check_env_var "WHATSAPP_PHONE_NUMBER" "be_astro/.env"
    check_env_var "BANK_NAME" "be_astro/.env"
    check_env_var "BANK_ACCOUNT_NUMBER" "be_astro/.env"
    check_env_var "BANK_ACCOUNT_NAME" "be_astro/.env"
    check_env_var "CORS_ORIGIN" "be_astro/.env"
    
    # Optional configurations
    check_env_var "MAX_FILE_SIZE" "be_astro/.env" false
    check_env_var "IMAGE_QUALITY" "be_astro/.env" false
    check_env_var "RATE_LIMIT_REQUESTS_PER_MINUTE" "be_astro/.env" false
fi

# Validate Frontend Configuration
if [ -f "fe_astro/.env" ]; then
    print_header "Frontend Configuration (fe_astro/.env)"
    check_env_var "VITE_API_URL" "fe_astro/.env"
    check_env_var "VITE_API_BASE_URL" "fe_astro/.env"
    check_env_var "VITE_WHATSAPP_PHONE_NUMBER" "fe_astro/.env"
    check_env_var "VITE_WHATSAPP_BASE_URL" "fe_astro/.env"
    
    # Optional configurations
    check_env_var "VITE_COMPANY_NAME" "fe_astro/.env" false
    check_env_var "VITE_BANK_NAME" "fe_astro/.env" false
    check_env_var "VITE_ENABLE_CHAT" "fe_astro/.env" false
fi

# Security Checks
print_header "Security Validation"

# Check JWT Secret strength
if [ -f "be_astro/.env" ]; then
    jwt_secret=$(grep "^JWT_SECRET=" "be_astro/.env" | cut -d'=' -f2- | sed 's/^"//' | sed 's/"$//')
    if [ ${#jwt_secret} -ge 32 ]; then
        print_success "JWT secret is sufficiently long (${#jwt_secret} characters)"
        ((CHECKS_PASSED++))
    else
        print_error "JWT secret is too short (${#jwt_secret} characters, minimum 32 required)"
        ((CHECKS_FAILED++))
    fi
    
    # Check if default passwords are changed
    admin_password=$(grep "^ADMIN_DEFAULT_PASSWORD=" "be_astro/.env" | cut -d'=' -f2- | sed 's/^"//' | sed 's/"$//')
    if [ "$admin_password" = "admin123" ]; then
        print_warning "Admin password is still using default value"
        ((CHECKS_WARNING++))
    else
        print_success "Admin password has been changed from default"
        ((CHECKS_PASSED++))
    fi
fi

# Summary
print_header "Validation Summary"
echo -e "Checks passed: ${GREEN}$CHECKS_PASSED${NC}"
echo -e "Warnings: ${YELLOW}$CHECKS_WARNING${NC}"
echo -e "Errors: ${RED}$CHECKS_FAILED${NC}"

if [ $CHECKS_FAILED -gt 0 ]; then
    echo -e "\n${RED}❌ Environment validation failed. Please fix the errors above.${NC}"
    exit 1
elif [ $CHECKS_WARNING -gt 0 ]; then
    echo -e "\n${YELLOW}⚠️  Environment validation completed with warnings. Consider addressing them.${NC}"
    exit 0
else
    echo -e "\n${GREEN}✅ Environment validation passed successfully!${NC}"
    exit 0
fi
