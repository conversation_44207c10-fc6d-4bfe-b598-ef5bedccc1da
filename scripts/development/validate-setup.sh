#!/bin/bash

# Setup Validation Script for Astro Works E-commerce
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}$(echo "$1" | sed 's/./=/g')${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Validation counters
CHECKS_PASSED=0
CHECKS_FAILED=0
CHECKS_WARNING=0

check_command() {
    local cmd=$1
    local name=$2
    local required=${3:-true}
    
    if command -v "$cmd" >/dev/null 2>&1; then
        local version=$($cmd --version 2>/dev/null | head -1 || echo "Unknown version")
        print_success "$name is installed: $version"
        ((CHECKS_PASSED++))
        return 0
    else
        if [ "$required" = "true" ]; then
            print_error "$name is not installed (required)"
            ((CHECKS_FAILED++))
            return 1
        else
            print_warning "$name is not installed (optional)"
            ((CHECKS_WARNING++))
            return 0
        fi
    fi
}

check_file() {
    local file=$1
    local name=$2
    local required=${3:-true}
    
    if [ -f "$file" ]; then
        print_success "$name exists: $file"
        ((CHECKS_PASSED++))
        return 0
    else
        if [ "$required" = "true" ]; then
            print_error "$name is missing: $file (required)"
            ((CHECKS_FAILED++))
            return 1
        else
            print_warning "$name is missing: $file (optional)"
            ((CHECKS_WARNING++))
            return 0
        fi
    fi
}

check_directory() {
    local dir=$1
    local name=$2
    local required=${3:-true}
    
    if [ -d "$dir" ]; then
        print_success "$name exists: $dir"
        ((CHECKS_PASSED++))
        return 0
    else
        if [ "$required" = "true" ]; then
            print_error "$name is missing: $dir (required)"
            ((CHECKS_FAILED++))
            return 1
        else
            print_warning "$name is missing: $dir (optional)"
            ((CHECKS_WARNING++))
            return 0
        fi
    fi
}

check_port() {
    local port=$1
    local name=$2
    
    if netstat -tuln 2>/dev/null | grep -q ":$port " || ss -tuln 2>/dev/null | grep -q ":$port "; then
        print_warning "$name port $port is already in use"
        ((CHECKS_WARNING++))
    else
        print_success "$name port $port is available"
        ((CHECKS_PASSED++))
    fi
}

check_docker_network() {
    local network=$1
    
    if docker network ls | grep -q "$network"; then
        print_success "Docker network '$network' exists"
        ((CHECKS_PASSED++))
    else
        print_warning "Docker network '$network' does not exist (will be created)"
        ((CHECKS_WARNING++))
    fi
}

echo "🔍 Astro Works E-commerce Setup Validation"
echo "=========================================="
echo ""

# System Requirements
print_header "System Requirements"
check_command "docker" "Docker" true
check_command "docker-compose" "Docker Compose" true
check_command "cargo" "Rust/Cargo" true
check_command "node" "Node.js" true
check_command "npm" "npm" true
check_command "curl" "curl" true
check_command "git" "Git" false
check_command "make" "Make" false

echo ""

# Optional Tools
print_header "Optional Tools"
check_command "htpasswd" "Apache htpasswd" false
check_command "openssl" "OpenSSL" false
check_command "jq" "jq (JSON processor)" false
check_command "ab" "Apache Bench" false
check_command "pg_isready" "PostgreSQL client" false
check_command "redis-cli" "Redis CLI" false

echo ""

# Project Structure
print_header "Project Structure"
check_directory "be_astro" "Backend directory"
check_directory "fe_astro" "Frontend directory"
check_directory "database" "Database directory"
check_directory "traefik" "Traefik directory"
check_directory "scripts" "Scripts directory" false

echo ""

# Configuration Files
print_header "Configuration Files"
check_file "docker-compose.yml" "Development Docker Compose"
check_file "docker-compose.prod.yml" "Production Docker Compose"
check_file "Makefile" "Makefile"
check_file ".env.example" "Environment template"
check_file "traefik/traefik.yml" "Traefik static config"
check_file "traefik/dynamic.yml" "Traefik dynamic config"

echo ""

# Backend Files
print_header "Backend Files"
check_file "be_astro/Cargo.toml" "Backend Cargo.toml"
check_file "be_astro/src/main.rs" "Backend main.rs"
check_file "be_astro/Dockerfile.prod" "Backend production Dockerfile"
check_file "be_astro/.env.example" "Backend environment template"

echo ""

# Frontend Files
print_header "Frontend Files"
check_file "fe_astro/package.json" "Frontend package.json"
check_file "fe_astro/svelte.config.js" "Svelte config" false
check_file "fe_astro/vite.config.js" "Vite config" false
check_file "fe_astro/Dockerfile.prod" "Frontend production Dockerfile"

echo ""

# Database Files
print_header "Database Files"
check_file "database/sample_data.sql" "Sample data SQL"

echo ""

# Port Availability
print_header "Port Availability"
check_port "5432" "PostgreSQL"
check_port "6379" "Redis"
check_port "8000" "Backend API"
check_port "5173" "Frontend (dev)"
check_port "3000" "Frontend (prod)"
check_port "80" "HTTP"
check_port "443" "HTTPS"
check_port "8080" "Traefik Dashboard"

echo ""

# Docker Environment
print_header "Docker Environment"
if command -v docker >/dev/null 2>&1; then
    if docker info >/dev/null 2>&1; then
        print_success "Docker daemon is running"
        ((CHECKS_PASSED++))
        
        # Check Docker networks
        check_docker_network "traefik"
        
        # Check Docker images
        if docker images | grep -q "postgres"; then
            print_success "PostgreSQL Docker image is available"
            ((CHECKS_PASSED++))
        else
            print_warning "PostgreSQL Docker image not found (will be downloaded)"
            ((CHECKS_WARNING++))
        fi
        
        if docker images | grep -q "redis"; then
            print_success "Redis Docker image is available"
            ((CHECKS_PASSED++))
        else
            print_warning "Redis Docker image not found (will be downloaded)"
            ((CHECKS_WARNING++))
        fi
        
    else
        print_error "Docker daemon is not running"
        ((CHECKS_FAILED++))
    fi
else
    print_error "Docker is not installed"
    ((CHECKS_FAILED++))
fi

echo ""

# Environment Configuration
print_header "Environment Configuration"
if [ -f "be_astro/.env" ]; then
    print_success "Backend .env file exists"
    ((CHECKS_PASSED++))
    
    # Check for required environment variables
    if grep -q "DATABASE_URL" "be_astro/.env"; then
        print_success "DATABASE_URL is configured"
        ((CHECKS_PASSED++))
    else
        print_warning "DATABASE_URL not found in .env"
        ((CHECKS_WARNING++))
    fi
    
    if grep -q "JWT_SECRET" "be_astro/.env"; then
        print_success "JWT_SECRET is configured"
        ((CHECKS_PASSED++))
    else
        print_warning "JWT_SECRET not found in .env"
        ((CHECKS_WARNING++))
    fi
else
    print_warning "Backend .env file not found (run 'make setup')"
    ((CHECKS_WARNING++))
fi

if [ -f "fe_astro/.env" ]; then
    print_success "Frontend .env file exists"
    ((CHECKS_PASSED++))
else
    print_warning "Frontend .env file not found (run 'make setup')"
    ((CHECKS_WARNING++))
fi

echo ""

# Summary
print_header "Validation Summary"
echo "Checks passed: $CHECKS_PASSED"
echo "Checks failed: $CHECKS_FAILED"
echo "Warnings: $CHECKS_WARNING"
echo ""

if [ $CHECKS_FAILED -eq 0 ]; then
    print_success "All critical checks passed! ✨"
    echo ""
    echo "$(echo -e "${GREEN}")Next steps:$(echo -e "${NC}")"
    echo "  1. Run 'make setup' to complete initial setup"
    echo "  2. Run 'make quick-start' for full development environment"
    echo "  3. Run 'make test' to verify everything works"
    echo ""
    exit 0
else
    print_error "Some critical checks failed. Please fix the issues above."
    echo ""
    echo "$(echo -e "${YELLOW}")Common solutions:$(echo -e "${NC}")"
    echo "  - Install missing dependencies"
    echo "  - Start Docker daemon"
    echo "  - Run 'make setup' to create configuration files"
    echo ""
    exit 1
fi
