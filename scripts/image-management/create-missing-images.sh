#!/bin/bash

# Create missing product images using simple colored rectangles

echo "🖼️  Creating Missing Product Images"
echo "=================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Create directories
mkdir -p be_astro/static/uploads/products

# All product slugs from database
SLUGS=(
    "fantasy-tv-cabinet"
    "glass-door"
    "soft-close-hinges"
    "pull-out-basket"
    "cabinet-lighting"
    "led-gola"
    "tandem-box"
    "modern-kitchen-set"
    "kitchen-island-premium"
    "pantry-cabinet-large"
)

# Function to create simple colored image using convert
create_simple_image() {
    local slug=$1
    local aspect=$2
    local width=$3
    local height=$4
    local filename="be_astro/static/uploads/products/${aspect}_${slug}_1.webp"
    
    # Skip if file already exists
    if [ -f "$filename" ]; then
        echo -e "${YELLOW}⏭️  Skipping $filename (already exists)${NC}"
        return
    fi
    
    # Create a simple colored rectangle
    if command -v convert >/dev/null 2>&1; then
        # Use a different color for each aspect ratio
        if [ "$aspect" = "4x5" ]; then
            color="#e3f2fd"  # Light blue for mobile
        else
            color="#f3e5f5"  # Light purple for desktop
        fi
        
        convert -size ${width}x${height} xc:"$color" "$filename"
        echo -e "${GREEN}✅ Created $filename${NC}"
    elif command -v magick >/dev/null 2>&1; then
        # Use ImageMagick v7
        if [ "$aspect" = "4x5" ]; then
            color="#e3f2fd"  # Light blue for mobile
        else
            color="#f3e5f5"  # Light purple for desktop
        fi
        
        magick -size ${width}x${height} xc:"$color" "$filename"
        echo -e "${GREEN}✅ Created $filename${NC}"
    else
        # Fallback: copy an existing image if available
        local existing_file=$(find be_astro/static/uploads/products/ -name "*.webp" | head -1)
        if [ -n "$existing_file" ]; then
            cp "$existing_file" "$filename"
            echo -e "${YELLOW}📋 Copied existing image to $filename${NC}"
        else
            # Create empty file as last resort
            touch "$filename"
            echo -e "${YELLOW}⚠️  Created empty placeholder for $filename${NC}"
        fi
    fi
}

echo -e "${BLUE}📱 Creating mobile images (4:5 aspect ratio)...${NC}"
for slug in "${SLUGS[@]}"; do
    create_simple_image "$slug" "4x5" "400" "500"
done

echo ""
echo -e "${BLUE}🖥️  Creating desktop images (16:9 aspect ratio)...${NC}"
for slug in "${SLUGS[@]}"; do
    create_simple_image "$slug" "16x9" "800" "450"
done

echo ""
echo -e "${GREEN}🎉 Image creation completed!${NC}"
echo ""
echo -e "${BLUE}📁 All images:${NC}"
ls -la be_astro/static/uploads/products/*.webp | wc -l | xargs echo "Total WebP files:"

echo ""
echo -e "${BLUE}📊 Summary:${NC}"
TOTAL_FILES=$(ls be_astro/static/uploads/products/*.webp 2>/dev/null | wc -l)
echo "  Total WebP files: $TOTAL_FILES"
echo "  Expected: 20 (10 products × 2 aspects)"
echo "  Products: ${#SLUGS[@]}"
echo ""

if [ "$TOTAL_FILES" -eq 20 ]; then
    echo -e "${GREEN}✨ All product images are complete!${NC}"
else
    echo -e "${YELLOW}⚠️  Some images may be missing. Expected 20, found $TOTAL_FILES${NC}"
fi
