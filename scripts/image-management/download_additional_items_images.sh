#!/bin/bash

# Download Additional Items Images Script
# This script downloads sample images for additional items/accessories

# Create directory if it doesn't exist
mkdir -p be_astro/static/images/additional-items

# Define image URLs for additional items
declare -A URLS=(
    ["led-gola"]="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&crop=center"
    ["tandem-box"]="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop&crop=center"
    ["glass-door"]="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=400&fit=crop&crop=center"
    ["soft-close-hinges"]="https://images.unsplash.com/photo-1504148455328-c376907d081c?w=400&h=400&fit=crop&crop=center"
    ["pull-out-basket"]="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop&crop=center"
    ["cabinet-lighting"]="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&crop=center"
)

echo "🖼️  Downloading Additional Items Images..."
echo "=========================================="

# Download each image
for item in "${!URLS[@]}"; do
    url="${URLS[$item]}"
    filename="be_astro/static/images/additional-items/${item}.jpg"
    
    echo "📥 Downloading ${item}.jpg..."
    
    if curl -L -o "$filename" "$url" --silent --show-error; then
        echo "✅ Successfully downloaded ${item}.jpg"
    else
        echo "❌ Failed to download ${item}.jpg"
    fi
    
    # Small delay to be respectful to the server
    sleep 0.5
done

echo ""
echo "🎉 Download complete!"
echo ""
echo "📁 Images saved to: be_astro/static/images/additional-items/"
echo ""
echo "📋 Downloaded images:"
ls -la be_astro/static/images/additional-items/
echo ""
echo "🔗 Access images at: http://localhost:8000/static/images/additional-items/[filename]"
echo ""
echo "💡 To use in frontend:"
echo "   <img src=\"/static/images/additional-items/led-gola.jpg\" alt=\"Led Gola\" />"
