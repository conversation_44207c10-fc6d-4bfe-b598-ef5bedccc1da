#!/usr/bin/env python3
"""
Script to download furniture images for the Astro Works project.
Downloads high-quality furniture images from Unsplash and converts them to WebP format.
"""

import requests
from pathlib import Path
import time
from PIL import Image
import io

# Configuration
TARGET_DIR = "be_astro/static/uploads/products"
WEBP_QUALITY = 85

# Product slugs from database
PRODUCTS = [
    "cabinet-lighting",
    "fantasy-tv-cabinet",
    "glass-door",
    "kitchen-island-premium",
    "led-gola",
    "modern-kitchen-set",
    "pantry-cabinet-large",
    "pull-out-basket",
    "rak-tv-baru",
    "soft-close-hinges",
    "tandem-box",
    "test-product"
]

# Unsplash furniture image URLs - high quality furniture photos
FURNITURE_IMAGES = {
    "fantasy-tv-cabinet": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&q=80",
    "modern-kitchen-set": "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&q=80",
    "kitchen-island-premium": "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&q=80",
    "pantry-cabinet-large": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "cabinet-lighting": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&q=80",
    "glass-door": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "soft-close-hinges": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "pull-out-basket": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "led-gola": "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&q=80",
    "tandem-box": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "rak-tv-baru": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&q=80",
    "test-product": "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&q=80"
}

def resize_image_to_aspect(image, target_width, target_height):
    """Resize image to specific dimensions while maintaining quality"""
    return image.resize((target_width, target_height), Image.Resampling.LANCZOS)

def convert_to_webp(image, quality=85):
    """Convert PIL Image to WebP format with specified quality"""
    output = io.BytesIO()
    image.save(output, format='WebP', quality=quality, optimize=True)
    return output.getvalue()

def download_and_process_image(url, product_slug):
    """Download image and create both mobile (4:5) and desktop (16:9) versions in WebP format"""
    try:
        print(f"  📥 Downloading from: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        # Open image with PIL
        image = Image.open(io.BytesIO(response.content))

        # Convert to RGB if necessary (for WebP compatibility)
        if image.mode in ('RGBA', 'LA', 'P'):
            image = image.convert('RGB')

        # Create mobile version (4:5 aspect ratio - 400x500)
        mobile_image = resize_image_to_aspect(image, 400, 500)
        mobile_webp = convert_to_webp(mobile_image, WEBP_QUALITY)

        # Create desktop version (16:9 aspect ratio - 800x450)
        desktop_image = resize_image_to_aspect(image, 800, 450)
        desktop_webp = convert_to_webp(desktop_image, WEBP_QUALITY)

        # Save mobile version
        mobile_path = Path(TARGET_DIR) / f"4x5_{product_slug}_1.webp"
        with open(mobile_path, 'wb') as f:
            f.write(mobile_webp)
        print(f"  ✅ Created mobile: {mobile_path} ({len(mobile_webp)//1024}KB)")

        # Save desktop version
        desktop_path = Path(TARGET_DIR) / f"16x9_{product_slug}_1.webp"
        with open(desktop_path, 'wb') as f:
            f.write(desktop_webp)
        print(f"  ✅ Created desktop: {desktop_path} ({len(desktop_webp)//1024}KB)")

        return True

    except Exception as e:
        print(f"  ❌ Error processing {product_slug}: {e}")
        return False

# File extensions to create
EXTENSIONS = ["jpeg", "jpg", "png", "webp"]

def create_target_directory():
    """Create the target directory if it doesn't exist."""
    target_path = Path(TARGET_DIR)
    target_path.mkdir(parents=True, exist_ok=True)
    print(f"✅ Target directory created/verified: {target_path.absolute()}")

def download_image(url, filepath):
    """Download an image from URL and save it to filepath."""
    try:
        print(f"📥 Downloading: {url}")
        
        # Add headers to mimic a real browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # Save the image
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ Saved: {filepath}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error downloading {url}: {e}")
        return False
    except Exception as e:
        print(f"❌ Error saving {filepath}: {e}")
        return False

def main():
    """Main function to download all furniture images."""
    print("🚀 Starting furniture image download...")
    print("=" * 60)
    
    # Create target directory
    create_target_directory()
    
    # Track download statistics
    total_files = 0
    successful_downloads = 0
    failed_downloads = 0
    
    # Download images for each product and aspect ratio
    for base_name, url in PLACEHOLDER_URLS.items():
        print(f"\n📦 Processing: {base_name}")
        print("-" * 40)
        
        for extension in EXTENSIONS:
            filename = f"{base_name}_1.{extension}"
            filepath = Path(TARGET_DIR) / filename
            
            total_files += 1
            
            # Add a small delay to avoid overwhelming the server
            time.sleep(0.5)
            
            if download_image(url, filepath):
                successful_downloads += 1
            else:
                failed_downloads += 1
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 DOWNLOAD SUMMARY")
    print("=" * 60)
    print(f"Total files processed: {total_files}")
    print(f"✅ Successful downloads: {successful_downloads}")
    print(f"❌ Failed downloads: {failed_downloads}")
    print(f"📁 Files saved to: {Path(TARGET_DIR).absolute()}")
    
    if failed_downloads == 0:
        print("\n🎉 All images downloaded successfully!")
    else:
        print(f"\n⚠️  {failed_downloads} downloads failed. Please check the URLs and try again.")
    
    # List all created files
    print("\n📋 Created files:")
    target_path = Path(TARGET_DIR)
    if target_path.exists():
        image_files = sorted([f for f in target_path.iterdir() if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.webp']])
        for file in image_files:
            file_size = file.stat().st_size
            print(f"   {file.name} ({file_size:,} bytes)")
    
    print("\n✨ Script completed!")

if __name__ == "__main__":
    main()
