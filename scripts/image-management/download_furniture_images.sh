#!/bin/bash

# Script to download furniture images for Astro Works project
# Downloads placeholder images and saves them with specific naming convention

# Configuration
TARGET_DIR="be_astro/static/uploads/products"

# Create target directory if it doesn't exist
mkdir -p "$TARGET_DIR"

echo "🚀 Starting furniture image download..."
echo "=============================================="
echo "📁 Target directory: $TARGET_DIR"

# Define image URLs (replace with actual URLs)
declare -A URLS=(
    # 4:5 aspect ratio images (mobile)
    ["4x5_fantasy-tv-cabinet"]="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=500&fit=crop&auto=format"
    ["4x5_led-gola"]="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=500&fit=crop&auto=format"
    ["4x5_tandem-box"]="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=500&fit=crop&auto=format"
    
    # 16:9 aspect ratio images (desktop)
    ["16x9_fantasy-tv-cabinet"]="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=450&fit=crop&auto=format"
    ["16x9_led-gola"]="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=450&fit=crop&auto=format"
    ["16x9_tandem-box"]="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=450&fit=crop&auto=format"
)

# File extensions to create
EXTENSIONS=("jpeg" "jpg" "png" "webp")

# Counters
total_files=0
successful_downloads=0
failed_downloads=0

# Function to download and save image
download_image() {
    local url="$1"
    local filepath="$2"
    local filename=$(basename "$filepath")
    
    echo "📥 Downloading: $filename"
    
    # Download with curl
    if curl -L -s -o "$filepath" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \
        --connect-timeout 30 \
        --max-time 60 \
        "$url"; then
        
        # Check if file was actually downloaded and has content
        if [[ -f "$filepath" && -s "$filepath" ]]; then
            local file_size=$(stat -f%z "$filepath" 2>/dev/null || stat -c%s "$filepath" 2>/dev/null || echo "unknown")
            echo "✅ Saved: $filename ($file_size bytes)"
            return 0
        else
            echo "❌ Failed: $filename (empty file)"
            rm -f "$filepath"
            return 1
        fi
    else
        echo "❌ Failed: $filename (download error)"
        rm -f "$filepath"
        return 1
    fi
}

# Main download loop
echo ""
for base_name in "${!URLS[@]}"; do
    url="${URLS[$base_name]}"
    echo "📦 Processing: $base_name"
    echo "🔗 URL: $url"
    echo "----------------------------------------"
    
    for extension in "${EXTENSIONS[@]}"; do
        filename="${base_name}_1.${extension}"
        filepath="$TARGET_DIR/$filename"
        
        ((total_files++))
        
        if download_image "$url" "$filepath"; then
            ((successful_downloads++))
        else
            ((failed_downloads++))
        fi
        
        # Small delay to avoid overwhelming the server
        sleep 0.5
    done
    echo ""
done

# Print summary
echo "=============================================="
echo "📊 DOWNLOAD SUMMARY"
echo "=============================================="
echo "Total files processed: $total_files"
echo "✅ Successful downloads: $successful_downloads"
echo "❌ Failed downloads: $failed_downloads"
echo "📁 Files saved to: $(pwd)/$TARGET_DIR"

if [[ $failed_downloads -eq 0 ]]; then
    echo ""
    echo "🎉 All images downloaded successfully!"
else
    echo ""
    echo "⚠️  $failed_downloads downloads failed. Please check the URLs and try again."
fi

# List all created files
echo ""
echo "📋 Created files:"
if [[ -d "$TARGET_DIR" ]]; then
    for file in "$TARGET_DIR"/*.{jpg,jpeg,png,webp} 2>/dev/null; do
        if [[ -f "$file" ]]; then
            filename=$(basename "$file")
            file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "unknown")
            echo "   $filename ($file_size bytes)"
        fi
    done
fi

echo ""
echo "✨ Script completed!"
