#!/usr/bin/env python3
"""
Script to download furniture images for the Astro Works project.
Downloads high-quality furniture images from Unsplash and converts them to WebP format.
"""

import requests
from pathlib import Path
import time
from PIL import Image
import io

# Configuration
TARGET_DIR = "be_astro/static/uploads/products"
WEBP_QUALITY = 85

# Product slugs from database
PRODUCTS = [
    "cabinet-lighting",
    "fantasy-tv-cabinet", 
    "glass-door",
    "kitchen-island-premium",
    "led-gola",
    "modern-kitchen-set",
    "pantry-cabinet-large",
    "pull-out-basket",
    "rak-tv-baru",
    "soft-close-hinges",
    "tandem-box",
    "test-product"
]

# Unsplash furniture image URLs - high quality furniture photos
FURNITURE_IMAGES = {
    "fantasy-tv-cabinet": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&q=80",
    "modern-kitchen-set": "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&q=80",
    "kitchen-island-premium": "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&q=80",
    "pantry-cabinet-large": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "cabinet-lighting": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&q=80",
    "glass-door": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "soft-close-hinges": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "pull-out-basket": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "led-gola": "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&q=80",
    "tandem-box": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&q=80",
    "rak-tv-baru": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&q=80",
    "test-product": "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&q=80"
}

def resize_image_to_aspect(image, target_width, target_height):
    """Resize image to specific dimensions while maintaining quality"""
    return image.resize((target_width, target_height), Image.Resampling.LANCZOS)

def convert_to_webp(image, quality=85):
    """Convert PIL Image to WebP format with specified quality"""
    output = io.BytesIO()
    image.save(output, format='WebP', quality=quality, optimize=True)
    return output.getvalue()

def download_and_process_image(url, product_slug):
    """Download image and create both mobile (4:5) and desktop (16:9) versions in WebP format"""
    try:
        print(f"  📥 Downloading from: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # Open image with PIL
        image = Image.open(io.BytesIO(response.content))
        
        # Convert to RGB if necessary (for WebP compatibility)
        if image.mode in ('RGBA', 'LA', 'P'):
            image = image.convert('RGB')
        
        # Create mobile version (4:5 aspect ratio - 400x500)
        mobile_image = resize_image_to_aspect(image, 400, 500)
        mobile_webp = convert_to_webp(mobile_image, WEBP_QUALITY)
        
        # Create desktop version (16:9 aspect ratio - 800x450)  
        desktop_image = resize_image_to_aspect(image, 800, 450)
        desktop_webp = convert_to_webp(desktop_image, WEBP_QUALITY)
        
        # Save mobile version
        mobile_path = Path(TARGET_DIR) / f"4x5_{product_slug}_1.webp"
        with open(mobile_path, 'wb') as f:
            f.write(mobile_webp)
        print(f"  ✅ Created mobile: {mobile_path} ({len(mobile_webp)//1024}KB)")
        
        # Save desktop version
        desktop_path = Path(TARGET_DIR) / f"16x9_{product_slug}_1.webp"
        with open(desktop_path, 'wb') as f:
            f.write(desktop_webp)
        print(f"  ✅ Created desktop: {desktop_path} ({len(desktop_webp)//1024}KB)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error processing {product_slug}: {e}")
        return False

def main():
    """Main function to download all furniture images"""
    print("🏠 Astro Works - Furniture Image Downloader")
    print("=" * 50)
    print(f"Target directory: {TARGET_DIR}")
    print(f"Total products: {len(PRODUCTS)}")
    print("Creating WebP images in 4:5 (mobile) and 16:9 (desktop) aspect ratios")
    print("=" * 50)
    
    # Ensure target directory exists
    Path(TARGET_DIR).mkdir(parents=True, exist_ok=True)
    print(f"📁 Directory ready: {TARGET_DIR}")
    
    # Statistics
    successful_downloads = 0
    failed_downloads = 0
    
    # Download images for each product
    for product_slug in PRODUCTS:
        print(f"\n📦 Processing: {product_slug}")
        print("-" * 40)
        
        # Check if images already exist
        mobile_path = Path(TARGET_DIR) / f"4x5_{product_slug}_1.webp"
        desktop_path = Path(TARGET_DIR) / f"16x9_{product_slug}_1.webp"
        
        if mobile_path.exists() and desktop_path.exists():
            print(f"  ⏭️  Images already exist for {product_slug}, skipping...")
            successful_downloads += 1
            continue
        
        # Get image URL for this product
        if product_slug in FURNITURE_IMAGES:
            url = FURNITURE_IMAGES[product_slug]
        else:
            # Use a default furniture image
            url = "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&q=80"
            print(f"  ⚠️  Using default image for {product_slug}")
        
        # Download and process
        if download_and_process_image(url, product_slug):
            successful_downloads += 1
        else:
            failed_downloads += 1
        
        # Add delay to be respectful to Unsplash
        time.sleep(1)
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 DOWNLOAD SUMMARY")
    print("=" * 50)
    print(f"✅ Successful: {successful_downloads}")
    print(f"❌ Failed: {failed_downloads}")
    print(f"📁 Images saved to: {TARGET_DIR}")
    print("=" * 50)

if __name__ == "__main__":
    main()
