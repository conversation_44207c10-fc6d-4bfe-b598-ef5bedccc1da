#!/bin/bash

# Generate placeholder images for all products
# This script creates placeholder images for products that don't have images

echo "🖼️  Generating Product Images"
echo "============================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Create directories
mkdir -p be_astro/static/uploads/products

# Product slugs and names
declare -A PRODUCTS=(
    ["fantasy-tv-cabinet"]="Fantasy TV Cabinet"
    ["glass-door"]="Glass Door"
    ["soft-close-hinges"]="Soft Close Hinges"
    ["pull-out-basket"]="Pull Out Basket"
    ["cabinet-lighting"]="Cabinet Lighting"
    ["led-gola"]="Led Gola"
    ["tandem-box"]="Tandem Box"
    ["modern-kitchen-set"]="Modern Kitchen Set"
    ["kitchen-island-premium"]="Kitchen Island Premium"
    ["pantry-cabinet-large"]="Pantry Cabinet Large"
)

# Function to create placeholder image using ImageMagick (if available)
create_placeholder_image() {
    local slug=$1
    local name=$2
    local aspect=$3
    local width=$4
    local height=$5
    local filename="be_astro/static/uploads/products/${aspect}_${slug}_1.webp"
    
    if command -v convert >/dev/null 2>&1; then
        # Use ImageMagick to create a nice placeholder
        convert -size ${width}x${height} \
            -background "#f3f4f6" \
            -fill "#6b7280" \
            -gravity center \
            -font Arial \
            -pointsize 24 \
            label:"$name\n${width}x${height}" \
            "$filename"
        echo -e "${GREEN}✅ Created $filename${NC}"
    else
        # Create a simple colored rectangle using ImageMagick alternative
        if command -v magick >/dev/null 2>&1; then
            magick -size ${width}x${height} \
                -background "#f3f4f6" \
                -fill "#6b7280" \
                -gravity center \
                -font Arial \
                -pointsize 24 \
                label:"$name\n${width}x${height}" \
                "$filename"
            echo -e "${GREEN}✅ Created $filename${NC}"
        else
            # Fallback: create a simple text file as placeholder
            echo "Placeholder for $name (${width}x${height})" > "${filename%.webp}.txt"
            echo -e "${YELLOW}⚠️  Created text placeholder for $filename (ImageMagick not available)${NC}"
        fi
    fi
}

# Function to download sample image from placeholder service
download_placeholder_image() {
    local slug=$1
    local name=$2
    local aspect=$3
    local width=$4
    local height=$5
    local filename="be_astro/static/uploads/products/${aspect}_${slug}_1.webp"
    
    # Use placeholder.com service
    local url="https://via.placeholder.com/${width}x${height}/f3f4f6/6b7280?text=${name// /+}"
    
    if command -v curl >/dev/null 2>&1; then
        curl -s -o "$filename" "$url"
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Downloaded $filename${NC}"
        else
            echo -e "${YELLOW}⚠️  Failed to download, creating local placeholder${NC}"
            create_placeholder_image "$slug" "$name" "$aspect" "$width" "$height"
        fi
    elif command -v wget >/dev/null 2>&1; then
        wget -q -O "$filename" "$url"
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Downloaded $filename${NC}"
        else
            echo -e "${YELLOW}⚠️  Failed to download, creating local placeholder${NC}"
            create_placeholder_image "$slug" "$name" "$aspect" "$width" "$height"
        fi
    else
        echo -e "${YELLOW}⚠️  No download tool available, creating local placeholder${NC}"
        create_placeholder_image "$slug" "$name" "$aspect" "$width" "$height"
    fi
}

echo -e "${BLUE}📱 Creating mobile images (4:5 aspect ratio)...${NC}"
for slug in "${!PRODUCTS[@]}"; do
    name="${PRODUCTS[$slug]}"
    download_placeholder_image "$slug" "$name" "4x5" "400" "500"
done

echo ""
echo -e "${BLUE}🖥️  Creating desktop images (16:9 aspect ratio)...${NC}"
for slug in "${!PRODUCTS[@]}"; do
    name="${PRODUCTS[$slug]}"
    download_placeholder_image "$slug" "$name" "16x9" "800" "450"
done

echo ""
echo -e "${GREEN}🎉 Image generation completed!${NC}"
echo ""
echo -e "${BLUE}📁 Generated images:${NC}"
ls -la be_astro/static/uploads/products/ | head -20

echo ""
echo -e "${BLUE}📊 Summary:${NC}"
TOTAL_FILES=$(ls be_astro/static/uploads/products/ | wc -l)
echo "  Total files created: $TOTAL_FILES"
echo "  Products: ${#PRODUCTS[@]}"
echo "  Aspects: 2 (4x5 mobile, 16x9 desktop)"
echo ""
echo -e "${GREEN}✨ All product images are ready!${NC}"
