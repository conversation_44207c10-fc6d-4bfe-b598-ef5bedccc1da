#!/bin/bash

# Verify that all products have corresponding images

echo "🔍 Verifying Product Images"
echo "=========================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Get all product slugs from API
echo -e "${BLUE}📡 Fetching products from API...${NC}"
PRODUCTS=$(curl -s "http://localhost:8000/api/v1/products?per_page=1000" | jq -r '.[] | .slug' | grep -v null | sort)

if [ -z "$PRODUCTS" ]; then
    echo -e "${RED}❌ Failed to fetch products from API${NC}"
    exit 1
fi

PRODUCT_COUNT=$(echo "$PRODUCTS" | wc -l)
echo -e "${GREEN}✅ Found $PRODUCT_COUNT products${NC}"
echo ""

# Check each product for both aspect ratios
MISSING_IMAGES=()
TOTAL_EXPECTED=0
TOTAL_FOUND=0

echo -e "${BLUE}🖼️  Checking images for each product...${NC}"
echo ""

for slug in $PRODUCTS; do
    echo -e "${BLUE}Checking: $slug${NC}"
    
    # Check 4x5 (mobile) image
    MOBILE_IMAGE="be_astro/static/uploads/products/4x5_${slug}_1.webp"
    MOBILE_URL="http://localhost:8000/static/uploads/products/4x5_${slug}_1.webp"
    TOTAL_EXPECTED=$((TOTAL_EXPECTED + 1))
    
    if [ -f "$MOBILE_IMAGE" ]; then
        # Check if accessible via HTTP
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$MOBILE_URL")
        if [ "$HTTP_STATUS" = "200" ]; then
            echo -e "  📱 Mobile (4x5): ${GREEN}✅ Available${NC}"
            TOTAL_FOUND=$((TOTAL_FOUND + 1))
        else
            echo -e "  📱 Mobile (4x5): ${RED}❌ File exists but HTTP $HTTP_STATUS${NC}"
            MISSING_IMAGES+=("$MOBILE_URL")
        fi
    else
        echo -e "  📱 Mobile (4x5): ${RED}❌ Missing${NC}"
        MISSING_IMAGES+=("$MOBILE_IMAGE")
    fi
    
    # Check 16x9 (desktop) image
    DESKTOP_IMAGE="be_astro/static/uploads/products/16x9_${slug}_1.webp"
    DESKTOP_URL="http://localhost:8000/static/uploads/products/16x9_${slug}_1.webp"
    TOTAL_EXPECTED=$((TOTAL_EXPECTED + 1))
    
    if [ -f "$DESKTOP_IMAGE" ]; then
        # Check if accessible via HTTP
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$DESKTOP_URL")
        if [ "$HTTP_STATUS" = "200" ]; then
            echo -e "  🖥️  Desktop (16x9): ${GREEN}✅ Available${NC}"
            TOTAL_FOUND=$((TOTAL_FOUND + 1))
        else
            echo -e "  🖥️  Desktop (16x9): ${RED}❌ File exists but HTTP $HTTP_STATUS${NC}"
            MISSING_IMAGES+=("$DESKTOP_URL")
        fi
    else
        echo -e "  🖥️  Desktop (16x9): ${RED}❌ Missing${NC}"
        MISSING_IMAGES+=("$DESKTOP_IMAGE")
    fi
    
    echo ""
done

# Summary
echo -e "${BLUE}📊 Summary${NC}"
echo "=========="
echo "Products: $PRODUCT_COUNT"
echo "Expected images: $TOTAL_EXPECTED"
echo "Found images: $TOTAL_FOUND"
echo "Missing images: ${#MISSING_IMAGES[@]}"
echo ""

if [ ${#MISSING_IMAGES[@]} -eq 0 ]; then
    echo -e "${GREEN}🎉 All product images are available!${NC}"
    echo -e "${GREEN}✨ No CORS or 404 errors expected${NC}"
else
    echo -e "${RED}⚠️  Missing images:${NC}"
    for missing in "${MISSING_IMAGES[@]}"; do
        echo -e "  ${RED}❌ $missing${NC}"
    done
fi

echo ""
echo -e "${BLUE}🌐 Test URLs:${NC}"
echo "Frontend: http://localhost:5174"
echo "Backend: http://localhost:8000"
echo "Sample image: http://localhost:8000/static/uploads/products/4x5_fantasy-tv-cabinet_1.webp"
