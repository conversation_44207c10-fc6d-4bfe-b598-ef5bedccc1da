#!/bin/bash

# ===========================================
# ASTRO WORKS INDONESIA - INITIALIZATION SCRIPT
# ===========================================

set -e

echo "🚀 Initializing Astro Works Indonesia Production Environment"
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_header "1. Creating necessary directories"

# Create directories
mkdir -p backups
mkdir -p logs
mkdir -p data
mkdir -p monitoring
mkdir -p be_astro/static/uploads/products
mkdir -p be_astro/static/images

print_status "Directories created successfully"

print_header "2. Setting up Docker networks"

# Create external networks if they don't exist
docker network create traefik 2>/dev/null || print_warning "Network 'traefik' already exists"

print_status "Docker networks configured"

print_header "3. Setting up environment configuration"

# Check if .env exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Copying from .env.example"
    cp .env.example .env
    print_warning "Please edit .env file with your actual configuration before deployment!"
    print_warning "Critical variables to change:"
    echo "  - POSTGRES_PASSWORD"
    echo "  - JWT_SECRET"
    echo "  - ADMIN_PASSWORD"
    echo "  - BANK_ACCOUNT_NUMBER"
else
    print_status ".env file already exists"
fi

print_header "4. Creating Prometheus configuration"

# Create Prometheus config if it doesn't exist
if [ ! -f monitoring/prometheus.yml ]; then
    cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'astro-backend'
    static_configs:
      - targets: ['astro-backend:7998']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
EOF
    print_status "Prometheus configuration created"
else
    print_status "Prometheus configuration already exists"
fi

print_header "5. Setting up file permissions"

# Set proper permissions
chmod +x scripts/*.sh 2>/dev/null || true
chmod 755 backups logs data monitoring
chmod 755 be_astro/static/uploads/products be_astro/static/images

print_status "File permissions set"

print_header "6. Validating environment variables"

# Source .env file
if [ -f .env ]; then
    source .env
    
    # Check critical variables
    MISSING_VARS=()
    
    [ -z "$POSTGRES_PASSWORD" ] && MISSING_VARS+=("POSTGRES_PASSWORD")
    [ -z "$JWT_SECRET" ] && MISSING_VARS+=("JWT_SECRET")
    [ -z "$ADMIN_PASSWORD" ] && MISSING_VARS+=("ADMIN_PASSWORD")
    [ -z "$BANK_ACCOUNT_NUMBER" ] && MISSING_VARS+=("BANK_ACCOUNT_NUMBER")
    
    if [ ${#MISSING_VARS[@]} -ne 0 ]; then
        print_error "Missing critical environment variables:"
        for var in "${MISSING_VARS[@]}"; do
            echo "  - $var"
        done
        print_error "Please configure these variables in .env file before deployment"
        exit 1
    fi
    
    # Check JWT_SECRET length
    if [ ${#JWT_SECRET} -lt 32 ]; then
        print_error "JWT_SECRET must be at least 32 characters long"
        exit 1
    fi
    
    print_status "Environment variables validation passed"
else
    print_error ".env file not found"
    exit 1
fi

print_header "7. Pre-deployment checks"

# Check if ports are available
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        print_warning "Port $1 is already in use"
        return 1
    else
        return 0
    fi
}

PORTS_OK=true
check_port 7998 || PORTS_OK=false
check_port 5432 || PORTS_OK=false
check_port 6379 || PORTS_OK=false
check_port 3000 || PORTS_OK=false
check_port 9090 || PORTS_OK=false

if [ "$PORTS_OK" = false ]; then
    print_warning "Some ports are already in use. This might cause conflicts."
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "Initialization cancelled"
        exit 1
    fi
fi

print_header "8. Creating deployment scripts"

# Create deployment script
cat > scripts/deploy.sh << 'EOF'
#!/bin/bash

set -e

echo "🚀 Deploying Astro Works Indonesia"
echo "=================================="

# Pull latest images
echo "Pulling latest images..."
docker-compose -f docker-compose.prod.yml pull

# Build and start services
echo "Building and starting services..."
docker-compose -f docker-compose.prod.yml up -d --build

# Wait for services to be healthy
echo "Waiting for services to be healthy..."
sleep 30

# Show status
echo "Checking service status..."
docker-compose -f docker-compose.prod.yml ps

# Show logs
echo "Showing recent logs..."
docker-compose -f docker-compose.prod.yml logs --tail=50

echo "✅ Deployment completed!"
echo "Backend API: http://localhost:7998"
echo "Frontend: http://localhost:3000"
echo "Prometheus: http://localhost:9090"
echo "Database: localhost:5432"
EOF

chmod +x scripts/deploy.sh

print_status "Deployment script created"

echo ""
echo "🎉 Initialization completed successfully!"
echo "========================================"
echo ""
print_status "Next steps:"
echo "1. Review and configure .env file with your actual values"
echo "2. Run: ./scripts/deploy.sh"
echo "3. Monitor logs: docker-compose -f docker-compose.prod.yml logs -f"
echo ""
print_warning "Important: Make sure to change all default passwords and secrets in .env file!"
echo ""
