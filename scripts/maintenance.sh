#!/bin/bash

# ===========================================
# ASTRO WORKS INDONESIA - MAINTENANCE SCRIPT
# ===========================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[MAINTENANCE]${NC} $1"
}

# Show help
show_help() {
    echo "Astro Works Indonesia - Maintenance Script"
    echo "========================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  cleanup     Clean up Docker resources and old files"
    echo "  update      Update and restart services"
    echo "  logs        Show and manage logs"
    echo "  restart     Restart specific service"
    echo "  status      Show detailed system status"
    echo "  backup      Create manual backup"
    echo "  restore     Restore from backup"
    echo "  help        Show this help message"
    echo ""
}

# Cleanup function
cleanup() {
    print_header "System Cleanup"
    
    print_status "Cleaning up Docker resources..."
    
    # Remove unused containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes (be careful with this)
    read -p "Remove unused Docker volumes? This may delete data! (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker volume prune -f
    fi
    
    # Remove unused networks
    docker network prune -f
    
    print_status "Cleaning up log files..."
    
    # Rotate large log files
    find logs/ -name "*.log" -size +100M -exec truncate -s 50M {} \; 2>/dev/null || true
    
    # Remove old temporary files
    find /tmp -name "astro-*" -mtime +1 -delete 2>/dev/null || true
    
    print_status "Cleanup completed"
}

# Update function
update() {
    print_header "System Update"
    
    print_status "Pulling latest images..."
    docker-compose -f docker-compose.prod.yml pull
    
    print_status "Rebuilding services..."
    docker-compose -f docker-compose.prod.yml build --no-cache
    
    print_status "Restarting services..."
    docker-compose -f docker-compose.prod.yml up -d
    
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check health
    ./scripts/monitor.sh
    
    print_status "Update completed"
}

# Logs management
manage_logs() {
    print_header "Log Management"
    
    echo "Available log commands:"
    echo "1. Show all logs"
    echo "2. Show specific service logs"
    echo "3. Follow logs in real-time"
    echo "4. Clear logs"
    echo "5. Export logs"
    
    read -p "Select option (1-5): " choice
    
    case $choice in
        1)
            docker-compose -f docker-compose.prod.yml logs --tail=100
            ;;
        2)
            echo "Available services:"
            docker-compose -f docker-compose.prod.yml ps --services
            read -p "Enter service name: " service
            docker-compose -f docker-compose.prod.yml logs --tail=100 "$service"
            ;;
        3)
            docker-compose -f docker-compose.prod.yml logs -f
            ;;
        4)
            read -p "Clear all logs? This cannot be undone! (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                docker-compose -f docker-compose.prod.yml down
                docker system prune -f
                docker-compose -f docker-compose.prod.yml up -d
                print_status "Logs cleared"
            fi
            ;;
        5)
            timestamp=$(date +%Y%m%d_%H%M%S)
            log_file="logs/exported_logs_$timestamp.txt"
            docker-compose -f docker-compose.prod.yml logs > "$log_file"
            print_status "Logs exported to: $log_file"
            ;;
        *)
            print_error "Invalid option"
            ;;
    esac
}

# Restart service
restart_service() {
    print_header "Service Restart"
    
    echo "Available services:"
    docker-compose -f docker-compose.prod.yml ps --services
    
    read -p "Enter service name to restart: " service
    
    if docker-compose -f docker-compose.prod.yml ps --services | grep -q "^$service$"; then
        print_status "Restarting $service..."
        docker-compose -f docker-compose.prod.yml restart "$service"
        
        # Wait a bit and check status
        sleep 10
        if docker-compose -f docker-compose.prod.yml ps | grep -q "$service.*Up"; then
            print_status "$service restarted successfully"
        else
            print_error "$service failed to restart"
            docker-compose -f docker-compose.prod.yml logs --tail=20 "$service"
        fi
    else
        print_error "Service '$service' not found"
    fi
}

# Detailed status
detailed_status() {
    print_header "Detailed System Status"
    
    # Run monitoring script
    ./scripts/monitor.sh
    
    # Additional system information
    echo ""
    print_header "System Information"
    
    echo "System uptime: $(uptime)"
    echo "Disk usage:"
    df -h /
    
    echo ""
    echo "Memory usage:"
    free -h
    
    echo ""
    echo "Docker system info:"
    docker system df
}

# Backup wrapper
create_backup() {
    print_header "Manual Backup"
    ./scripts/backup.sh --type manual
}

# Restore function
restore_backup() {
    print_header "Restore from Backup"
    
    print_warning "This will restore the database from a backup file."
    print_warning "Current data will be LOST!"
    
    # List available backups
    echo ""
    echo "Available backup files:"
    ls -la backups/backup_*.sql 2>/dev/null || {
        print_error "No backup files found"
        exit 1
    }
    
    echo ""
    read -p "Enter backup filename (without path): " backup_file
    
    if [ ! -f "backups/$backup_file" ]; then
        print_error "Backup file not found: backups/$backup_file"
        exit 1
    fi
    
    read -p "Are you sure you want to restore from $backup_file? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Restore cancelled"
        exit 0
    fi
    
    # Source environment
    source .env
    
    print_status "Stopping backend service..."
    docker-compose -f docker-compose.prod.yml stop astro-backend
    
    print_status "Restoring database..."
    
    # Drop and recreate database
    docker-compose -f docker-compose.prod.yml exec -T postgres psql -U astro_user -d postgres -c "DROP DATABASE IF EXISTS astro_ecommerce;"
    docker-compose -f docker-compose.prod.yml exec -T postgres psql -U astro_user -d postgres -c "CREATE DATABASE astro_ecommerce;"
    
    # Restore from backup
    if docker-compose -f docker-compose.prod.yml exec -T postgres psql -U astro_user -d astro_ecommerce < "backups/$backup_file"; then
        print_status "Database restored successfully"
    else
        print_error "Database restore failed"
        exit 1
    fi
    
    print_status "Starting backend service..."
    docker-compose -f docker-compose.prod.yml start astro-backend
    
    # Wait for service to be ready
    sleep 20
    
    if curl -s http://localhost:7998/health > /dev/null; then
        print_status "✅ Restore completed successfully!"
    else
        print_error "Backend service failed to start after restore"
    fi
}

# Main script logic
case "${1:-help}" in
    cleanup)
        cleanup
        ;;
    update)
        update
        ;;
    logs)
        manage_logs
        ;;
    restart)
        restart_service
        ;;
    status)
        detailed_status
        ;;
    backup)
        create_backup
        ;;
    restore)
        restore_backup
        ;;
    help|*)
        show_help
        ;;
esac
