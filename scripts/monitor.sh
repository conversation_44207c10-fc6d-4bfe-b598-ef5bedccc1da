#!/bin/bash

# ===========================================
# ASTRO WORKS INDONESIA - MONITORING SCRIPT
# ===========================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[MONITOR]${NC} $1"
}

echo "📊 Astro Works Indonesia - System Monitor"
echo "========================================"

print_header "Service Status"

# Check if services are running
services=("astro-backend" "postgres" "redis" "astro-frontend" "backup" "prometheus")

for service in "${services[@]}"; do
    if docker-compose -f docker-compose.prod.yml ps | grep -q "$service.*Up"; then
        print_status "$service: Running"
    else
        print_error "$service: Not running"
    fi
done

echo ""
print_header "Health Checks"

# Backend health check
if curl -s http://localhost:7998/health > /dev/null; then
    print_status "Backend API: Healthy"
else
    print_error "Backend API: Unhealthy"
fi

# Database health check
if docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U astro_user -d astro_ecommerce > /dev/null 2>&1; then
    print_status "Database: Healthy"
else
    print_error "Database: Unhealthy"
fi

# Redis health check
if docker-compose -f docker-compose.prod.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
    print_status "Redis: Healthy"
else
    print_error "Redis: Unhealthy"
fi

# Frontend health check
if curl -s http://localhost:3000 > /dev/null; then
    print_status "Frontend: Healthy"
else
    print_error "Frontend: Unhealthy"
fi

echo ""
print_header "Resource Usage"

# Get container stats
echo "Container Resource Usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" $(docker-compose -f docker-compose.prod.yml ps -q)

echo ""
print_header "Disk Usage"

# Check disk usage
echo "Disk Usage:"
echo "Backups: $(du -sh backups 2>/dev/null || echo '0B')"
echo "Logs: $(du -sh logs 2>/dev/null || echo '0B')"
echo "Data: $(du -sh data 2>/dev/null || echo '0B')"
echo "Uploads: $(du -sh be_astro/static/uploads 2>/dev/null || echo '0B')"

# Check available disk space
echo ""
echo "Available Disk Space:"
df -h / | tail -1

echo ""
print_header "Recent Logs (Last 10 lines)"

# Show recent logs from each service
for service in "${services[@]}"; do
    echo ""
    echo "--- $service ---"
    docker-compose -f docker-compose.prod.yml logs --tail=3 "$service" 2>/dev/null || echo "No logs available"
done

echo ""
print_header "Backup Status"

# Check backup files
backup_count=$(ls -1 backups/*.sql 2>/dev/null | wc -l)
if [ "$backup_count" -gt 0 ]; then
    latest_backup=$(ls -t backups/*.sql 2>/dev/null | head -1)
    backup_age=$(stat -c %Y "$latest_backup" 2>/dev/null || echo 0)
    current_time=$(date +%s)
    age_hours=$(( (current_time - backup_age) / 3600 ))
    
    print_status "Backup files: $backup_count"
    print_status "Latest backup: $(basename "$latest_backup") ($age_hours hours ago)"
    
    if [ "$age_hours" -gt 25 ]; then
        print_warning "Latest backup is older than 25 hours"
    fi
else
    print_warning "No backup files found"
fi

echo ""
print_header "Network Connectivity"

# Check external connectivity
if ping -c 1 google.com > /dev/null 2>&1; then
    print_status "External connectivity: OK"
else
    print_warning "External connectivity: Failed"
fi

# Check internal Docker network
if docker network inspect astro_network > /dev/null 2>&1; then
    print_status "Docker network: OK"
else
    print_error "Docker network: Failed"
fi

echo ""
print_header "Security Checks"

# Check for running containers with privileged mode
privileged_containers=$(docker ps --filter "label=privileged=true" --format "{{.Names}}" 2>/dev/null)
if [ -z "$privileged_containers" ]; then
    print_status "No privileged containers detected"
else
    print_warning "Privileged containers found: $privileged_containers"
fi

# Check for containers running as root
root_containers=$(docker-compose -f docker-compose.prod.yml exec -T astro-backend whoami 2>/dev/null)
if [ "$root_containers" = "astro" ]; then
    print_status "Backend running as non-root user"
else
    print_warning "Backend may be running as root"
fi

echo ""
print_header "Performance Metrics"

# Database connections
db_connections=$(docker-compose -f docker-compose.prod.yml exec -T postgres psql -U astro_user -d astro_ecommerce -t -c "SELECT count(*) FROM pg_stat_activity;" 2>/dev/null | tr -d ' ' || echo "N/A")
print_status "Database connections: $db_connections"

# Redis memory usage
redis_memory=$(docker-compose -f docker-compose.prod.yml exec -T redis redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r' 2>/dev/null || echo "N/A")
print_status "Redis memory usage: $redis_memory"

echo ""
echo "📊 Monitoring completed at $(date)"
echo "================================"
