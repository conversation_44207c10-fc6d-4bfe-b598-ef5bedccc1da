#!/bin/bash

# =============================================================================
# VPS Setup Script for Astro Works Backend
# =============================================================================
# This script prepares the VPS for deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SECURE_ENV_FILE="$PROJECT_ROOT/.env.vps.secure"

# Load secure VPS configuration
if [[ -f "$SECURE_ENV_FILE" ]]; then
    source "$SECURE_ENV_FILE"
else
    echo -e "${RED}[ERROR]${NC} Secure environment file not found: $SECURE_ENV_FILE"
    echo "Please create .env.vps.secure with VPS connection details"
    exit 1
fi

# Functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test VPS connection
test_connection() {
    print_header "Testing VPS Connection"
    
    print_info "Testing SSH connection to $VPS_HOST..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$VPS_HOST" "echo 'SSH connection successful'"; then
        print_success "SSH connection successful"
    else
        print_error "Cannot connect to VPS via SSH"
        print_info "Please ensure SSH key authentication is set up"
        exit 1
    fi
}

# Install Docker on VPS
install_docker() {
    print_header "Installing Docker on VPS"
    
    print_info "Checking if Docker is installed..."
    if ssh "$VPS_HOST" "command -v docker &> /dev/null"; then
        print_success "Docker is already installed"
        return
    fi
    
    print_info "Installing Docker..."
    ssh "$VPS_HOST" "
        # Update package index
        sudo apt-get update
        
        # Install required packages
        sudo apt-get install -y \
            ca-certificates \
            curl \
            gnupg \
            lsb-release
        
        # Add Docker's official GPG key
        sudo mkdir -p /etc/apt/keyrings
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
        
        # Set up the repository
        echo \"deb [arch=\$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \$(lsb_release -cs) stable\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
        
        # Update package index again
        sudo apt-get update
        
        # Install Docker Engine
        sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
        
        # Add user to docker group
        sudo usermod -aG docker \$USER
        
        # Start and enable Docker
        sudo systemctl start docker
        sudo systemctl enable docker
    "
    
    print_success "Docker installed successfully"
}

# Setup firewall
setup_firewall() {
    print_header "Setting Up Firewall"
    
    print_info "Configuring UFW firewall..."
    ssh "$VPS_HOST" "
        # Install UFW if not installed
        sudo apt-get install -y ufw
        
        # Reset UFW to defaults
        sudo ufw --force reset
        
        # Set default policies
        sudo ufw default deny incoming
        sudo ufw default allow outgoing
        
        # Allow SSH
        sudo ufw allow ssh
        
        # Allow HTTP and HTTPS
        sudo ufw allow 80/tcp
        sudo ufw allow 443/tcp
        
        # Allow backend API port
        sudo ufw allow 7998/tcp
        
        # Enable UFW
        sudo ufw --force enable
        
        # Show status
        sudo ufw status verbose
    "
    
    print_success "Firewall configured successfully"
}

# Create deployment directories
create_directories() {
    print_header "Creating Deployment Directories"
    
    print_info "Creating directory structure on VPS..."
    ssh "$VPS_HOST" "
        # Create main deployment directory
        mkdir -p $VPS_DEPLOY_PATH
        
        # Create subdirectories
        mkdir -p $VPS_DEPLOY_PATH/data
        mkdir -p $VPS_DEPLOY_PATH/backups
        mkdir -p $VPS_DEPLOY_PATH/logs
        
        # Set permissions
        chmod 755 $VPS_DEPLOY_PATH
        chmod 755 $VPS_DEPLOY_PATH/data
        chmod 755 $VPS_DEPLOY_PATH/backups
        chmod 755 $VPS_DEPLOY_PATH/logs
    "
    
    print_success "Directories created successfully"
}

# Setup system monitoring
setup_monitoring() {
    print_header "Setting Up System Monitoring"
    
    print_info "Installing monitoring tools..."
    ssh "$VPS_HOST" "
        # Update package list
        sudo apt-get update
        
        # Install monitoring tools
        sudo apt-get install -y \
            htop \
            iotop \
            nethogs \
            ncdu \
            tree \
            jq \
            curl \
            wget \
            unzip
        
        # Install Docker monitoring
        sudo apt-get install -y docker-compose
    "
    
    print_success "Monitoring tools installed"
}

# Setup log rotation
setup_log_rotation() {
    print_header "Setting Up Log Rotation"
    
    print_info "Configuring log rotation..."
    ssh "$VPS_HOST" "
        # Create logrotate configuration for Docker
        sudo tee /etc/logrotate.d/docker > /dev/null <<EOF
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=1M
    missingok
    delaycompress
    copytruncate
}
EOF

        # Create logrotate configuration for application logs
        sudo tee /etc/logrotate.d/astro-works > /dev/null <<EOF
$VPS_DEPLOY_PATH/logs/*.log {
    rotate 14
    daily
    compress
    size=10M
    missingok
    delaycompress
    copytruncate
    create 644 $VPS_USER $VPS_USER
}
EOF
    "
    
    print_success "Log rotation configured"
}

# Setup backup script
setup_backup() {
    print_header "Setting Up Backup Script"
    
    print_info "Creating backup script..."
    ssh "$VPS_HOST" "
        # Create backup script
        tee $VPS_DEPLOY_PATH/backup.sh > /dev/null <<'EOF'
#!/bin/bash

# Backup script for Astro Works
BACKUP_DIR=\"$VPS_DEPLOY_PATH/backups\"
DATE=\$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p \"\$BACKUP_DIR\"

# Backup database
echo \"Backing up database...\"
cd $VPS_DEPLOY_PATH
docker compose -f docker-compose.vps.yml exec -T postgres pg_dump -U astro_user astro_ecommerce > \"\$BACKUP_DIR/db_backup_\$DATE.sql\"

# Backup static files
echo \"Backing up static files...\"
tar -czf \"\$BACKUP_DIR/static_backup_\$DATE.tar.gz\" -C $VPS_DEPLOY_PATH/be_astro static/

# Backup data directory
echo \"Backing up data directory...\"
tar -czf \"\$BACKUP_DIR/data_backup_\$DATE.tar.gz\" -C $VPS_DEPLOY_PATH data/

# Remove old backups (keep last 7 days)
find \"\$BACKUP_DIR\" -name \"*_backup_*.sql\" -mtime +7 -delete
find \"\$BACKUP_DIR\" -name \"*_backup_*.tar.gz\" -mtime +7 -delete

echo \"Backup completed: \$DATE\"
EOF

        # Make backup script executable
        chmod +x $VPS_DEPLOY_PATH/backup.sh
        
        # Add to crontab (daily at 2 AM)
        (crontab -l 2>/dev/null; echo \"0 2 * * * $VPS_DEPLOY_PATH/backup.sh >> $VPS_DEPLOY_PATH/logs/backup.log 2>&1\") | crontab -
    "
    
    print_success "Backup script configured"
}

# Show setup summary
show_summary() {
    print_header "Setup Summary"
    
    echo -e "${CYAN}VPS Setup Completed Successfully!${NC}"
    echo ""
    echo -e "${YELLOW}VPS Details:${NC}"
    echo "  Host: $VPS_HOST"
    echo "  Deploy Path: $VPS_DEPLOY_PATH"
    echo "  API Port: 7998"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "  1. Run: make deploy-vps"
    echo "  2. Test: curl http://$VPS_IP:7998/health"
    echo "  3. Monitor: make vps-logs"
    echo ""
    echo -e "${YELLOW}Management:${NC}"
    echo "  Logs: ssh $VPS_HOST 'cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.vps.yml logs -f'"
    echo "  Status: make vps-status"
    echo "  Backup: ssh $VPS_HOST '$VPS_DEPLOY_PATH/backup.sh'"
}

# Main setup function
main() {
    print_header "Astro Works VPS Setup"
    print_info "Setting up VPS: $VPS_HOST"
    
    test_connection
    install_docker
    setup_firewall
    create_directories
    setup_monitoring
    setup_log_rotation
    setup_backup
    show_summary
    
    print_success "VPS setup completed successfully!"
}

# Run main function
main "$@"
