#!/bin/bash

# Generate SSL certificates for local development
# This script creates self-signed certificates for local domains

echo "Generating SSL certificates for local development..."

# Create directories
mkdir -p ssl/certs
mkdir -p ssl/private
mkdir -p traefik/certs

# Define domains
DOMAINS=(
    "astrokabinet.id"
    "api.astrokabinet.id"
    "traefik.astrokabinet.id"
    "astrokabinet.dev"
    "api.astrokabinet.dev"
    "traefik.astrokabinet.dev"
)

# Create OpenSSL config file for SAN (Subject Alternative Names)
cat > ssl/openssl.cnf << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=ID
ST=Jakarta
L=Jakarta
O=Astro Cabinet Development
OU=IT Department
CN=astrokabinet.id

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = astrokabinet.id
DNS.2 = api.astrokabinet.id
DNS.3 = traefik.astrokabinet.id
DNS.4 = astrokabinet.dev
DNS.5 = api.astrokabinet.dev
DNS.6 = traefik.astrokabinet.dev
DNS.7 = localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

echo "Creating Root CA..."

# Generate Root CA private key
openssl genrsa -out ssl/private/ca.key 4096

# Generate Root CA certificate
openssl req -new -x509 -days 3650 -key ssl/private/ca.key -out ssl/certs/ca.crt -subj "/C=ID/ST=Jakarta/L=Jakarta/O=Astro Cabinet CA/CN=Astro Cabinet Root CA"

echo "Creating server certificate..."

# Generate server private key
openssl genrsa -out ssl/private/server.key 2048

# Generate certificate signing request
openssl req -new -key ssl/private/server.key -out ssl/server.csr -config ssl/openssl.cnf

# Generate server certificate signed by CA
openssl x509 -req -in ssl/server.csr -CA ssl/certs/ca.crt -CAkey ssl/private/ca.key -CAcreateserial -out ssl/certs/server.crt -days 365 -extensions v3_req -extfile ssl/openssl.cnf

# Copy certificates for Traefik
cp ssl/certs/server.crt traefik/certs/
cp ssl/private/server.key traefik/certs/

# Create combined certificate file for Traefik
cat ssl/certs/server.crt ssl/certs/ca.crt > traefik/certs/fullchain.crt

# Set proper permissions
chmod 600 ssl/private/*.key
chmod 644 ssl/certs/*.crt
chmod 600 traefik/certs/*.key
chmod 644 traefik/certs/*.crt

# Clean up
rm ssl/server.csr
rm ssl/openssl.cnf

echo ""
echo "✅ SSL certificates generated successfully!"
echo ""
echo "📁 Certificate files:"
echo "  Root CA: ssl/certs/ca.crt"
echo "  Server Certificate: ssl/certs/server.crt"
echo "  Server Private Key: ssl/private/server.key"
echo "  Traefik Certificates: traefik/certs/"
echo ""
echo "🔒 To trust the certificates in your browser:"
echo "  1. Import ssl/certs/ca.crt as a trusted Root CA"
echo "  2. Or accept the security warning for self-signed certificates"
echo ""
echo "🌐 Domains covered:"
for domain in "${DOMAINS[@]}"; do
    echo "  - https://$domain"
done
echo "  - https://localhost"
echo ""
echo "⚠️  Note: These are self-signed certificates for development only!"
