#!/bin/bash

# Generate Traefik Basic Auth Hash
# Usage: ./generate-traefik-auth.sh username password

if [ $# -ne 2 ]; then
    echo "Usage: $0 <username> <password>"
    echo "Example: $0 admin mypassword"
    exit 1
fi

USERNAME="$1"
PASSWORD="$2"

# Check if htpasswd is available
if command -v htpasswd >/dev/null 2>&1; then
    # Use htpasswd if available
    HASH=$(htpasswd -nbB "$USERNAME" "$PASSWORD" | cut -d: -f2)
    echo "Generated hash using htpasswd:"
    echo "$USERNAME:$HASH"
elif command -v openssl >/dev/null 2>&1; then
    # Use openssl as fallback
    HASH=$(openssl passwd -apr1 "$PASSWORD")
    echo "Generated hash using openssl:"
    echo "$USERNAME:$HASH"
elif command -v python3 >/dev/null 2>&1; then
    # Use Python as fallback
    HASH=$(python3 -c "
import crypt
import getpass
password = '$PASSWORD'
hash = crypt.crypt(password, crypt.mksalt(crypt.METHOD_SHA512))
print('$USERNAME:' + hash)
")
    echo "Generated hash using Python:"
    echo "$HASH"
else
    echo "Error: No suitable tool found for generating password hash."
    echo "Please install one of: htpasswd, openssl, or python3"
    exit 1
fi

echo ""
echo "Add this to your .env.prod file:"
echo "TRAEFIK_AUTH=\"$USERNAME:\$HASH\""
echo ""
echo "Or use in docker-compose labels:"
echo "- \"traefik.http.middlewares.dashboard-auth.basicauth.users=$USERNAME:\$HASH\""
