#!/bin/bash

# Complete setup for local HTTPS development
# This script sets up domains, generates SSL certificates, and starts services

echo "🚀 Setting up Astro Cabinet with Local HTTPS Development"
echo "========================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if running as root for domain setup
check_sudo() {
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}❌ This script needs sudo access to modify /etc/hosts${NC}"
        echo "Please run with sudo:"
        echo "sudo ./setup-local-https.sh"
        exit 1
    fi
}

# Setup domains
setup_domains() {
    echo -e "${BLUE}📝 Setting up local domains...${NC}"
    
    # Backup hosts file
    cp /etc/hosts /etc/hosts.backup.$(date +%Y%m%d_%H%M%S)
    echo -e "${GREEN}✅ Backed up /etc/hosts${NC}"
    
    # Define domains
    DOMAINS=(
        "astrokabinet.id"
        "api.astrokabinet.id"
        "traefik.astrokabinet.id"
        "astrokabinet.dev"
        "api.astrokabinet.dev"
        "traefik.astrokabinet.dev"
    )
    
    # Add domains to hosts file
    echo "" >> /etc/hosts
    echo "# Astro Cabinet Local Development Domains" >> /etc/hosts
    
    for domain in "${DOMAINS[@]}"; do
        if grep -q "$domain" /etc/hosts; then
            echo -e "${YELLOW}⚠️  Domain $domain already exists in /etc/hosts${NC}"
        else
            echo "127.0.0.1    $domain" >> /etc/hosts
            echo -e "${GREEN}✅ Added $domain to /etc/hosts${NC}"
        fi
    done
}

# Generate SSL certificates
generate_ssl() {
    echo -e "${BLUE}🔒 Generating SSL certificates...${NC}"
    
    # Switch back to regular user for file operations
    SUDO_USER_HOME=$(eval echo ~$SUDO_USER)
    cd "$SUDO_USER_HOME/$(basename "$PWD")" || exit 1
    
    # Run SSL generation as regular user
    sudo -u $SUDO_USER ./generate-ssl.sh
    
    echo -e "${GREEN}✅ SSL certificates generated${NC}"
}

# Start services
start_services() {
    echo -e "${BLUE}🐳 Starting services with Traefik...${NC}"
    
    # Switch to regular user for Docker operations
    sudo -u $SUDO_USER docker-compose -f docker-compose.traefik.yml up -d
    
    echo -e "${GREEN}✅ Services started${NC}"
}

# Main execution
main() {
    check_sudo
    
    echo -e "${BLUE}🔧 Starting setup process...${NC}"
    echo ""
    
    setup_domains
    echo ""
    
    generate_ssl
    echo ""
    
    start_services
    echo ""
    
    echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}🌐 You can now access:${NC}"
    echo -e "  Frontend:  ${GREEN}https://astrokabinet.id${NC} or ${GREEN}https://astrokabinet.dev${NC}"
    echo -e "  API:       ${GREEN}https://api.astrokabinet.id${NC} or ${GREEN}https://api.astrokabinet.dev${NC}"
    echo -e "  Traefik:   ${GREEN}https://traefik.astrokabinet.id${NC} or ${GREEN}https://traefik.astrokabinet.dev${NC}"
    echo ""
    echo -e "${YELLOW}🔒 SSL Certificate Setup:${NC}"
    echo "  1. Your browser will show a security warning for self-signed certificates"
    echo "  2. Click 'Advanced' and 'Proceed to site' to accept the certificate"
    echo "  3. Or import ssl/certs/ca.crt as a trusted Root CA in your browser"
    echo ""
    echo -e "${BLUE}📋 Useful commands:${NC}"
    echo "  Stop services:    docker-compose -f docker-compose.traefik.yml down"
    echo "  View logs:        docker-compose -f docker-compose.traefik.yml logs -f"
    echo "  Restart:          docker-compose -f docker-compose.traefik.yml restart"
    echo ""
    echo -e "${GREEN}✨ Happy coding!${NC}"
}

# Run main function
main "$@"
