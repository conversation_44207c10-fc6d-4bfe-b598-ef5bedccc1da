#!/bin/bash

# Simple HTTPS development setup without Docker
# Uses existing development servers with SSL proxy

echo "🚀 Simple HTTPS Development Setup"
echo "================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Check if SSL certificates exist
if [ ! -f "ssl/certs/server.crt" ]; then
    echo -e "${BLUE}🔒 Generating SSL certificates...${NC}"
    ./generate-ssl.sh
    echo ""
fi

echo -e "${BLUE}📋 Manual Domain Setup Required${NC}"
echo ""
echo "Please add these lines to your /etc/hosts file:"
echo ""
echo -e "${YELLOW}127.0.0.1    astrokabinet.id${NC}"
echo -e "${YELLOW}127.0.0.1    api.astrokabinet.id${NC}"
echo ""
echo "Command to add domains:"
echo 'echo "127.0.0.1    astrokabinet.id api.astrokabinet.id" | sudo tee -a /etc/hosts'
echo ""

# Check if backend is running
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend is running at http://localhost:8000${NC}"
else
    echo -e "${YELLOW}⚠️  Backend is not running. Please start it with:${NC}"
    echo "cd be_astro && cargo run --bin be_astro"
    echo ""
fi

# Check if frontend is running
if curl -s http://localhost:5174 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Frontend is running at http://localhost:5174${NC}"
else
    echo -e "${YELLOW}⚠️  Frontend is not running. Please start it with:${NC}"
    echo "cd fe_astro && npm run dev"
    echo ""
fi

echo ""
echo -e "${BLUE}🌐 Current Access URLs:${NC}"
echo -e "  Frontend: ${GREEN}http://localhost:5174${NC}"
echo -e "  Backend:  ${GREEN}http://localhost:8000${NC}"
echo ""
echo -e "${BLUE}🔒 After adding domains to /etc/hosts:${NC}"
echo -e "  You can access via custom domains (HTTP for now)"
echo -e "  Frontend: ${GREEN}http://astrokabinet.id:5174${NC}"
echo -e "  Backend:  ${GREEN}http://api.astrokabinet.id:8000${NC}"
echo ""
echo -e "${BLUE}📝 Next Steps:${NC}"
echo "1. Add domains to /etc/hosts (requires sudo)"
echo "2. Ensure both backend and frontend are running"
echo "3. Test the application at http://localhost:5174"
echo "4. All products should be visible (7 total)"
echo "5. No CORS issues since backend accepts localhost:5174"
echo ""
echo -e "${GREEN}✨ Development environment is ready!${NC}"
