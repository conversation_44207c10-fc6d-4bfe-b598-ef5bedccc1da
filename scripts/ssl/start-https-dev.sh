#!/bin/bash

# Start HTTPS development without requiring sudo
# This script starts services and provides instructions for manual domain setup

echo "🚀 Starting Astro Cabinet HTTPS Development"
echo "==========================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}📋 Manual Domain Setup Required${NC}"
echo ""
echo "Please add these lines to your /etc/hosts file:"
echo ""
echo -e "${YELLOW}127.0.0.1    astrokabinet.id${NC}"
echo -e "${YELLOW}127.0.0.1    api.astrokabinet.id${NC}"
echo -e "${YELLOW}127.0.0.1    traefik.astrokabinet.id${NC}"
echo -e "${YELLOW}127.0.0.1    astrokabinet.dev${NC}"
echo -e "${YELLOW}127.0.0.1    api.astrokabinet.dev${NC}"
echo -e "${YELLOW}127.0.0.1    traefik.astrokabinet.dev${NC}"
echo ""
echo "Commands to add domains:"
echo "sudo nano /etc/hosts"
echo "# Or use this one-liner:"
echo 'echo "127.0.0.1    astrokabinet.id api.astrokabinet.id traefik.astrokabinet.id astrokabinet.dev api.astrokabinet.dev traefik.astrokabinet.dev" | sudo tee -a /etc/hosts'
echo ""

# Check if SSL certificates exist
if [ ! -f "ssl/certs/server.crt" ]; then
    echo -e "${BLUE}🔒 Generating SSL certificates...${NC}"
    ./generate-ssl.sh
    echo ""
fi

echo -e "${BLUE}🐳 Starting services with Traefik...${NC}"
docker-compose -f docker-compose.traefik.yml up -d

echo ""
echo -e "${GREEN}✅ Services started successfully!${NC}"
echo ""
echo -e "${BLUE}🌐 Access URLs (after adding domains to /etc/hosts):${NC}"
echo -e "  Frontend:  ${GREEN}https://astrokabinet.id${NC}"
echo -e "  API:       ${GREEN}https://api.astrokabinet.id${NC}"
echo -e "  Traefik:   ${GREEN}https://traefik.astrokabinet.id${NC}"
echo ""
echo -e "${BLUE}🔒 SSL Certificate Info:${NC}"
echo "  Your browser will show a security warning for self-signed certificates"
echo "  Click 'Advanced' → 'Proceed to site' to accept the certificate"
echo "  Or import ssl/certs/ca.crt as a trusted Root CA"
echo ""
echo -e "${BLUE}📋 Useful commands:${NC}"
echo "  Stop:     docker-compose -f docker-compose.traefik.yml down"
echo "  Logs:     docker-compose -f docker-compose.traefik.yml logs -f"
echo "  Restart:  docker-compose -f docker-compose.traefik.yml restart"
echo ""
echo -e "${GREEN}🎉 Ready for HTTPS development!${NC}"
