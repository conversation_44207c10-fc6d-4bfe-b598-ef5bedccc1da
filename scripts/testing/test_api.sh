#!/bin/bash

# API Testing Script for Astro Works E-commerce
set -e

API_BASE="http://localhost:8000/api/v1"
ADMIN_TOKEN=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local headers=$4
    
    if [ -n "$data" ]; then
        if [ -n "$headers" ]; then
            curl -s -X "$method" "$API_BASE$endpoint" \
                -H "Content-Type: application/json" \
                -H "$headers" \
                -d "$data"
        else
            curl -s -X "$method" "$API_BASE$endpoint" \
                -H "Content-Type: application/json" \
                -d "$data"
        fi
    else
        if [ -n "$headers" ]; then
            curl -s -X "$method" "$API_BASE$endpoint" \
                -H "$headers"
        else
            curl -s -X "$method" "$API_BASE$endpoint"
        fi
    fi
}

echo "🧪 Testing Astro Works E-commerce API"
echo "====================================="

# Test 1: Health Check
print_test "Health check"
response=$(api_call "GET" "/health")
if echo "$response" | grep -q "ok"; then
    print_success "Health check passed"
else
    print_error "Health check failed: $response"
    exit 1
fi

# Test 2: Admin Login
print_test "Admin login"
login_data='{"email":"<EMAIL>","password":"admin123"}'
response=$(api_call "POST" "/auth/login" "$login_data")
if echo "$response" | grep -q "token"; then
    ADMIN_TOKEN=$(echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    print_success "Admin login successful"
else
    print_error "Admin login failed: $response"
fi

# Test 3: List Categories
print_test "List categories"
response=$(api_call "GET" "/categories")
if echo "$response" | grep -q "Kitchen\|Wardrobe"; then
    print_success "Categories retrieved successfully"
else
    print_error "Failed to retrieve categories: $response"
fi

# Test 4: List Products
print_test "List products"
response=$(api_call "GET" "/products")
if echo "$response" | grep -q "Fantasy\|SORA"; then
    print_success "Products retrieved successfully"
    # Extract first product ID for further testing
    PRODUCT_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
else
    print_error "Failed to retrieve products: $response"
fi

# Test 5: Get Product with Pricing
if [ -n "$PRODUCT_ID" ]; then
    print_test "Get product with pricing options"
    response=$(api_call "GET" "/products/$PRODUCT_ID/pricing")
    if echo "$response" | grep -q "product_dimensions\|product_themes"; then
        print_success "Product pricing options retrieved successfully"
    else
        print_error "Failed to retrieve product pricing: $response"
    fi
fi

# Test 6: Get Product Dimensions
if [ -n "$PRODUCT_ID" ]; then
    print_test "Get product dimensions"
    response=$(api_call "GET" "/products/$PRODUCT_ID/dimensions")
    if echo "$response" | grep -q "width\|height"; then
        print_success "Product dimensions retrieved successfully"
        DIMENSION_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    else
        print_error "Failed to retrieve product dimensions: $response"
    fi
fi

# Test 7: Get Product Themes
if [ -n "$PRODUCT_ID" ]; then
    print_test "Get product themes"
    response=$(api_call "GET" "/products/$PRODUCT_ID/themes")
    if echo "$response" | grep -q "name"; then
        print_success "Product themes retrieved successfully"
        THEME_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    else
        print_error "Failed to retrieve product themes: $response"
    fi
fi

# Test 8: Calculate Dynamic Price
if [ -n "$PRODUCT_ID" ] && [ -n "$DIMENSION_ID" ]; then
    print_test "Calculate dynamic price"
    price_data="{\"product_id\":\"$PRODUCT_ID\",\"dimension_id\":\"$DIMENSION_ID\"}"
    if [ -n "$THEME_ID" ]; then
        price_data="{\"product_id\":\"$PRODUCT_ID\",\"dimension_id\":\"$DIMENSION_ID\",\"theme_id\":\"$THEME_ID\"}"
    fi
    
    response=$(api_call "POST" "/products/$PRODUCT_ID/calculate-price" "$price_data")
    if echo "$response" | grep -q "final_price\|breakdown"; then
        print_success "Dynamic price calculation successful"
        echo "Price calculation result: $response" | jq '.' 2>/dev/null || echo "$response"
    else
        print_error "Failed to calculate dynamic price: $response"
    fi
fi

# Test 9: Get Available Accessories
print_test "Get available accessories"
response=$(api_call "GET" "/products/accessories")
if echo "$response" | grep -q "Led\|Tandem"; then
    print_success "Accessories retrieved successfully"
else
    print_error "Failed to retrieve accessories: $response"
fi

# Test 10: List Product Bundles
print_test "List product bundles"
response=$(api_call "GET" "/bundles")
if echo "$response" | grep -q "Fantasy\|Complete"; then
    print_success "Product bundles retrieved successfully"
else
    print_error "Failed to retrieve product bundles: $response"
fi

# Test 11: List Orders
print_test "List orders"
response=$(api_call "GET" "/orders")
if echo "$response" | grep -q "order_number\|customer_name"; then
    print_success "Orders retrieved successfully"
else
    print_error "Failed to retrieve orders: $response"
fi

# Test 12: Create Test Order
print_test "Create test order"
order_data='{
    "customer_name": "Test Customer",
    "customer_email": "<EMAIL>",
    "customer_phone": "081234567890",
    "customer_address": "Jl. Test No. 123",
    "customer_city": "Jakarta",
    "customer_province": "DKI Jakarta",
    "shipping_amount": 100000,
    "notes": "Test order from API",
    "order_items": [
        {
            "product_id": "'$PRODUCT_ID'",
            "quantity": 1,
            "unit_price": 1590000
        }
    ]
}'

if [ -n "$PRODUCT_ID" ]; then
    response=$(api_call "POST" "/orders" "$order_data")
    if echo "$response" | grep -q "order_number"; then
        print_success "Test order created successfully"
        ORDER_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    else
        print_error "Failed to create test order: $response"
    fi
fi

# Test 13: Generate WhatsApp Link
if [ -n "$ORDER_ID" ]; then
    print_test "Generate WhatsApp link"
    response=$(api_call "GET" "/orders/$ORDER_ID/whatsapp")
    if echo "$response" | grep -q "whatsapp_url"; then
        print_success "WhatsApp link generated successfully"
    else
        print_error "Failed to generate WhatsApp link: $response"
    fi
fi

echo ""
echo "🎉 API Testing Completed!"
echo "========================"
echo "All core functionality has been tested."
echo ""
echo "Next steps:"
echo "1. Test the frontend interface"
echo "2. Verify real-time price calculations"
echo "3. Test responsive design on different devices"
echo "4. Performance testing with larger datasets"
