#!/bin/bash

# =============================================================================
# Complete Environment Variables Verification Script
# =============================================================================
# This script verifies ALL configuration uses environment variables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Counters
CHECKS_PASSED=0
CHECKS_WARNING=0
CHECKS_FAILED=0

# Functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((CHECKS_PASSED++))
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    ((CHECKS_WARNING++))
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((CHECKS_FAILED++))
}

# Check for ANY hardcoded values
check_all_hardcoded() {
    print_header "Checking for ALL Hardcoded Values"
    
    # Check for localhost references
    print_info "Checking for localhost references..."
    local localhost_files=$(grep -r "localhost:" fe_astro/src/ be_astro/src/ --exclude-dir=node_modules --exclude="*.md" 2>/dev/null || true)
    if [[ -z "$localhost_files" ]]; then
        print_success "No hardcoded localhost found in source code"
    else
        print_error "Found hardcoded localhost references:"
        echo "$localhost_files"
    fi
    
    # Check for IP addresses
    print_info "Checking for hardcoded IP addresses..."
    local ip_files=$(grep -r "103\.117\.56\.159\|202\.74\.74\.171\|127\.0\.0\.1" fe_astro/src/ be_astro/src/ 2>/dev/null || true)
    if [[ -z "$ip_files" ]]; then
        print_success "No hardcoded IP addresses found"
    else
        print_error "Found hardcoded IP addresses:"
        echo "$ip_files"
    fi
    
    # Check for old domains
    print_info "Checking for old domain references..."
    local old_domains=$(grep -r "astro-works\.com" fe_astro/src/ be_astro/src/ docs/ 2>/dev/null || true)
    if [[ -z "$old_domains" ]]; then
        print_success "No old domain references found"
    else
        print_error "Found old domain references:"
        echo "$old_domains"
    fi
    
    # Check for hardcoded ports
    print_info "Checking for hardcoded ports..."
    local port_files=$(grep -r ":8000\|:5432\|:6379" fe_astro/src/ be_astro/src/ --exclude="*.md" --exclude="*.toml" 2>/dev/null || true)
    if [[ -z "$port_files" ]]; then
        print_success "No hardcoded ports found in source code"
    else
        print_warning "Found potential hardcoded ports (may be acceptable):"
        echo "$port_files"
    fi
}

# Check environment variable usage
check_env_usage() {
    print_header "Checking Environment Variable Usage"
    
    # Frontend config usage
    print_info "Checking frontend config usage..."
    if grep -q "env.VITE_" fe_astro/src/lib/config/env.ts; then
        print_success "Frontend uses environment variables"
    else
        print_error "Frontend not using environment variables"
    fi
    
    # Backend config usage
    print_info "Checking backend config usage..."
    if grep -q "env::var" be_astro/src/config.rs; then
        print_success "Backend uses environment variables"
    else
        print_error "Backend not using environment variables"
    fi
    
    # Vite config usage
    print_info "Checking Vite config usage..."
    if grep -q "loadEnv\|env\." fe_astro/vite.config.ts; then
        print_success "Vite config uses environment variables"
    else
        print_error "Vite config not using environment variables"
    fi
}

# Check environment files completeness
check_env_files() {
    print_header "Checking Environment Files"
    
    # Required environment files
    local env_files=(".env.vps" "fe_astro/.env" "fe_astro/.env.production")
    
    for file in "${env_files[@]}"; do
        if [[ -f "$file" ]]; then
            print_success "Environment file exists: $file"
            
            # Check for required variables
            case "$file" in
                ".env.vps")
                    local required_vars=("API_DOMAIN" "CORS_ORIGIN" "JWT_SECRET" "DATABASE_URL" "REDIS_URL")
                    ;;
                "fe_astro/.env")
                    local required_vars=("VITE_API_URL" "VITE_DEV_API_TARGET" "VITE_WHATSAPP_PHONE_NUMBER")
                    ;;
                "fe_astro/.env.production")
                    local required_vars=("VITE_API_URL" "VITE_COMPANY_EMAIL")
                    ;;
            esac
            
            for var in "${required_vars[@]}"; do
                if grep -q "^$var=" "$file"; then
                    print_success "  $var is configured"
                else
                    print_error "  $var is missing from $file"
                fi
            done
        else
            print_error "Environment file missing: $file"
        fi
    done
}

# Check Docker configurations
check_docker_configs() {
    print_header "Checking Docker Configurations"
    
    # Check docker-compose files use env vars
    local compose_files=("docker-compose.vps-traefik.yml" "docker-compose.yml")
    
    for file in "${compose_files[@]}"; do
        if [[ -f "$file" ]]; then
            print_success "Docker compose file exists: $file"
            
            if grep -q "\${" "$file"; then
                print_success "  Uses environment variables"
            else
                print_warning "  May not use environment variables"
            fi
        else
            print_warning "Docker compose file missing: $file"
        fi
    done
}

# Check fallback values are appropriate
check_fallback_values() {
    print_header "Checking Fallback Values"
    
    # Check backend fallbacks
    print_info "Checking backend fallback values..."
    if grep -q "astrokabinet.id" be_astro/src/config.rs; then
        print_success "Backend fallbacks use production domains"
    else
        print_error "Backend fallbacks may use incorrect domains"
    fi
    
    # Check frontend fallbacks
    print_info "Checking frontend fallback values..."
    if grep -q "astrokabinet.id" fe_astro/src/lib/config/env.ts; then
        print_success "Frontend fallbacks use production domains"
    else
        print_error "Frontend fallbacks may use incorrect domains"
    fi
}

# Check consistency across environments
check_consistency() {
    print_header "Checking Environment Consistency"
    
    # Check email consistency
    print_info "Checking email consistency..."
    local vps_email=$(grep "COMPANY_EMAIL=" .env.vps 2>/dev/null | cut -d'=' -f2 || echo "")
    local fe_email=$(grep "VITE_COMPANY_EMAIL=" fe_astro/.env.production 2>/dev/null | cut -d'=' -f2 || echo "")
    
    if [[ "$vps_email" == "$fe_email" ]]; then
        print_success "Company email is consistent across environments"
    else
        print_error "Company email inconsistent: VPS($vps_email) vs Frontend($fe_email)"
    fi
    
    # Check API domain consistency
    print_info "Checking API domain consistency..."
    local vps_domain=$(grep "API_DOMAIN=" .env.vps 2>/dev/null | cut -d'=' -f2 || echo "")
    local fe_domain=$(grep "VITE_API_BASE_URL=" fe_astro/.env.production 2>/dev/null | cut -d'=' -f2 | sed 's|https://||' || echo "")
    
    if [[ "$vps_domain" == "$fe_domain" ]]; then
        print_success "API domain is consistent across environments"
    else
        print_error "API domain inconsistent: VPS($vps_domain) vs Frontend($fe_domain)"
    fi
}

# Generate comprehensive report
generate_report() {
    print_header "Environment Configuration Audit Report"
    
    echo -e "${CYAN}Summary:${NC}"
    echo -e "  ${GREEN}Passed: $CHECKS_PASSED${NC}"
    echo -e "  ${YELLOW}Warnings: $CHECKS_WARNING${NC}"
    echo -e "  ${RED}Failed: $CHECKS_FAILED${NC}"
    echo ""
    
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}✅ ALL ENVIRONMENT CHECKS PASSED!${NC}"
        echo -e "${GREEN}All routing and configuration properly use environment variables.${NC}"
        echo ""
        echo -e "${CYAN}Ready for deployment:${NC}"
        echo -e "  Development: All configs use env vars with appropriate fallbacks"
        echo -e "  Production: All configs use env vars with production values"
        echo -e "  Security: No hardcoded sensitive values found"
    else
        echo -e "${RED}❌ ENVIRONMENT AUDIT FAILED!${NC}"
        echo -e "${RED}Please fix the issues above before deployment.${NC}"
    fi
    
    if [[ $CHECKS_WARNING -gt 0 ]]; then
        echo ""
        echo -e "${YELLOW}⚠️  There are warnings that should be reviewed.${NC}"
    fi
    
    echo ""
    echo -e "${CYAN}Environment Files Status:${NC}"
    echo -e "  Development: fe_astro/.env"
    echo -e "  Production Frontend: fe_astro/.env.production"
    echo -e "  Production Backend: .env.vps"
    echo -e "  Vite Config: Uses environment variables"
    echo -e "  Backend Config: Uses environment variables with fallbacks"
}

# Main function
main() {
    print_header "Complete Environment Variables Audit"
    print_info "Verifying ALL configuration uses environment variables"
    
    check_all_hardcoded
    check_env_usage
    check_env_files
    check_docker_configs
    check_fallback_values
    check_consistency
    generate_report
    
    # Exit with appropriate code
    if [[ $CHECKS_FAILED -gt 0 ]]; then
        exit 1
    else
        exit 0
    fi
}

# Run main function
main "$@"
