#!/bin/bash

# =============================================================================
# Configuration Verification Script
# =============================================================================
# This script verifies all routing and configuration are properly sourced from environment variables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Counters
CHECKS_PASSED=0
CHECKS_WARNING=0
CHECKS_FAILED=0

# Functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((CHECKS_PASSED++))
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    ((CHECKS_WARNING++))
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((CHECKS_FAILED++))
}

# Check for hardcoded values
check_hardcoded_values() {
    print_header "Checking for Hardcoded Values"
    
    # Check frontend files for localhost references
    print_info "Checking frontend files for hardcoded localhost..."
    if grep -r "localhost:8000" fe_astro/src/ --exclude-dir=node_modules 2>/dev/null; then
        print_error "Found hardcoded localhost:8000 in frontend"
    else
        print_success "No hardcoded localhost:8000 found in frontend"
    fi
    
    # Check for old domain references
    print_info "Checking for old domain references..."
    if grep -r "astrokabinet.id" fe_astro/src/ be_astro/src/ 2>/dev/null; then
        print_error "Found old domain references (astrokabinet.id)"
    else
        print_success "No old domain references found"
    fi
    
    # Check for hardcoded IPs
    print_info "Checking for hardcoded IP addresses..."
    if grep -r "103\.117\.56\.159\|202\.74\.74\.171" fe_astro/src/ be_astro/src/ 2>/dev/null; then
        print_error "Found hardcoded IP addresses in source code"
    else
        print_success "No hardcoded IP addresses found in source code"
    fi
}

# Check environment variable usage
check_env_usage() {
    print_header "Checking Environment Variable Usage"
    
    # Check frontend config
    print_info "Checking frontend environment configuration..."
    if grep -q "env.VITE_API_URL" fe_astro/src/lib/config/env.ts; then
        print_success "Frontend uses environment variables for API URL"
    else
        print_error "Frontend not using environment variables for API URL"
    fi
    
    # Check backend config
    print_info "Checking backend environment configuration..."
    if grep -q "env::var.*HOST" be_astro/src/config.rs; then
        print_success "Backend uses environment variables for HOST"
    else
        print_error "Backend not using environment variables for HOST"
    fi
    
    if grep -q "env::var.*PORT" be_astro/src/config.rs; then
        print_success "Backend uses environment variables for PORT"
    else
        print_error "Backend not using environment variables for PORT"
    fi
}

# Check production environment files
check_production_env() {
    print_header "Checking Production Environment Files"
    
    # Check VPS environment
    if [[ -f ".env.vps" ]]; then
        print_success "VPS environment file exists"
        
        # Check required variables
        required_vars=("API_DOMAIN" "CORS_ORIGIN" "JWT_SECRET" "WHATSAPP_PHONE_NUMBER" "COMPANY_EMAIL")
        for var in "${required_vars[@]}"; do
            if grep -q "^$var=" .env.vps; then
                print_success "$var is configured in VPS environment"
            else
                print_error "$var is missing from VPS environment"
            fi
        done
        
        # Check for correct domain
        if grep -q "api.astrokabinet.id" .env.vps; then
            print_success "Correct API domain configured in VPS environment"
        else
            print_error "API domain not correctly configured in VPS environment"
        fi
        
        # Check for correct email
        if grep -q "<EMAIL>" .env.vps; then
            print_success "Correct company email configured"
        else
            print_error "Company email not correctly configured"
        fi
    else
        print_error "VPS environment file (.env.vps) not found"
    fi
    
    # Check frontend production environment
    if [[ -f "fe_astro/.env.production" ]]; then
        print_success "Frontend production environment file exists"
        
        if grep -q "api.astrokabinet.id" fe_astro/.env.production; then
            print_success "Correct API URL in frontend production environment"
        else
            print_error "API URL not correctly configured in frontend production environment"
        fi
    else
        print_error "Frontend production environment file not found"
    fi
}

# Check Wrangler configuration
check_wrangler_config() {
    print_header "Checking Wrangler Configuration"
    
    if [[ -f "fe_astro/wrangler.toml" ]]; then
        print_success "Wrangler configuration file exists"
        
        # Check production environment
        if grep -A 20 "\[env.production.vars\]" fe_astro/wrangler.toml | grep -q "furapi.astrokabinet.id"; then
            print_success "Correct API URL in Wrangler production config"
        else
            print_error "API URL not correctly configured in Wrangler production config"
        fi
        
        # Check for correct email
        if grep -A 20 "\[env.production.vars\]" fe_astro/wrangler.toml | grep -q "<EMAIL>"; then
            print_success "Correct company email in Wrangler config"
        else
            print_error "Company email not correctly configured in Wrangler config"
        fi
    else
        print_error "Wrangler configuration file not found"
    fi
}

# Check Docker configuration
check_docker_config() {
    print_header "Checking Docker Configuration"
    
    # Check Traefik Docker Compose
    if [[ -f "docker-compose.vps-traefik.yml" ]]; then
        print_success "Traefik Docker Compose file exists"
        
        if grep -q "\${API_DOMAIN}" docker-compose.vps-traefik.yml; then
            print_success "Docker Compose uses environment variables for domain"
        else
            print_error "Docker Compose not using environment variables for domain"
        fi
        
        if grep -q "\${CORS_ORIGIN}" docker-compose.vps-traefik.yml; then
            print_success "Docker Compose uses environment variables for CORS"
        else
            print_error "Docker Compose not using environment variables for CORS"
        fi
    else
        print_error "Traefik Docker Compose file not found"
    fi
}

# Check API endpoints configuration
check_api_endpoints() {
    print_header "Checking API Endpoints Configuration"
    
    # Check if API calls use config
    print_info "Checking API endpoint usage..."
    if grep -r "config.api.url" fe_astro/src/ 2>/dev/null; then
        print_success "Frontend API calls use configuration"
    else
        print_error "Frontend API calls not using configuration"
    fi
    
    # Check for direct URL usage
    if grep -r "https://api.astrokabinet.id" fe_astro/src/ --exclude="**/config/env.ts" 2>/dev/null; then
        print_warning "Found direct API URL usage (should use config)"
    else
        print_success "No direct API URL usage found"
    fi
}

# Check CORS configuration
check_cors_config() {
    print_header "Checking CORS Configuration"
    
    # Check backend CORS
    if grep -q "cors_origin" be_astro/src/config.rs; then
        print_success "Backend CORS uses environment variable"
    else
        print_error "Backend CORS not using environment variable"
    fi
    
    # Check VPS CORS includes frontend domain
    if grep -q "astrokabinet.id" .env.vps 2>/dev/null; then
        print_success "VPS CORS includes production domain"
    else
        print_error "VPS CORS missing production domain"
    fi
}

# Generate summary report
generate_summary() {
    print_header "Configuration Audit Summary"
    
    echo -e "${CYAN}Total Checks:${NC}"
    echo -e "  ${GREEN}Passed: $CHECKS_PASSED${NC}"
    echo -e "  ${YELLOW}Warnings: $CHECKS_WARNING${NC}"
    echo -e "  ${RED}Failed: $CHECKS_FAILED${NC}"
    echo ""
    
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}✅ Configuration audit passed!${NC}"
        echo -e "${GREEN}All routing and configuration properly use environment variables.${NC}"
        echo ""
        echo -e "${CYAN}Ready for deployment:${NC}"
        echo -e "  1. Backend: make deploy-vps"
        echo -e "  2. Frontend: make deploy-frontend"
        echo -e "  3. Full deployment: make deploy-full"
    else
        echo -e "${RED}❌ Configuration audit failed!${NC}"
        echo -e "${RED}Please fix the issues above before deployment.${NC}"
    fi
    
    if [[ $CHECKS_WARNING -gt 0 ]]; then
        echo ""
        echo -e "${YELLOW}⚠️  There are warnings that should be reviewed.${NC}"
    fi
}

# Main function
main() {
    print_header "Astro Works Configuration Audit"
    print_info "Verifying all routing and configuration use environment variables"
    
    check_hardcoded_values
    check_env_usage
    check_production_env
    check_wrangler_config
    check_docker_config
    check_api_endpoints
    check_cors_config
    generate_summary
    
    # Exit with appropriate code
    if [[ $CHECKS_FAILED -gt 0 ]]; then
        exit 1
    else
        exit 0
    fi
}

# Run main function
main "$@"
