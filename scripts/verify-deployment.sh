#!/bin/bash

# =============================================================================
# Deployment Verification Script
# =============================================================================
# This script verifies that the VPS deployment is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SECURE_ENV_FILE="$PROJECT_ROOT/.env.vps.secure"

# Load secure VPS configuration
if [[ -f "$SECURE_ENV_FILE" ]]; then
    source "$SECURE_ENV_FILE"
else
    echo -e "${RED}[ERROR]${NC} Secure environment file not found: $SECURE_ENV_FILE"
    exit 1
fi

# Functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test VPS connectivity
test_vps_connectivity() {
    print_header "Testing VPS Connectivity"
    
    print_info "Testing SSH connection..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$VPS_HOST" "echo 'SSH OK'" &> /dev/null; then
        print_success "SSH connection successful"
    else
        print_error "SSH connection failed"
        return 1
    fi
    
    print_info "Testing Docker on VPS..."
    if ssh "$VPS_HOST" "docker --version" &> /dev/null; then
        print_success "Docker is available on VPS"
    else
        print_error "Docker is not available on VPS"
        return 1
    fi
}

# Check service status
check_service_status() {
    print_header "Checking Service Status"
    
    print_info "Checking if deployment directory exists..."
    if ssh "$VPS_HOST" "test -d $VPS_DEPLOY_PATH"; then
        print_success "Deployment directory exists"
    else
        print_error "Deployment directory not found"
        return 1
    fi
    
    print_info "Checking Docker services..."
    local services_output
    services_output=$(ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml ps --format table" 2>/dev/null || echo "")
    
    if [[ -n "$services_output" ]]; then
        echo "$services_output"
        print_success "Docker services are running"
    else
        print_error "No Docker services found or not running"
        return 1
    fi
}

# Test API endpoints
test_api_endpoints() {
    print_header "Testing API Endpoints"
    
    local domain_url="https://api.astrokabinet.id"
    local internal_url="http://$VPS_IP:7998"

    print_info "Testing health endpoint (domain)..."
    if curl -f -s "$domain_url/health" > /dev/null; then
        print_success "Domain health endpoint is responding"
    else
        print_warning "Domain health endpoint is not responding - testing internal..."
        if curl -f -s "$internal_url/health" > /dev/null; then
            print_warning "Internal health endpoint works - DNS/SSL issue"
        else
            print_error "Both domain and internal health endpoints failed"
            return 1
        fi
    fi

    print_info "Testing API base endpoint..."
    if curl -f -s "$domain_url/api/v1" > /dev/null; then
        print_success "API base endpoint is responding"
    else
        print_warning "API base endpoint may not be configured"
    fi

    print_info "Testing categories endpoint..."
    local categories_response
    categories_response=$(curl -s "$domain_url/api/v1/categories" || echo "")
    if [[ -n "$categories_response" ]]; then
        print_success "Categories endpoint is responding"
    else
        print_warning "Categories endpoint may not be responding"
    fi

    print_info "Testing SSL certificate..."
    if curl -I -s "$domain_url/health" | grep -q "HTTP/2 200\|HTTP/1.1 200"; then
        print_success "SSL certificate is working"
    else
        print_warning "SSL certificate may have issues"
    fi
}

# Check database connectivity
check_database() {
    print_header "Checking Database Connectivity"
    
    print_info "Testing database connection..."
    local db_test
    db_test=$(ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml exec -T postgres psql -U astro_user -d astro_ecommerce -c 'SELECT 1;'" 2>/dev/null || echo "")
    
    if [[ "$db_test" == *"1"* ]]; then
        print_success "Database connection successful"
    else
        print_error "Database connection failed"
        return 1
    fi
    
    print_info "Checking database tables..."
    local tables
    tables=$(ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml exec -T postgres psql -U astro_user -d astro_ecommerce -c '\dt'" 2>/dev/null || echo "")
    
    if [[ -n "$tables" ]]; then
        print_success "Database tables exist"
    else
        print_warning "No database tables found - migrations may be needed"
    fi
}

# Check Redis connectivity
check_redis() {
    print_header "Checking Redis Connectivity"
    
    print_info "Testing Redis connection..."
    local redis_test
    redis_test=$(ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml exec -T redis redis-cli ping" 2>/dev/null || echo "")
    
    if [[ "$redis_test" == "PONG" ]]; then
        print_success "Redis connection successful"
    else
        print_error "Redis connection failed"
        return 1
    fi
}

# Check logs for errors
check_logs() {
    print_header "Checking Application Logs"
    
    print_info "Checking backend logs for errors..."
    local error_logs
    error_logs=$(ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml logs backend --tail=50" 2>/dev/null | grep -i "error\|panic\|fatal" || echo "")
    
    if [[ -z "$error_logs" ]]; then
        print_success "No critical errors found in logs"
    else
        print_warning "Found potential errors in logs:"
        echo "$error_logs"
    fi
    
    print_info "Recent backend logs:"
    ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml logs backend --tail=10" 2>/dev/null || echo "Could not retrieve logs"
}

# Check system resources
check_system_resources() {
    print_header "Checking System Resources"
    
    print_info "Checking disk usage..."
    local disk_usage
    disk_usage=$(ssh "$VPS_HOST" "df -h /" | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [[ $disk_usage -lt 80 ]]; then
        print_success "Disk usage is acceptable ($disk_usage%)"
    else
        print_warning "Disk usage is high ($disk_usage%)"
    fi
    
    print_info "Checking memory usage..."
    local memory_info
    memory_info=$(ssh "$VPS_HOST" "free -h | grep Mem")
    echo "Memory: $memory_info"
    
    print_info "Checking Docker container resources..."
    ssh "$VPS_HOST" "cd $VPS_DEPLOY_PATH && docker compose -f docker-compose.vps-traefik.yml exec backend ps aux | head -5" 2>/dev/null || echo "Could not retrieve container processes"
}

# Performance test
performance_test() {
    print_header "Performance Test"
    
    print_info "Testing API response time..."
    local response_time
    response_time=$(curl -o /dev/null -s -w "%{time_total}" "https://api.astrokabinet.id/health" || echo "0")
    
    if (( $(echo "$response_time < 1.0" | bc -l) )); then
        print_success "API response time is good (${response_time}s)"
    else
        print_warning "API response time is slow (${response_time}s)"
    fi
}

# Generate verification report
generate_report() {
    print_header "Verification Report"
    
    echo -e "${CYAN}Deployment Verification Summary${NC}"
    echo "================================"
    echo "VPS: $VPS_HOST"
    echo "API URL: https://api.astrokabinet.id"
    echo "Deploy Path: $VPS_DEPLOY_PATH"
    echo "Date: $(date)"
    echo ""

    echo -e "${YELLOW}Quick Commands:${NC}"
    echo "Health Check: curl https://api.astrokabinet.id/health"
    echo "View Logs: make vps-logs"
    echo "Service Status: make vps-status"
    echo "Restart Services: make vps-restart"
    echo ""

    echo -e "${YELLOW}API Endpoints:${NC}"
    echo "Health: https://api.astrokabinet.id/health"
    echo "Categories: https://api.astrokabinet.id/api/v1/categories"
    echo "Products: https://api.astrokabinet.id/api/v1/products"
    echo "Config: https://api.astrokabinet.id/api/v1/config"
    echo ""

    echo -e "${YELLOW}Traefik Dashboard:${NC}"
    echo "Dashboard: https://traefik.api.astrokabinet.id"
    echo "Username: admin"
    echo "Password: (configured in Traefik labels)"
}

# Main verification function
main() {
    print_header "Astro Works VPS Deployment Verification"
    print_info "Verifying deployment on: $VPS_HOST"
    
    local exit_code=0
    
    test_vps_connectivity || exit_code=1
    check_service_status || exit_code=1
    test_api_endpoints || exit_code=1
    check_database || exit_code=1
    check_redis || exit_code=1
    check_logs
    check_system_resources
    performance_test
    generate_report
    
    if [[ $exit_code -eq 0 ]]; then
        print_success "All verification checks passed!"
    else
        print_error "Some verification checks failed. Please review the output above."
    fi
    
    return $exit_code
}

# Run main function
main "$@"
