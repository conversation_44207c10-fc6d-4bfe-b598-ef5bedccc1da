#!/bin/bash

# Setup Local Development Environment (No Docker)
# ===============================================

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🔧 Astro Works - Local Setup (No Docker)${NC}"
echo -e "${CYAN}=========================================${NC}"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
echo -e "${BLUE}[INFO]${NC} Checking dependencies..."

if ! command_exists psql; then
    echo -e "${YELLOW}[WARNING]${NC} PostgreSQL client not found. Please install PostgreSQL:"
    echo -e "  Ubuntu/Debian: sudo apt install postgresql postgresql-contrib"
    echo -e "  macOS: brew install postgresql"
fi

if ! command_exists redis-cli; then
    echo -e "${YELLOW}[WARNING]${NC} Redis client not found. Please install Redis:"
    echo -e "  Ubuntu/Debian: sudo apt install redis-server"
    echo -e "  macOS: brew install redis"
fi

if ! command_exists cargo; then
    echo -e "${RED}[ERROR]${NC} Rust/Cargo not found. Please install Rust first:"
    echo -e "  curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
    exit 1
fi

if command_exists bun; then
    PACKAGE_MANAGER="bun"
    echo -e "${GREEN}[INFO]${NC} Using bun for frontend"
elif command_exists npm; then
    PACKAGE_MANAGER="npm"
    echo -e "${YELLOW}[INFO]${NC} Using npm for frontend (bun not found)"
else
    echo -e "${RED}[ERROR]${NC} Neither bun nor npm found. Please install Node.js/bun"
    exit 1
fi

echo ""

# Setup environment file
echo -e "${BLUE}[INFO]${NC} Setting up environment file..."
if [ ! -f "be_astro/.env" ]; then
    cp .env.dev be_astro/.env
    echo -e "${GREEN}[SUCCESS]${NC} Environment file created"
else
    echo -e "${YELLOW}[INFO]${NC} Environment file already exists"
fi

# Install frontend dependencies
echo -e "${BLUE}[INFO]${NC} Installing frontend dependencies..."
cd fe_astro
if [ ! -d "node_modules" ]; then
    $PACKAGE_MANAGER install
    echo -e "${GREEN}[SUCCESS]${NC} Frontend dependencies installed"
else
    echo -e "${YELLOW}[INFO]${NC} Frontend dependencies already installed"
fi
cd ..

echo ""
echo -e "${GREEN}[SUCCESS]${NC} Local setup completed!"
echo ""

# Check database connection
echo -e "${BLUE}[INFO]${NC} Checking database connections..."

# Test PostgreSQL
if command_exists psql; then
    if psql -h localhost -p 5432 -U astro_user -d astro_ecommerce -c "SELECT 1;" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PostgreSQL connection successful${NC}"
    else
        echo -e "${YELLOW}⚠️  PostgreSQL not accessible. Please ensure it's running:${NC}"
        echo -e "   sudo systemctl start postgresql"
        echo -e "   sudo -u postgres createdb astro_ecommerce"
        echo -e "   sudo -u postgres createuser astro_user"
    fi
fi

# Test Redis
if command_exists redis-cli; then
    if redis-cli ping >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis connection successful${NC}"
    else
        echo -e "${YELLOW}⚠️  Redis not accessible. Please ensure it's running:${NC}"
        echo -e "   sudo systemctl start redis-server"
    fi
fi

echo ""
echo -e "${CYAN}🚀 Ready to start development servers:${NC}"
echo ""
echo -e "${YELLOW}Terminal 1 - Backend:${NC}"
echo -e "   cd be_astro"
echo -e "   PORT=7998 cargo run --bin be_astro"
echo ""
echo -e "${YELLOW}Terminal 2 - Frontend:${NC}"
echo -e "   cd fe_astro"
echo -e "   $PACKAGE_MANAGER run dev"
echo ""
echo -e "${CYAN}URLs after starting:${NC}"
echo -e "   🌐 Frontend: http://localhost:5173"
echo -e "   🔗 API: http://localhost:7998/api/v1"
echo -e "   ❤️  Health: http://localhost:7998/health"
echo ""
echo -e "${CYAN}Admin Access:${NC}"
echo -e "   📧 Email: <EMAIL>"
echo -e "   🔑 Password: admin123"
echo -e "   🌐 URL: http://localhost:5173/manajemen"
