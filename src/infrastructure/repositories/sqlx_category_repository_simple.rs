use crate::domain::entities::*;
use crate::domain::repositories::*;
use crate::domain::value_objects::*;
use async_trait::async_trait;
use sqlx::{PgPool, Row};
use uuid::Uuid;
use chrono::{DateTime, Utc};

pub struct SqlxCategoryRepository {
    pool: PgPool,
}

impl SqlxCategoryRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl CategoryRepository for SqlxCategoryRepository {
    async fn find_by_id(&self, id: &CategoryId) -> Result<Option<Category>, String> {
        let row = sqlx::query(
            "SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at, deleted_at FROM categories WHERE id = $1 AND is_active = true AND deleted_at IS NULL"
        )
        .bind(id.0)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let category = Category {
                id: CategoryId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                description: row.get::<Option<String>, _>("description"),
                parent_id: row.get::<Option<Uuid>, _>("parent_id").map(CategoryId::from_uuid),
                image_url: row.get::<Option<String>, _>("image_url"),
                is_active: row.get::<bool, _>("is_active"),
                sort_order: row.get::<i32, _>("sort_order"),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
                deleted_at: row.get::<Option<DateTime<Utc>>, _>("deleted_at"),
            };
            Ok(Some(category))
        } else {
            Ok(None)
        }
    }

    async fn find_by_id_including_deleted(&self, id: &CategoryId) -> Result<Option<Category>, String> {
        let row = sqlx::query(
            "SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at, deleted_at FROM categories WHERE id = $1"
        )
        .bind(id.0)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let category = Category {
                id: CategoryId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                description: row.get::<Option<String>, _>("description"),
                parent_id: row.get::<Option<Uuid>, _>("parent_id").map(CategoryId::from_uuid),
                image_url: row.get::<Option<String>, _>("image_url"),
                is_active: row.get::<bool, _>("is_active"),
                sort_order: row.get::<i32, _>("sort_order"),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
                deleted_at: row.get::<Option<DateTime<Utc>>, _>("deleted_at"),
            };
            Ok(Some(category))
        } else {
            Ok(None)
        }
    }

    async fn find_all(&self) -> Result<Vec<Category>, String> {
        let rows = sqlx::query(
            "SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at, deleted_at FROM categories WHERE is_active = true AND deleted_at IS NULL ORDER BY sort_order, name"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut categories = Vec::new();
        for row in rows {
            let category = Category {
                id: CategoryId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                description: row.get::<Option<String>, _>("description"),
                parent_id: row.get::<Option<Uuid>, _>("parent_id").map(CategoryId::from_uuid),
                image_url: row.get::<Option<String>, _>("image_url"),
                is_active: row.get::<bool, _>("is_active"),
                sort_order: row.get::<i32, _>("sort_order"),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
                deleted_at: row.get::<Option<DateTime<Utc>>, _>("deleted_at"),
            };
            categories.push(category);
        }
        Ok(categories)
    }

    async fn find_root_categories(&self) -> Result<Vec<Category>, String> {
        let rows = sqlx::query(
            "SELECT id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at, deleted_at FROM categories WHERE parent_id IS NULL AND is_active = true AND deleted_at IS NULL ORDER BY sort_order, name"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut categories = Vec::new();
        for row in rows {
            let category = Category {
                id: CategoryId::from_uuid(row.get::<Uuid, _>("id")),
                name: row.get::<String, _>("name"),
                description: row.get::<Option<String>, _>("description"),
                parent_id: row.get::<Option<Uuid>, _>("parent_id").map(CategoryId::from_uuid),
                image_url: row.get::<Option<String>, _>("image_url"),
                is_active: row.get::<bool, _>("is_active"),
                sort_order: row.get::<i32, _>("sort_order"),
                created_at: row.get::<DateTime<Utc>, _>("created_at"),
                updated_at: row.get::<DateTime<Utc>, _>("updated_at"),
                deleted_at: row.get::<Option<DateTime<Utc>>, _>("deleted_at"),
            };
            categories.push(category);
        }
        Ok(categories)
    }

    async fn find_children(&self, _parent_id: &CategoryId) -> Result<Vec<Category>, String> {
        Ok(vec![])
    }

    async fn find_by_parent(&self, _parent_id: Option<&CategoryId>) -> Result<Vec<Category>, String> {
        Ok(vec![])
    }

    async fn count(&self) -> Result<u64, String> {
        let count: (i64,) = sqlx::query_as("SELECT COUNT(*) FROM categories WHERE is_active = true AND deleted_at IS NULL")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        Ok(count.0 as u64)
    }

    async fn save(&self, category: &Category) -> Result<(), String> {
        sqlx::query(
            r#"
            INSERT INTO categories (id, name, description, parent_id, image_url, is_active, sort_order, created_at, updated_at, deleted_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                description = EXCLUDED.description,
                parent_id = EXCLUDED.parent_id,
                image_url = EXCLUDED.image_url,
                is_active = EXCLUDED.is_active,
                sort_order = EXCLUDED.sort_order,
                updated_at = EXCLUDED.updated_at,
                deleted_at = EXCLUDED.deleted_at
            "#
        )
        .bind(category.id.0)
        .bind(&category.name)
        .bind(&category.description)
        .bind(category.parent_id.as_ref().map(|id| id.0))
        .bind(&category.image_url)
        .bind(category.is_active)
        .bind(category.sort_order)
        .bind(category.created_at)
        .bind(category.updated_at)
        .bind(category.deleted_at)
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    async fn delete(&self, id: &CategoryId) -> Result<(), String> {
        sqlx::query("UPDATE categories SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND deleted_at IS NULL")
            .bind(id.0)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        Ok(())
    }

    async fn soft_delete(&self, id: &CategoryId) -> Result<(), String> {
        sqlx::query("UPDATE categories SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND deleted_at IS NULL")
            .bind(id.0)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        Ok(())
    }

    async fn restore(&self, id: &CategoryId) -> Result<(), String> {
        sqlx::query("UPDATE categories SET deleted_at = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = $1")
            .bind(id.0)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        Ok(())
    }

    async fn get_category_path(&self, _id: &CategoryId) -> Result<Vec<Category>, String> {
        Ok(vec![])
    }

    async fn has_children(&self, _id: &CategoryId) -> Result<bool, String> {
        Ok(false)
    }

    async fn get_category_tree(&self) -> Result<Vec<CategoryWithChildren>, String> {
        Ok(vec![])
    }
}
