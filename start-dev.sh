#!/bin/bash

# Astro Works Development Environment Starter
# ==========================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🚀 Starting Astro Works Development Environment${NC}"
echo -e "${CYAN}===============================================${NC}"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
echo -e "${BLUE}[INFO]${NC} Checking dependencies..."

if ! command_exists docker; then
    echo -e "${RED}[ERROR]${NC} Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command_exists docker-compose; then
    echo -e "${RED}[ERROR]${NC} Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

if ! command_exists cargo; then
    echo -e "${RED}[ERROR]${NC} Rust/Cargo is not installed. Please install Rust first."
    exit 1
fi

if ! command_exists bun; then
    echo -e "${YELLOW}[WARNING]${NC} Bun is not installed. Falling back to npm."
    PACKAGE_MANAGER="npm"
else
    PACKAGE_MANAGER="bun"
fi

echo -e "${GREEN}[SUCCESS]${NC} All dependencies are available"
echo ""

# Function to start PostgreSQL
start_postgres() {
    echo -e "${BLUE}[INFO]${NC} Starting PostgreSQL..."

    # Remove existing container if it exists
    docker rm -f astro_postgres_dev 2>/dev/null || true

    # Start PostgreSQL
    docker run -d \
        --name astro_postgres_dev \
        -e POSTGRES_DB=astro_ecommerce \
        -e POSTGRES_USER=astro_user \
        -e POSTGRES_PASSWORD=astro_password \
        -p 5432:5432 \
        postgres:16-alpine

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}[SUCCESS]${NC} PostgreSQL container started"
    else
        echo -e "${RED}[ERROR]${NC} Failed to start PostgreSQL"
        return 1
    fi
}

# Function to start Redis
start_redis() {
    echo -e "${BLUE}[INFO]${NC} Starting Redis..."

    # Remove existing container if it exists
    docker rm -f astro_redis_dev 2>/dev/null || true

    # Start Redis
    docker run -d \
        --name astro_redis_dev \
        -p 6379:6379 \
        redis:7-alpine redis-server --appendonly yes

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}[SUCCESS]${NC} Redis container started"
    else
        echo -e "${RED}[ERROR]${NC} Failed to start Redis"
        return 1
    fi
}

# Start databases
start_postgres || exit 1
start_redis || exit 1

# Wait for PostgreSQL
echo -e "${BLUE}[INFO]${NC} Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if docker exec astro_postgres_dev pg_isready -U astro_user -d astro_ecommerce >/dev/null 2>&1; then
        echo -e "${GREEN}[SUCCESS]${NC} PostgreSQL is ready!"
        break
    fi
    echo -e "${YELLOW}[INFO]${NC} Waiting for PostgreSQL... ($i/30)"
    sleep 2
done

# Check if PostgreSQL is ready
if ! docker exec astro_postgres_dev pg_isready -U astro_user -d astro_ecommerce >/dev/null 2>&1; then
    echo -e "${RED}[ERROR]${NC} PostgreSQL failed to start properly"
    exit 1
fi

echo -e "${GREEN}[SUCCESS]${NC} Databases are ready!"
echo ""

# Install frontend dependencies if needed
if [ ! -d "fe_astro/node_modules" ]; then
    echo -e "${BLUE}[INFO]${NC} Installing frontend dependencies..."
    cd fe_astro && $PACKAGE_MANAGER install && cd ..
fi

echo -e "${CYAN}Services Status:${NC}"
echo -e "  ✅ PostgreSQL: localhost:5432"
echo -e "  ✅ Redis: localhost:6379"
echo ""

echo -e "${CYAN}Ready to start application servers:${NC}"
echo ""
echo -e "${PURPLE}Backend (API on port 7998):${NC}"
echo -e "  cd be_astro && PORT=7998 cargo run --bin be_astro"
echo ""
echo -e "${PURPLE}Frontend (Vite with $PACKAGE_MANAGER):${NC}"
echo -e "  cd fe_astro && $PACKAGE_MANAGER run dev"
echo ""

echo -e "${CYAN}Or use these shortcuts:${NC}"
echo -e "  make start-backend    # Start backend on port 7998"
echo -e "  make start-frontend   # Start frontend with bun"
echo -e "  make dev-local        # Start both automatically"
echo ""

echo -e "${GREEN}[SUCCESS]${NC} Development environment is ready!"
echo -e "${YELLOW}[TIP]${NC} Open two terminal windows to run backend and frontend simultaneously"
