#!/bin/bash

# Script to update product images to WebP format via API
API_BASE="http://localhost:7998/api/v1"

echo "Updating product images to WebP format..."

# Function to update product image
update_product_image() {
    local product_id=$1
    local image_url=$2
    
    echo "Updating product $product_id with image: $image_url"
    
    curl -X PATCH "$API_BASE/products/$product_id" \
        -H "Content-Type: application/json" \
        -d "{\"image_url\": \"$image_url\"}" \
        -s -o /dev/null -w "HTTP %{http_code}\n"
}

# Get all products and update their images
echo "Fetching products..."
products=$(curl -s "$API_BASE/products")

# Update specific products with known WebP images
echo "Updating products with WebP images..."

# Fantasy TV Cabinet
update_product_image "a93dfa6d-abda-4956-8b25-e98d0311f9f2" "/static/uploads/products/4x5_fantasy-tv-cabinet_1.webp"

# Modern Kitchen Set  
update_product_image "9c9b768f-bb25-4238-ab4d-ddb4b5008cd6" "/static/uploads/products/4x5_modern-kitchen-set_1.webp"

# Kitchen Island Premium
update_product_image "57156a29-d682-47d1-afee-60372955014a" "/static/uploads/products/4x5_kitchen-island-premium_1.webp"

# Pantry Cabinet Large
update_product_image "97028b1e-f4a8-4c46-af8d-18e4dcb23beb" "/static/uploads/products/4x5_pantry-cabinet-large_1.webp"

# Accessories
update_product_image "960095e7-a77d-4d25-9643-3ec9a216b230" "/static/images/additional-items/soft-close-hinges.webp"
update_product_image "91dc35de-e33a-4782-9424-271d8978270f" "/static/images/additional-items/pull-out-basket.webp"
update_product_image "769e7f57-79cf-435e-afc8-2fb30951b584" "/static/images/additional-items/glass-door.webp"
update_product_image "600a1ba8-05aa-4b64-8a5a-924a406aad42" "/static/images/additional-items/cabinet-lighting.webp"

# LED Gola
update_product_image "d24ba90b-6f79-48b3-b22f-517f04c2f86c" "/static/uploads/products/4x5_led-gola_1.webp"

# Tandem Box
update_product_image "bdc89aba-3516-494f-9e59-9ae4c4e63e3b" "/static/uploads/products/4x5_tandem-box_1.webp"

# Testing Produk
update_product_image "f9cc24e3-24f9-4927-add1-a0d7b87ccdc9" "/static/uploads/products/4x5_testing-produk_1_3e5badb9.webp"

# Rak TV Final Test
update_product_image "c962d1ea-aa6e-4f62-8021-70db434cd31c" "/static/uploads/products/4x5_rak-tv-baru_1.webp"

echo "Product image updates completed!"
echo "Verifying updates..."

# Verify updates
curl -s "$API_BASE/products" | jq '.[] | {name, slug, image_url}' | head -20
